#########################
# GENERAL
#########################

APP_ENV=local
APP_DEBUG=true
APP_NAME=apptree
APP_URL=http://127.0.0.1:8080
APP_KEY=

#########################
# DOCKER
#########################

DOCKER_NGINX_VERSION=1.27
DOCKER_PHP_VERSION=8.4
DOCKER_COMPOSER_VERSION=2.8
DOCKER_NODE_VERSION=22.4
DOCKER_REDIS_VERSION=7.2
DOCKER_MYSQL_VERSION=8.0
DOCKER_APP_PORT=8080
DOCKER_DB_PORT=3306 # Should match the port of the db service (APPTREE_DOCKER_DB_PORT)
DOCKER_DB_NETWORK=apptree-db-network # Should match the network name of the db service (APPTREE_DOCKER_DB_NETWORK)
DOCKER_DB_VOLUME=apptree-db-data # Should match the volume name of the db service (APPTREE_DOCKER_DB_VOLUME)
DOCKER_REDIS_PORT=6379 # Should match the port of the redis service (APPTREE_DOCKER_REDIS_PORT)
DOCKER_REDIS_NETWORK=apptree-redis-network # Should match the network name of the redis service (APPTREE_DOCKER_REDIS_NETWORK)
DOCKER_REDIS_VOLUME=apptree-redis-data # Should match the volume name of the redis service (APPTREE_DOCKER_REDIS_VOLUME)

#########################
# PATHS
#########################

WKHTMLTOPDF=/home/<USER>/webApps/workProjects/apptree2/vendor/bin/wkhtmltopdf-amd64
WKHTMLTOIMAGE=/home/<USER>/webApps/workProjects/apptree2/vendor/bin/wkhtmltoimage-amd64
# Font path is used by dompdf and must point to the asset inside the docker container
FONT_PATH=/var/www/html/public/fonts/

#########################
# DATABASES
#########################

DB_HOST=apptree-db
DB_HOST_SOX="${DB_HOST}"
DB_PORT=3306
DB_ROOT_PASSWORD=
DB_USERNAME=
DB_PASSWORD=

DB_CONNECTION_MAIN=mysql_main
DB_CONNECTION_PHONEBOOK=mysql_phonebook
DB_CONNECTION_PERSONNEL=mysql_personnel
DB_CONNECTION_OPENDATA=mysql_opendata
DB_CONNECTION_CONTRACTUALS=mysql_contractuals
DB_CONNECTION_CONSERVATIONS=mysql_conservations
DB_CONNECTION_REGISTRY=mysql_registry
DB_CONNECTION_EDUCATIONAL=mysql_educational
DB_CONNECTION_PUBLIC_CONTRACTUALS=mysql_public_contractuals
DB_CONNECTION_SUMMER_CAMPS=mysql_summer_camps
DB_CONNECTION_ASSETS=mysql_assets

DB_DATABASE_MAIN=laravel
DB_DATABASE_PHONEBOOK=phonebook
DB_DATABASE_PERSONNEL=personnel
DB_DATABASE_OPENDATA=opendata
DB_DATABASE_CONTRACTUALS=contractuals
DB_DATABASE_CONSERVATIONS=conservations
DB_DATABASE_REGISTRY=registry
DB_DATABASE_EDUCATIONAL=educational
DB_DATABASE_PUBLIC_CONTRACTUALS=public_contractuals
DB_DATABASE_SUMMER_CAMPS=summer_camps
DB_DATABASE_ASSETS=assets

#########################
# DBSYNC
#########################
DBSYNC_REMOTE_HOST=***********
DBSYNC_REMOTE_PORT=3306
DBSYNC_REMOTE_USER=
DBSYNC_REMOTE_PASSWORD=
DBSYNC_LOCAL_HOST=${DB_HOST}
DBSYNC_LOCAL_PORT=${DB_PORT}
DBSYNC_LOCAL_USER=${DB_USERNAME}
DBSYNC_LOCAL_PASSWORD=${DB_PASSWORD}

#########################
# LDAP SERVER
#########################

LDAP_LOGGING=true
LDAP_CONNECTION=default
LDAP_HOST=dc01.culture.gr
LDAP_USERNAME=
LDAP_PASSWORD=
LDAP_PORT=389
LDAP_BASE_DN="dc=culture,dc=gr"
LDAP_TIMEOUT=10
LDAP_SSL=false
LDAP_TLS=false

########################
# CACHE DRIVER
########################

CACHE_DRIVER=file
SESSION_DRIVER=file
QUEUE_DRIVER=redis

########################
# REDIS DRIVER
########################

REDIS_HOST=apptree-redis
REDIS_PASSWORD=admin
REDIS_PORT=6379
REDIS_DB=0

########################
# MAIL SERVER
########################

MAIL_MAILER=smtp
MAIL_HOST=***********
MAIL_PORT=25
MAIL_USERNAME=dhd.dev
MAIL_PASSWORD=
MAIL_ENCRYPTION=false
MAIL_FROM_ADDRESS='<EMAIL>'
MAIL_FROM_NAME="${APP_NAME}"

########################
# LOGGER
########################

ACTIVITY_LOGGER_ENABLED=true

########################
# LARAVEL PASSPORT
########################

PASSPORT_PERSONAL_ACCESS_CLIENT_ID=
PASSPORT_PERSONAL_ACCESS_CLIENT_SECRET=

########################
# MISC
########################

CHRISTMAS_MODE=false
AUTH_GUARD=web # or web-fallback

#########################
# BUGSNAG
#########################

BUGSNAG_API_KEY=
BUGSNAG_LOGGER_LEVEL=error
BUGSNAG_NOTIFY_RELEASE_STAGES=production

#########################
# Public Contractuals Integration
#########################
PUBLIC_CONTRACTUALS_URL= # [IP]:[PORT]see https://laracasts.com/discuss/channels/laravel/guzzlehttp-exception-connectexception-curl-error-7-failed-to-connect-to-localhost-port-8087-connection-refused
PUBLIC_CONTRACTUALS_API_KEY=

#########################
# PUSHER
#########################

BROADCAST_DRIVER=pusher
PUSHER_APP_ID=local
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

LARAVEL_WEBSOCKETS_HOST=websocket
LARAVEL_WEBSOCKETS_PORT=6001
LARAVEL_WEBSOCKETS_SCHEME=http
LARAVEL_WEBSOCKETS_SSL_LOCAL_CERT=
LARAVEL_WEBSOCKETS_SSL_LOCAL_PK=

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"
MIX_LARAVEL_WEBSOCKETS_PORT="${LARAVEL_WEBSOCKETS_PORT}"
MIX_LARAVEL_WEBSOCKETS_FORCETLS=false
