includes:
    - vendor/larastan/larastan/extension.neon

parameters:

    paths:
        - app/

    level: 5

    ignoreErrors:
        - '#Access to an undefined property#'
        - '#PHPDoc type array of property#'     # To suppress You can fix 3rd party PHPDoc types with stub files
        - '#PHPDoc type array\|string\|null of property#'
        - '#Parameter \#1 \$callback of method Illuminate\\Database\\Eloquent\\Collection<.*>#'
        - '#Parameter \#1 \$callback of method Illuminate\\Support\\Collection<.*>#'

    excludePaths:
        - vendor/
        - app/Http/Middleware/GenerateMenus.php
        - app/Services/Shared/Elasticsearch/ElasticSearchEngine.php

#
#    checkMissingIterableValueType: false

