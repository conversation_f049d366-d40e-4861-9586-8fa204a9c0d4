<?php

namespace Tests\Feature\Contractuals;

use App\Models\Contractuals\Evaluation;
use Carbon\Carbon;
use Database\CustomFactories\Contractuals\ApplicationFactory;
use Database\CustomFactories\Contractuals\ContestFactory;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Passport\Passport;
use Tests\ContractualsTestCase;

class UserEvaluatesApplicationTest extends ContractualsTestCase
{
    use RefreshDatabase;

    protected $contest;

    public function setUp(): void
    {
        parent::setUp();

        $this->contest = ContestFactory::create([
            'positions' => [
                [
                    'amount' => 2,
                    'location' => 'Athens',
                    'specialization' => 'PE',
                    'requirements' => [
                        0 => [
                            [
                                'name' => 'High Degree A',
                                'requirement_type' => 'degree',
                            ],
                            [
                                'name' => 'Experience A',
                                'requirement_type' => 'experience',
                            ],
                        ],
                        1 => [
                            [
                                'name' => 'Low Degree A',
                                'requirement_type' => 'degree',
                            ],
                            [
                                'name' => 'ECDL A',
                                'requirement_type' => 'computerSkills',
                            ],
                        ],
                    ],
                ],
                [
                    'amount' => 3,
                    'location' => 'Thessaloniki',
                    'specialization' => 'PE',
                    'requirements' => [
                        0 => [
                            [
                                'name' => 'High Degree B',
                                'requirement_type' => 'degree',
                            ],
                            [
                                'name' => 'Experience B',
                                'requirement_type' => 'experience',
                            ],
                        ],
                    ],
                ],
            ],
        ]);
    }

    /** @test */
    public function an_auth_user_can_initialize_evaluations_if_the_application_is_locked_by_visiting_edit_form()
    {
        $application = ApplicationFactory::createForContest($this->contest, [
            'locked_at' => Carbon::now(),
            'positions' => [
                ['id' => $this->contest->positions->first()->id, 'order' => 1],
                ['id' => $this->contest->positions->last()->id, 'order' => 2],
            ],
            'degrees' => [
                [
                    'name' => 'BSc in archaeology',
                    'issuer' => 'University of Athens',
                ],
            ],
            'experiences' => [
                [
                    'name' => 'Archaeologist',
                    'issuer' => 'Company A',
                    'months' => 6,
                    'started_at' => '2017-01-01',
                    'ended_at' => '2017-06-30',
                ],
                [
                    'name' => 'Byzantinologist',
                    'issuer' => 'Company B',
                    'months' => 6,
                    'started_at' => '2017-07-01',
                    'ended_at' => '2017-31-12',
                ],
            ],
        ]);

        $this->assertCount(0, Evaluation::all());

        $response = $this->actingAs($this->user)
            ->get("/contractuals/applications/$application->id/evaluations/edit");

        $response->assertStatus(200)
            ->assertViewIs('contractuals.evaluation.edit')
            ->assertViewHasAll([
                'application' => $application->fresh()->load([
                    'contest',
                    'evaluations.qualification.qualifiable.media',
                    'positions.specialization.specializationType',
                    'positions.unit',
                    'positions.requirements.requirementType',
                ]),
            ]);
        // Total number of evaluations = # user selected positions * # user qualifications
        $this->assertCount(6, Evaluation::all());
    }

    /** @test */
    public function an_auth_user_cannot_initialize_evaluations_if_the_application_is_unlocked()
    {
        $application = ApplicationFactory::createForContest($this->contest, [
            'locked_at' => null,
            'positions' => [
                ['id' => $this->contest->positions->first()->id, 'order' => 1],
            ],
            'degrees' => [
                [
                    'name' => 'BSc in archaeology',
                    'issuer' => 'University of Athens',
                ],
            ],
            'experiences' => [
                [
                    'name' => 'Archaeologist',
                    'issuer' => 'New company',
                    'started_at' => '2017-01-01',
                    'ended_at' => '2017-06-30',
                ],
                [
                    'name' => 'Byzantinologist',
                    'issuer' => 'Company C',
                    'started_at' => '2017-07-01',
                    'ended_at' => '2017-31-12',
                ],
            ],
        ]);

        $this->assertCount(0, Evaluation::all());

        $response = $this->actingAs($this->user)
            ->get("/contractuals/applications/$application->id/evaluations/edit");

        $response->assertStatus(422);
        $this->assertCount(0, Evaluation::all());
    }

    /** @test */
    public function evaluations_are_initialized_when_a_user_tries_to_edit_evaluations_of_a_locked_application()
    {
        $application = ApplicationFactory::createForContest($this->contest, [
            'locked_at' => Carbon::now(),
            'positions' => [
                ['id' => $this->contest->positions->first()->id, 'order' => 1],
            ],
            'degrees' => [
                [
                    'name' => 'BSc in archaeology',
                    'issuer' => 'University of Athens',
                ],
            ],
            'experiences' => [
                [
                    'name' => 'Archaeologist',
                    'issuer' => 'New company',
                    'started_at' => '2017-01-01',
                    'ended_at' => '2017-06-30',
                ],
                [
                    'name' => 'Byzantinologist',
                    'issuer' => 'Company C',
                    'started_at' => '2017-07-01',
                    'ended_at' => '2017-31-12',
                ],
            ],
        ]);

        $this->assertCount(0, Evaluation::all());

        $response = $this->actingAs($this->user)
            ->get("/contractuals/applications/$application->id/evaluations/edit");

        $response->assertStatus(200)
            ->assertViewIs('contractuals.evaluation.edit')
            ->assertViewHasAll([
                'application' => $application->fresh()->load([
                    'contest',
                    'evaluations.qualification.qualifiable.media',
                    'positions.specialization.specializationType',
                    'positions.unit',
                    'positions.requirements.requirementType',
                ]),
            ]);
        $this->assertCount(3, Evaluation::all());
    }

    /** @test */
    public function evaluations_are_not_reinitialized_if_they_have_already_been_initialized()
    {
        $application = ApplicationFactory::createForContest($this->contest, [
            'locked_at' => Carbon::now(),
            'positions' => [
                ['id' => $this->contest->positions->first()->id, 'order' => 1],
            ],
            'degrees' => [
                [
                    'name' => 'BSc in archaeology',
                    'issuer' => 'University of Athens',
                ],
            ],
            'experiences' => [
                [
                    'name' => 'Archaeologist',
                    'issuer' => 'New company',
                    'started_at' => '2017-01-01',
                    'ended_at' => '2017-06-30',
                ],
                [
                    'name' => 'Byzantinologist',
                    'issuer' => 'Company C',
                    'started_at' => '2017-07-01',
                    'ended_at' => '2017-31-12',
                ],
            ],
        ]);

        // Manually initialize evaluations and edit them
        $application->initializeEvaluations();
        $this->assertCount(3, Evaluation::all());
        $this->assertEquals(0, $application->getAuxiliaryLevel($application->positions()->first()));
        Evaluation::where('id', 1)->update(['relevant' => 1]);

        $response = $this->actingAs($this->user)
            ->get("/contractuals/applications/$application->id/evaluations/edit");

        $response->assertStatus(200);
        $response->assertViewIs('contractuals.evaluation.edit');
        $response->assertViewHasAll([
            'application' => $application->fresh()->load([
                'contest',
                'evaluations.qualification.qualifiable.media',
                'positions.specialization.specializationType',
                'positions.unit',
                'positions.requirements.requirementType',
            ]),
        ]);
        $this->assertCount(3, Evaluation::all());
        Evaluation::where('id', 1)->update(['relevant' => 1]);
        $this->assertEquals(0, $application->fresh()->getAuxiliaryLevel($application->positions()->first()));
    }

    /** @test */
    public function an_auth_user_can_update_initial_evaluations_if_application_is_not_rated()
    {
        $application = ApplicationFactory::createForContest($this->contest, [
            'locked_at' => Carbon::now(),
            'rated_at' => null,
            'positions' => [
                ['id' => $this->contest->positions->first()->id, 'order' => 1],
            ],
            'degrees' => [
                [
                    'name' => 'BSc in archaeology',
                    'issuer' => 'University of Athens',
                ],
            ],
            'experiences' => [
                [
                    'name' => 'Archaeologist',
                    'issuer' => 'New company',
                    'started_at' => '2017-01-01',
                    'ended_at' => '2017-06-30',
                ],
                [
                    'name' => 'Byzantinologist',
                    'issuer' => 'Company C',
                    'started_at' => '2017-07-01',
                    'ended_at' => '2017-31-12',
                ],
            ],
        ]);
        $application->initializeEvaluations();
        $this->assertFalse($application->isRated());
        $this->assertTrue($application->hasEvaluations());
        $this->assertEquals(0, $application->getAuxiliaryLevel($application->positions()->first()));

        // we simulate the user request by changing some evaluations...
        $positionAuxiliaryLevel = 1;
        $userData = $application->evaluations->toArray();
        $userData[0]['relevant'] = true;
        $userData[1]['requirement_id'] = $this->contest->positions->first()->requirements->first()->id;
        $userData[0]['auxiliary_level'] = $positionAuxiliaryLevel;
        $userData[1]['auxiliary_level'] = $positionAuxiliaryLevel;

        $response = $this->actingAs($this->user)
            ->patch("/contractuals/applications/{$application->id}/evaluations", $userData);

        $response->assertStatus(200);
        $response->assertJson([
            'message' => 'Evaluations has been updated',
            'evaluations' => $userData,
        ]);
        $this->assertSame($positionAuxiliaryLevel, $application->fresh()->getAuxiliaryLevel($application->positions()->first()));
    }

    /** @test */
    public function an_auth_user_cannot_update_initial_evaluations_if_application_is_rated()
    {
        $application = ApplicationFactory::createForContest($this->contest, [
            'locked_at' => Carbon::now(),
            'rated_at' => Carbon::now(),
            'positions' => [
                ['id' => $this->contest->positions->first()->id, 'order' => 1],
            ],
            'degrees' => [
                [
                    'name' => 'BSc in archaeology',
                    'issuer' => 'University of Athens',
                ],
            ],
            'experiences' => [
                [
                    'name' => 'Archaeologist',
                    'issuer' => 'New company',
                    'started_at' => '2017-01-01',
                    'ended_at' => '2017-06-30',
                ],
                [
                    'name' => 'Byzantinologist',
                    'issuer' => 'Company C',
                    'started_at' => '2017-07-01',
                    'ended_at' => '2017-31-12',
                ],
            ],
        ]);
        $application->initializeEvaluations();
        $this->assertTrue($application->isRated());
        $this->assertTrue($application->hasEvaluations());

        // we simulate the user request by changing some evaluations...
        $userData = $application->evaluations->toArray();
        $userData[0]['relevant'] = 1;
        $userData[1]['requirement_id'] = $this->contest->positions->first()->requirements->first()->id;

        $response = $this->actingAs($this->user)
            ->patch("/contractuals/applications/{$application->id}/evaluations", $userData);

        $response->assertStatus(422);
        $this->assertEquals($response->exception->getMessage(), 'Evaluations cannot been updated because the application is finalized');
    }

    /** @test */
    public function an_auth_user_can_clear_evaluations_if_application_is_not_rated()
    {
        $application = ApplicationFactory::createForContest($this->contest, [
            'locked_at' => Carbon::now(),
            'rated_at' => null,
            'positions' => [
                ['id' => $this->contest->positions->first()->id, 'order' => 1],
            ],
            'degrees' => [
                [
                    'name' => 'BSc in archaeology',
                    'issuer' => 'University of Athens',
                ],
            ],
            'experiences' => [
                [
                    'name' => 'Archaeologist',
                    'issuer' => 'New company',
                    'started_at' => '2017-01-01',
                    'ended_at' => '2017-06-30',
                ],
                [
                    'name' => 'Byzantinologist',
                    'issuer' => 'Company C',
                    'started_at' => '2017-07-01',
                    'ended_at' => '2017-31-12',
                ],
            ],
        ]);
        $application->initializeEvaluations();
        $application->positions()->updateExistingPivot($application->positions()->first()->id, ['auxiliary_level' => 2]);
        $this->assertFalse($application->isRated());
        $this->assertTrue($application->hasEvaluations());
        $this->assertNotNull($application->fresh()->getAuxiliaryLevel($application->positions()->first()));

        Passport::actingAs($this->user);
        $response = $this->delete("/api/contractuals/applications/{$application->id}/evaluations");

        $response->assertStatus(200);
        $this->assertFalse($application->fresh()->hasEvaluations());
        $this->assertNull($application->fresh()->getAuxiliaryLevel($application->positions()->first()));
    }

    /** @test */
    public function an_auth_user_cannot_clear_evaluations_if_application_is_rated()
    {
        $application = ApplicationFactory::createForContest($this->contest, [
            'locked_at' => Carbon::now(),
            'rated_at' => Carbon::now(),
            'positions' => [
                ['id' => $this->contest->positions->first()->id, 'order' => 1],
            ],
            'degrees' => [
                [
                    'name' => 'BSc in archaeology',
                    'issuer' => 'University of Athens',
                ],
            ],
            'experiences' => [
                [
                    'name' => 'Archaeologist',
                    'issuer' => 'New company',
                    'started_at' => '2017-01-01',
                    'ended_at' => '2017-06-30',
                ],
                [
                    'name' => 'Byzantinologist',
                    'issuer' => 'Company C',
                    'started_at' => '2017-07-01',
                    'ended_at' => '2017-31-12',
                ],
            ],
        ]);
        $application->initializeEvaluations();
        $this->assertTrue($application->isRated());
        $this->assertTrue($application->hasEvaluations());

        Passport::actingAs($this->user);
        $response = $this->delete("/api/contractuals/applications/{$application->id}/evaluations");

        $response->assertStatus(422);
        $this->assertTrue($application->fresh()->hasEvaluations());
    }
}
