{
  "env": {
    "browser": true,
    "es2021": true,
    "node": true
  },
  "extends": [
    "airbnb-base",
    "eslint:recommended",
    "plugin:@typescript-eslint/recommended",
    "plugin:vue/recommended",
    //"plugin:vue-scoped-css/recommended",
    "plugin:import/errors",
    "plugin:import/warnings"
  ],
  "parser": "vue-eslint-parser",
  "parserOptions": {
    "parser": "@typescript-eslint/parser",
    "ecmaVersion": "latest",
    "vueFeatures": {
      "filter": true,
      "interpolationAsNonHTML": false
    }
  },
  "plugins": [
    "simple-import-sort"
  ],
  "settings": {
    "import/resolver": {
      "typescript": {}, // this loads <rootdir>/tsconfig.json to eslint
//      "alias": [
//        ["@", "./resources/js"]
//      ],
      "exclude": [
        "node_modules"
      ]
    }
  },
  "rules": {
    // The core 'no-unused-vars' rules (in the eslint:recommeded ruleset)
    // does not work with type definitions
    "no-unused-vars": "off",
    "func-call-spacing": "off", // Fix for 'defineEmits'
    "no-spaced-func": "off", // Fix for 'defineEmits'
    "max-len": ["warn", {"code":  120}],
    "simple-import-sort/imports": "error",
    "simple-import-sort/exports": "error",
    "import/no-extraneous-dependencies": ["error", {"devDependencies": true}],
    "import/extensions": [
      "error",
      "ignorePackages",
      {
        "js": "never",
        "jsx": "never",
        "ts": "never",
        "tsx": "never"
      }
    ]
  }
}
