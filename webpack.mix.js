const mix = require('laravel-mix');
const path = require('path');

// Required in case of multi-stage dockerized apps
// mix will not set it if artisan file doesn't exist.
// https://github.com/JeffreyWay/laravel-mix/issues/1326#issuecomment-553577275
mix.setPublicPath('public');

mix.alias({
  '@': path.join(__dirname, 'resources/js'),
});

// Configure webpack watch options to prevent infinite loops with Tailwind CSS v4
// https://github.com/tailwindlabs/tailwindcss/issues/16151
mix.webpackConfig({
  watchOptions: {
    ignored: [
      '**/public/**',
      '**/mix-manifest.json',
    ],
  },
});
/*
 |--------------------------------------------------------------------------
 | Apptree layout
 |--------------------------------------------------------------------------
 | Build the required js and css for the apptree
 |
 */
// build styles
mix.less('resources/less/apptree.less', 'public/css/apptree.css')
  .copy('resources/css/pdf.css', 'public/css/pdf.css')
  .copy('resources/css/snappy.css', 'public/css/snappy.css');

mix.css('resources/css/myStyles.css', 'public/css/myStyles.css');

// Process Tailwind CSS with specific output path to avoid watcher loops
mix.postCss('resources/css/tailwind.css', 'public/css/tailwind.css', [
  require('@tailwindcss/postcss'),
]);

// build scripts
mix.combine([
  'resources/admin-lte/dist/js/smoothscroll.js',
  'resources/admin-lte/plugins/iCheck/icheck.min.js',
  'resources/admin-lte/plugins/slimScroll/jquery.slimscroll.min.js',
  'resources/admin-lte/plugins/sweetalert/sweetalert.min.js',
  'resources/admin-lte/plugins/select2/select2.full.min.js',
  'resources/admin-lte/plugins/select2/i18n/el.js',
  'resources/admin-lte/plugins/datatables/datatables.min.js',
  'resources/admin-lte/plugins/moment/moment.min.js',
  'resources/admin-lte/plugins/datatables/plugins/sorting/datetime-moment.js',
  'resources/admin-lte/plugins/toggles/toggles.min.js',
  'resources/admin-lte/plugins/bootstrap-checkbox/dist/js/bootstrap-checkbox.js',
  'resources/admin-lte/plugins/bootstrap-checkbox/dist/js/i18n/el.js',
  'resources/admin-lte/dist/js/admin-lte.js',
  'resources/admin-lte/plugins/datepicker/bootstrap-datepicker.js',
  'resources/admin-lte/plugins/datepicker/locales/bootstrap-datepicker.el.js',
  'resources/admin-lte/plugins/daterangepicker/daterangepicker.js',
  'resources/admin-lte/plugins/daterangepicker/moment.min.js',
  'resources/admin-lte/plugins/bootstrap-wysihtml5/bootstrap3-wysihtml5.all.js',
  'resources/js/myScripts.js',
], 'public/js/apptree.js');

// mix.copy('resources/admin-lte/plugins/datatables/pdfmake-0.1.18/build/pdfmake.min.js.map', 'public/js/')

// copy required fonts
mix.copy('resources/fonts/**/*.*', 'public/fonts');

// copy required plugins
mix.copy('resources/admin-lte/dist/js/jquery-2.2.3.min.js', 'public/js/jquery-2.2.3.min.js')
  .copy('resources/admin-lte/dist/js/bootstrap-3.3.4.min.js', 'public/js/bootstrap-3.3.4.min.js')
  .copy('resources/admin-lte/plugins/datatables/datatables.greek.lang', 'public/js')
  .copy('resources/js/shared/geojson/okxegeodata_greece4326.min.js', 'public/js/okxegeodata_greece4326.min.js')
  .copy('resources/bin/*.*', 'public/bin');

/*
 |--------------------------------------------------------------------------
 | Laravel Passport
 |--------------------------------------------------------------------------
 | Build the Laravel Passport components
 |
 */
mix.js('resources/js/passport/passport.js', 'public/js/passport/passport.js').vue();

/*
 |--------------------------------------------------------------------------
 | Phonebook
 |--------------------------------------------------------------------------
 | Build the required assets for the phonebook app
 |
 */
mix.js('resources/js/phonebook/index.js', 'public/js/phonebook/index.js').vue();

/*
 |--------------------------------------------------------------------------
 | Opendata
 |--------------------------------------------------------------------------
 | Build the required assets for the opendata app
 |
 */
mix.combine(['resources/js/opendata/dataset_form.js'], 'public/js/opendata/dataset_form.js')
  .combine(['resources/js/opendata/dataset_search.js'], 'public/js/opendata/dataset_search.js')
  .combine(['resources/js/opendata/dataset_table.js'], 'public/js/opendata/dataset_table.js');
mix.js('resources/js/opendata/index.js', 'public/js/opendata/index.js').vue();
/*
 |--------------------------------------------------------------------------
 | Personnel
 |--------------------------------------------------------------------------
 |
 */
mix.js('resources/js/personnel/index.js', 'public/js/personnel/index.js').vue();

/*
 |--------------------------------------------------------------------------
 | Contractuals
 |--------------------------------------------------------------------------
 | Build the required assets for the Contractuals app
 |
 */
mix.js('resources/js/contractuals/contractuals.js', 'public/js/contractuals/contractuals.js')
  .vue()
  .sourceMaps(false, 'inline-source-map');
mix.copy('resources/images/contractuals/*.svg', 'public/images/contractuals');

/*
 |--------------------------------------------------------------------------
 | Conservations
 |--------------------------------------------------------------------------
 | Build the required assets for the Contractuals app
 |
 */
mix.js('resources/js/conservations/conservator.js', 'public/js/conservations/conservator.js').vue();

/*
 |--------------------------------------------------------------------------
 | Registry
 |--------------------------------------------------------------------------
 | Build the required assets for the Registry app
 |
 */
mix.js('resources/js/registry/registry.js', 'public/js/registry/registry.js').vue();

/*
 |--------------------------------------------------------------------------
 | Educational
 |--------------------------------------------------------------------------
 | Build the required assets for the Educational app
 |
 */
mix.ts('resources/js/educational/educational.ts', 'public/js/educational').vue();

/*
 |--------------------------------------------------------------------------
 | Role Management
 |--------------------------------------------------------------------------
 | Build the required assets for the Educational app
 |
 */
mix.js('resources/js/roleManagement/roleManagement.js', 'public/js/roleManagement/roleManagement.js').vue();

/*
 |--------------------------------------------------------------------------
 | Summer Camps
 |--------------------------------------------------------------------------
 | Build the required assets for the Contractuals app
 |
 */
mix.js('resources/js/summerCamps/summerCamps.js', 'public/js/summerCamps/summerCamps.js').vue();

/*
 |--------------------------------------------------------------------------
 | Assets
 |--------------------------------------------------------------------------
 | Build the required assets for the Assets app
 |
 */
mix.ts('resources/js/assets/assets.ts', 'public/js/assets').vue();

/*
 |--------------------------------------------------------------------------
 | Admin
 |--------------------------------------------------------------------------
 | Admin dashboard
 |
 */
mix.ts('resources/js/admin/admin.ts', 'public/js/admin').vue();

/*
 |--------------------------------------------------------------------------
 | Units
 |--------------------------------------------------------------------------
 | Build the required assets for the Educational app
 |
 */
mix.js('resources/js/admin/unit/index.js', 'public/js/admin/unit/index.js').vue();

/*
 |--------------------------------------------------------------------------
 | Source Maps, versioning
 |--------------------------------------------------------------------------
 | Apply versioning in production and source maps in development
 |
 */

if (mix.inProduction()) {
  mix.version();
}
