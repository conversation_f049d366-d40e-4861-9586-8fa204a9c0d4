<?php

use App\Http\Controllers\Contractuals\ApplicationController;
use App\Http\Controllers\Contractuals\ApplicationRatingController;
use App\Http\Controllers\Contractuals\BackOfficeStatisticsController;
use App\Http\Controllers\Contractuals\CalculationEmployableDiffController;
use App\Http\Controllers\Contractuals\ContestController;
use App\Http\Controllers\Contractuals\RankedPositionExportController;
use App\Http\Controllers\Contractuals\FrontOfficeApplicationController;
use App\Http\Controllers\Contractuals\FrontOfficeAttachmentController;
use App\Http\Controllers\Contractuals\FrontOfficeStatisticsController;
use App\Http\Controllers\Contractuals\ImportPositionsController;
use App\Http\Controllers\Contractuals\InvalidationDescriptionController;
use App\Http\Controllers\Contractuals\LanguageController;
use App\Http\Controllers\Contractuals\PositionController;
use App\Http\Controllers\Contractuals\RankedPositionAcceptedExportController;
use App\Http\Controllers\Contractuals\RankedPositionController;
use App\Http\Controllers\Contractuals\RankedPositionRejectedExportController;
use App\Http\Controllers\Contractuals\RequirementController;
use App\Http\Controllers\Contractuals\SpecializationController;
use App\Http\Controllers\Contractuals\VacantPositionController;
use App\Services\Shared\VideoStreamer;

Route::get('/', fn () => view('contractuals.home'))->name('home');

Route::get('contests/{contest}/import-applications', function (App\Models\Contractuals\Contest $contest) {
    $exitCode = Artisan::call('contractuals:application-import', ['contest' => $contest->id]);

    abort_unless($exitCode === 0, 422, 'Applications could not be imported');

    return redirect()->route('contractuals.contests.show', ['contest' => $contest->id]);
})->name('import-applications');

Route::get('contests/{contest}/clear-deleted-applications', function (App\Models\Contractuals\Contest $contest) {
    $exitCode = Artisan::call('contractuals:application-clear-deleted', ['contest' => $contest->id]);

    abort_unless($exitCode === 0, 422, 'Applications could not be deleted');

    return redirect()->route('contractuals.contests.show', ['contest' => $contest->id]);
})->name('clear-deleted-applications');

Route::view('instructional-videos', 'contractuals.instructional-videos')->name('instructional-videos');

Route::get('instructional-videos/{stream}', function ($stream) {
    $v = public_path('docs/'.$stream.'.mp4');
    $tmp = new VideoStreamer($v);
    $tmp->start();
})->name('instructional-videos.stream');

/*---------------------------------------------------------------------------
| Contest
|--------------------------------------------------------------------------*/

Route::resource('contests', ContestController::class)->except('destroy');
Route::put('contests/{contest}/restore', [ContestController::class, 'restore'])->name('contests.restore');

/*---------------------------------------------------------------------------
| Position
|--------------------------------------------------------------------------*/

Route::resource('contests/{contest}/positions', PositionController::class);
Route::put('contests/{contest}/positions/{position}/restore', [PositionController::class, 'restore'])->name('positions.restore');

/*---------------------------------------------------------------------------
| Import Position
|--------------------------------------------------------------------------*/
// TODO - Refactor these routes
Route::get('contests/{contest}/import-positions', [ImportPositionsController::class, 'create'])->name('import-positions.create');
Route::post('contests/{contest}/import-positions', [ImportPositionsController::class, 'store'])->name('import-positions.store');

/*---------------------------------------------------------------------------
| Ranked Position
|--------------------------------------------------------------------------*/

Route::get('contests/{contest}/ranked-positions', [RankedPositionController::class, 'index'])->name('ranked-positions.index');
Route::get('contests/{contest}/ranked-positions/{rankedPosition}', [RankedPositionController::class, 'show'])->name('ranked-positions.show');

Route::get('contests/{contest}/ranked-position-accepted-exports/{rankedPosition}', [RankedPositionAcceptedExportController::class, 'show']);
Route::get('contests/{contest}/ranked-position-rejected-exports/{rankedPosition}', [RankedPositionRejectedExportController::class, 'show']);

/*---------------------------------------------------------------------------
| Ranked Position
|--------------------------------------------------------------------------*/
Route::get('contest/{contest}/calculation/{calculation}/vacant-positions', [VacantPositionController::class, 'index'])->name('vacant-positions.index');


/*---------------------------------------------------------------------------
| Application
|--------------------------------------------------------------------------*/

Route::get('contests/{contest}/applications/{application}', [ApplicationController::class, 'show'])->name('applications.show');
Route::get('contests/{contest}/applications/{application}/edit', [ApplicationController::class, 'edit']);
Route::put('contests/{contest}/applications', [ApplicationController::class, 'update']);

Route::get('front-office/applications/{id}', [FrontOfficeApplicationController::class, 'show']);
Route::get('front-office/applications/{id}/attachments/{attachmentId}', [FrontOfficeAttachmentController::class, 'show']);
Route::get('front-office/applications/{id}/attachments-zip', [FrontOfficeAttachmentController::class, 'zip']);

Route::get('contests/{contest}/applications/{application}/application-ratings', [ApplicationRatingController::class, 'index'])->name('application-ratings.index');

Route::get('contest/{contest}/calculation/{calculation}/employable-diff', [CalculationEmployableDiffController::class, 'index'])->name('calculation-employable-diff');

/*---------------------------------------------------------------------------
| Specialization
|--------------------------------------------------------------------------*/

Route::resource('specializations', SpecializationController::class);
Route::put('specializations/{specialization}/restore', [SpecializationController::class, 'restore'])->name('specializations.restore');

/*---------------------------------------------------------------------------
| Requirement
|--------------------------------------------------------------------------*/

Route::resource('requirements', RequirementController::class);
Route::put('requirements/{requirement}/restore', [RequirementController::class, 'restore'])->name('requirements.restore');

/*---------------------------------------------------------------------------
| Language
|--------------------------------------------------------------------------*/

Route::resource('languages', LanguageController::class);

/*---------------------------------------------------------------------------
| Invalidation Description
|--------------------------------------------------------------------------*/

Route::resource('invalidation-descriptions', InvalidationDescriptionController::class);

Route::get('statistics/front-office', [FrontOfficeStatisticsController::class, 'index'])->name('statistics.front-office')->middleware('permission:contractuals.adminUnit');
Route::get('statistics/back-office', [BackOfficeStatisticsController::class, 'index'])->name('statistics.back-office')->middleware('permission:contractuals.adminUnit');
