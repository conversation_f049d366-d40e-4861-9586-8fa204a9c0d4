<?php

use App\Http\Controllers\Admin\AppController;
use App\Http\Controllers\Admin\ImpersonatedUserController;
use App\Http\Controllers\Admin\ManagerController;
use App\Http\Controllers\Admin\PermissionController;
use App\Http\Controllers\Admin\RoleController;
use App\Http\Controllers\Admin\RouterController;
use App\Http\Controllers\Admin\UnitController;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\Admin\UserSecondaryUnitController;

Route::resource('apps', AppController::class)->except('show');

Route::resource('role', RoleController::class)->except('show');

Route::resource('permission', PermissionController::class)->except('show');

Route::resource('managers', ManagerController::class)->only(['index', 'edit', 'update']);

Route::post('impersontated-user', [ImpersonatedUserController::class, 'store'])->name('impersonated-user.store');
Route::delete('impersonated-user/{user}', [ImpersonatedUserController::class, 'destroy'])->name('impersonated-user.destroy')->withoutMiddleware('permission:administer');

// User routes
Route::get('user/{user}/secondary-unit/create', [UserSecondaryUnitController::class, 'create'])->name('user.secondary-unit.create');
Route::post('user/{user}/secondary-unit', [UserSecondaryUnitController::class, 'store'])->name('user.secondary-unit.store');
Route::delete('user/{user}/secondary-unit/{unit}', [UserSecondaryUnitController::class, 'destroy'])->name('user.secondary-unit.destroy');
Route::put('user/{user_id}/restore', [UserController::class, 'restore'])->name('user.restore');
Route::delete('user/{user_id}/force-destroy', [UserController::class, 'forceDestroy'])->name('user.force-destroy');
Route::resource('user', UserController::class)->except('show');

// Unit routes
Route::get('units', [UnitController::class, 'index'])->name('unit.index');
Route::get('units/create', [UnitController::class, 'create'])->name('unit.create');
Route::post('units', [UnitController::class, 'store'])->name('unit.store');
Route::get('units/{unit}/edit', [UnitController::class, 'edit'])->name('unit.edit');
Route::put('units/{unit}', [UnitController::class, 'update'])->name('unit.update');
Route::delete('units/{unit}', [UnitController::class, 'destroy'])->name('unit.destroy');

Route::get('routes', [RouterController::class, 'index'])->name('router.index');

Route::get('passport', fn () => view('passport.index'))->name('passport.index');
