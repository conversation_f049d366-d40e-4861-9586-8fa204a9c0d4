<?php

use App\Http\Controllers\Opendata\DatasetController;
use App\Http\Controllers\Opendata\LicenceController;
use App\Http\Controllers\Opendata\FiletypeController;
use App\Http\Controllers\Opendata\UpdaterateController;

Route::get('/', fn () => view('opendata.home'))->name('home');

Route::get('dataset/directoratelisting', [DatasetController::class, 'directorateListing'])->name('dataset.directoratelisting');
Route::any('dataset/bycategory', [DatasetController::class, 'listByCategory'])->name('dataset.bycategory');
Route::get('dataset/search', [DatasetController::class, 'search'])->name('dataset.search');
Route::post('dataset/search', [DatasetController::class, 'showSearchResults'])->name('dataset.search-results');
Route::resource('dataset', DatasetController::class);

Route::middleware('permission:opendata.admin')->group(function () {
    Route::resource('filetype', FiletypeController::class);
    Route::resource('licence', LicenceController::class);
    Route::resource('updaterate', UpdaterateController::class);
});
