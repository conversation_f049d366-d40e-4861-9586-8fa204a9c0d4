version: "3.9"

services:
  web:
    image: 10.2.128.32:5000/culturegr/apptree/web:staging
    build:
      context: ./
      dockerfile: ./docker/web/Dockerfile
      target: ci-build
      args:
        NGINX_VERSION: ${DOCKER_NGINX_VERSION}
        NODE_VERSION: ${DOCKER_NODE_VERSION}
    ports:
      - "8080:80"
      - "8443:443"
    networks:
      - frontend
    volumes:
      - /home/<USER>/ssl:/etc/nginx/ssl
  app:
    image: 10.2.128.32:5000/culturegr/apptree/app:staging
    build:
      context: ./
      dockerfile: ./docker/app/Dockerfile
      target: ci-build
      args:
        PHP_VERSION: ${DOCKER_PHP_VERSION}
        COMPOSER_VERSION: ${DOCKER_COMPOSER_VERSION}
        NODE_VERSION: ${DOCKER_NODE_VERSION}
        MYSQL_VERSION: ${DOCKER_MYSQL_VERSION}
      labels:
        - "commit.sha=${COMMIT_SHA:-}"
    ports:
      - "6001:6001"
    environment:
      APP_ENV: staging
      APP_KEY_FILE: /run/secrets/app_key
      DB_USERNAME_FILE: /run/secrets/db_username
      DB_PASSWORD_FILE: /run/secrets/db_password
      LDAP_USERNAME_FILE: /run/secrets/ldap_username
      LDAP_PASSWORD_FILE: /run/secrets/ldap_password
      BUGSNAG_API_KEY_FILE: /run/secrets/bugsnag_api_key
      PUBLIC_CONTRACTUALS_API_KEY_FILE: /run/secrets/public_contractuals_api_key
      PUSHER_APP_KEY_FILE: /run/secrets/pusher_app_key
      PUSHER_APP_SECRET_FILE: /run/secrets/pusher_app_secret
      MAIL_PASSWORD_FILE: /run/secrets/mail_password
    secrets:
      - app_key
      - db_password
      - db_username
      - ldap_username
      - ldap_password
      - bugsnag_api_key
      - public_contractuals_api_key
      - pusher_app_key
      - pusher_app_secret
      - mail_password
    networks:
      - frontend
      - backend
    volumes:
      - storage_data:/var/www/html/storage/app
      - /home/<USER>/ssl/certs:/usr/local/share/ca-certificates/custom:ro
#      - /mnt/backup_compass:/mnt/backup_compass
#      - /home/<USER>/ssl:/etc/ssl/websockets
  redis:
    image: "redis:${DOCKER_REDIS_VERSION}"
    restart: always
    command: redis-server --save 20 1 --loglevel warning --requirepass admin
    ports:
      - "6379:6379"
    networks:
      - backend
    volumes:
      - redis_data:/data

secrets:
  app_key:
    external: true
  db_root_password:
    external: true
  db_database:
    external: true
  db_password:
    external: true
  db_username:
    external: true
  ldap_username:
    external: true
  ldap_password:
    external: true
  bugsnag_api_key:
    external: true
  public_contractuals_api_key:
    external: true
  pusher_app_key:
    external: true
  pusher_app_secret:
    external: true
  mail_password:
    external: true

networks:
  frontend:
  backend:

volumes:
  storage_data:
    driver: local
  mysql_data:
    driver: local
  redis_data:
    driver: local
