version: '3'

# This file contains network configurations that are optional
# It can be included with docker-compose -f docker-compose.yml -f docker-compose.external-networks.yml up
# Or excluded for environments that don't need these networks

services:
  app:
    networks:
      - db
      - redis

networks:
  db:
    name: ${DOCKER_DB_NETWORK}
    driver: bridge
    external: true
  redis:
    name: ${DOCKER_REDIS_NETWORK}
    driver: bridge
    external: true
