# Docker Environment Setup

This document explains how to set up the Docker environment for different operating systems.

## Environment Configuration

The project uses Docker Compose to manage services. The main services are:

- **web**: NGINX web server
- **app**: PHP application
- **db**: MySQL database (optional)
- **redis**: Redis server (optional)

## Setup Options

### Option 1: Using External DB and Redis (Linux)

For Linux users who run DB and Redis as external dockerized services:

1. Set the following in your `.env` file:
   ```
   DB_HOST=db-service
   REDIS_HOST=redis-service
   ```

2. Start the application using the `develop` script:
   ```bash
   ./develop up -d
   ```

   The script will automatically detect that you're on Linux and include the `docker-compose.external-networks.yml` file.

### Option 2: Using Local DB and Redis (Windows/Mac)

For Windows/Mac users who run DB and Redis locally (non-dockerized):

1. Set the following in your `.env` file:
   ```
   DB_HOST=host.docker.internal
   REDIS_HOST=host.docker.internal
   ```

2. Start the application using the `develop` script:
   ```bash
   ./develop up -d
   ```

   The script will automatically detect that you're on Windows/Mac and use only the main `docker-compose.yml` file.

## Manual Configuration

If you want to override the automatic OS detection, you can use these commands:

- To explicitly use external networks (for Linux):
  ```bash
  ./develop up:shared -d
  ```

- To explicitly avoid external networks (for Windows/Mac):
  ```bash
  ./develop up:basic -d
  ```

## Notes

- Windows and Mac users can use `host.docker.internal` to connect to services running on the host machine
- Linux users need to use the actual network IP or Docker networks to connect to services
- The `docker-compose.external-networks.yml` file is optional and only needed if you're using external Docker networks
- The `develop` script automatically detects your OS and uses the appropriate configuration
