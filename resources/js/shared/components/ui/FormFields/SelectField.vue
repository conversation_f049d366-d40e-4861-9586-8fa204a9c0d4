<template>
  <field
    :name="name"
    :error="error"
    :title="title"
    :description="description"
    :required="required"
  >
    <el-select
      :id="name"
      :value="updatedValue"
      placeholder="Επιλέξτε..."
      :name="name"
      :disabled="!this.editing"
      @input="updateValue"
    >
      <el-option
        v-for="option in options"
        :key="option[Object.keys(option)[0]]"
        :value="option[Object.keys(option)[0]]"
        :label="option[Object.keys(option)[1]]"
        :selected="option[Object.keys(option)[0]] == value"
      />
    </el-select>
  </field>
</template>

<script>
import Field from './Field.vue';

export default {
  name: 'SelectField',

  components: {
    Field,
  },

  props: ['name', 'title', 'description', 'value', 'options', 'error', 'open', 'required'],

  computed: {
    editing() {
      if (typeof this.open !== 'undefined') {
        return this.open;
      }

      if (typeof this.$parent.editing !== 'undefined') {
        return this.$parent.editing;
      }

      return true;
    },
    updatedValue() {
      return this.value;
    },
  },

  methods: {
    updateValue(value) {
      // sanitize, validate etc
      this.$emit('valid');
      //
      this.$emit('input', value);
    },
  },
};
</script>
