<template>
  <table
    v-if="applicationChildren.length > 0"
    class="table table-condensed"
  >
    <thead>
      <tr>
        <th>A/A</th>
        <th>Ονοματεπώνυμο</th>
        <th>Ημ. Γέννησης</th>
        <th>ΑμεΑ</th>
        <th style="width: 10%;">Ημέρες</th>
        <th><i class="fa fa-cog" /></th>
      </tr>
    </thead>
    <tbody>
      <tr
        v-for="(child, index) in applicationChildren"
        :key="index"
      >
        <td>
          {{ index + 1 }}
        </td>
        <td>{{ child.full_name }}</td>
        <td>{{ child.birthdate }}</td>
        <td>{{ child.has_disability ? 'NAI' + ' ' + child.disability_percentage + '%' : '' }}</td>
        <td>{{ child.days }}</td>
        <td>
          <div>
            <EditApplicationChildButton
              :application-child="child"
              :application-id="applicationId"
              @update="$emit('update', $event)"
              style="float:left"
            />
            <RemoveApplicationChildButton
              :application-child="child"
              :application-id="applicationId"
              @remove="$emit('remove', $event)"
              style="float:left; margin-left:10px"
            />
          </div>
        </td>
      </tr>
    </tbody>
  </table>
  <InfoAlert v-else>
    <i class="fa fa-exclamation-circle" /> Δεν έχουν καταχωρισθεί τέκνα
  </InfoAlert>
</template>

<script>
import EditApplicationChildButton from '@/summerCamps/components/application/EditApplicationChildButton.vue';
import RemoveApplicationChildButton from '@/summerCamps/components/application/RemoveApplicationChildButton.vue';
import InfoAlert from '@/shared/components/ui/Alerts/InfoAlert.vue';

export default {
  name: 'ApplicationChildList',
  components: {
    InfoAlert,
    EditApplicationChildButton,
    RemoveApplicationChildButton,
  },
  props: {
    applicationChildren: {
      type: Array,
      required: true,
    },
    applicationId: {
      type: Number,
      required: true,
    },
  },
};
</script>

<style scoped>
table.assets-table thead,
table.positions-table thead {
  background-color: var(--gray-400)
}

table.assets-table tbody tr td {
  border: unset;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  vertical-align: middle;
}

table.assets-table thead tr th:first-child,
table.assets-table tbody tr td:first-child {
  width: 32px;
  text-align: center;
}

table.assets-table thead tr th:last-child,
table.assets-table tbody tr td:last-child {
  width: 100px;
  text-align: center;
}

table.assets-table tbody tr td:last-child div {
  display: flex;
  justify-content: space-around;
}
</style>
