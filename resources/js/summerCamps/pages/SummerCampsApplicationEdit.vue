<template>
  <div>
    <SuccessAlert v-if="successMessage">
      <p>{{ successMessage }}</p>
    </SuccessAlert>
    <DangerAlert
        v-if="errorMessage"
        :message="errorMessage"
    />
    <Box
        title="Επεξεργασία Αίτησης"
        :is-loading="form.busy"
    >
      <form id="summer-camps-application-form" @submit.prevent="save">
        <FormSection
          title="Γενικά Στοιχεία"
          icon="fa-pencil-square-o"
        >
          <div class="row">
            <div class="col-sm-12 col-md-6">
              <TextField
                  v-model="form.data.name"
                  title="Όνομα"
                  name="name"
                  :error="form.errors.get('name')"
              />
            </div>
            <div class="col-sm-12 col-md-6">
              <TextField
                  v-model="form.data.surname"
                  title="Επώνυμο"
                  name="surname"
                  :error="form.errors.get('surname')"
              />
            </div>
            <div class="col-sm-12 col-md-6">
              <TextField
                  v-model="form.data.father_name"
                  title="Πατρώνυμο"
                  name="father_name"
                  :error="form.errors.get('father_name')"
              />
            </div>
          </div>
          <div class="row">
            <div class="col-sm-12 col-md-6">
              <SelectField
                  v-model="form.data.employment_type_id"
                  title="Σχέση Εργασίας"
                  name="employment_type_id"
                  :error="form.errors.get('employment_type_id')"
                  :options="employmentTypeOptions"
              />
            </div>
            <div class="col-sm-12 col-md-6">
              <SelectField
                  v-model="form.data.employment_sector_id"
                  title="Τομέας"
                  name="employment_sector_id"
                  :error="form.errors.get('employment_sector_id')"
                  :options="employmentSectorOptions"
              />
            </div>
          </div>
          <div class="row">
            <div class="col-sm-12">
              <TextField
                  v-model="form.data.position"
                  title="Υπηρεσία Οργανικής Θέσης"
                  name="position"
                  :error="form.errors.get('position')"
              />
            </div>
          </div>
          <div class="row">
            <div class="col-sm-12 col-md-6">
              <TextField
                  v-model="form.data.afm"
                  title="ΑΦΜ"
                  name="afm"
                  :error="form.errors.get('afm')"
              />
            </div>
            <div class="col-sm-12 col-md-6">
              <TextField
                  v-model="form.data.doy"
                  title="Αρμόδια Δ.Ο.Υ."
                  name="doy"
                  :error="form.errors.get('doy')"
              />
            </div>
          </div>
        </FormSection>
        <FormSection
            title="Στοιχεία Επικοινωνίας"
            icon="fa-envelope-o"
        >
          <div class="row">
            <div class="col-sm-12 col-md-6">
              <TextField
                  v-model="form.data.personal_phone"
                  title="Τηλέφωνο Επικοινωνίας"
                  name="personal_phone"
                  :error="form.errors.get('personal_phone')"
              />
            </div>
            <div class="col-sm-12 col-md-6">
              <TextField
                  v-model="form.data.mobile_phone"
                  title="Κινητό"
                  name="mobile_phone"
                  :error="form.errors.get('mobile_phone')"
              />
            </div>
            <div class="col-sm-12 col-md-6">
              <TextField
                  v-model="form.data.work_phone"
                  title="Τηλέφωνο Εργασίας"
                  name="work_phone"
                  :error="form.errors.get('work_phone')"
              />
            </div>
          </div>
          <div class="row">
            <div class="col-sm-12 col-md-6">
              <TextField
                  v-model="form.data.email_address"
                  title="Email Επικοινωνίας"
                  name="email_address"
                  :error="form.errors.get('email_address')"
              />
            </div>
          </div>
        </FormSection>
        <FormSection
            title="Ωφελούμενα Τέκνα"
            icon="fa-child"
        >
          <application-children
              :application-id="application.id"
              :application-children-data="application.application_children"
          />
        </FormSection>
        <FormSection
          title="Επισυνάψεις"
          icon="fa-paperclip"
        >
          <div class="row">
            <div class="col-sm-12">
              <UploadAjaxField
                  :initial-files="form.data.attachments"
                  title="Δικαιολογητικά"
                  name="attachments"
                  :base-url="`/api/summer-camps/applications/${application.id}/attachments`"
                  :error="form.errors.attachments"
              />
            </div>
          </div>
        </FormSection>
        <hr>
        <div class="row">
          <div class="col-sm-4">
            <a
                class="btn btn-default btn-block"
                href="/summer-camps/applications"
            >
              <i
                  class="fa fa-arrow-left"
                  aria-hidden="true"
              /> Επιστροφή
            </a>
          </div>
          <div class="col-sm-4">
            <LoadingButton
                class="btn btn-block btn-primary btn-outline"
                type="button"
                :loading="form.busy"
                @click="save"
            >
              <i class="fa fa-save" /> Προσωρινή αποθήκευση
            </LoadingButton>
          </div>
          <div class="col-sm-4">
            <LoadingButton
                class="btn btn-block btn-primary"
                type="button"
                :loading="form.busy"
                @click="saveAndPreview"
            >
              <i class="fa fa-arrow-right" /> Προς υποβολή
            </LoadingButton>
          </div>
        </div>
      </form>
    </Box>
  </div>
</template>

<script>
import Box from '@/shared/components/ui/Boxes/Box.vue';
import FormSection from '@/shared/components/ui/FormFields/FormSection.vue'
import LoadingButton from '@/shared/components/ui/Buttons/LoadingButton.vue';
import SubmitButton from "@/shared/components/ui/Buttons/SubmitButton.vue";
import TextField from '@/shared/components/ui/FormFields/TextField.vue';
import TextareaField from '@/shared/components/ui/FormFields/TextareaField.vue';
import DateField from '@/shared/components/ui/FormFields/DateField.vue';
import NumberField from '@/shared/components/ui/FormFields/NumberField.vue';
import SelectField from '@/shared/components/ui/FormFields/SelectField.vue';
import SwitchField from '@/shared/components/ui/FormFields/SwitchField.vue';

import Form from '../../shared/Form';
import __ from '../../shared/Helpers';
import SuccessAlert from "@/shared/components/ui/Alerts/SuccessAlert.vue";
import DangerAlert from "@/shared/components/ui/Alerts/DangerAlert.vue";
import UploadAjaxField from '@/shared/components/ui/FormFields/UploadAjaxField.vue';
// import {isAxiosError} from "@/shared/composables/useHttp";

export default {
  name: 'SummerCampsApplicationEdit',

  components: {
    UploadAjaxField,
    DangerAlert,
    SuccessAlert,
    FormSection,
    Box,
    LoadingButton,
    SubmitButton,
    TextField,
    TextareaField,
    DateField,
    SelectField,
    SwitchField,
    NumberField,
  },

  props: {
    application: {
      type: Object,
      default: null,
    },
    employmentSectorOptions: {
      type: Array,
      default: null,
    },
    employmentTypeOptions: {
      type: Array,
      default: null,
    },
  },

  data() {
    return {
      isLoading: false,
      form: new Form({
        name: this.application.name,
        surname: this.application.surname,
        father_name: this.application.father_name,
        employment_sector_id: this.application.employment_sector_id,
        employment_type_id: this.application.employment_type_id,
        position: this.application.position,
        afm: this.application.afm,
        doy: this.application.doy,
        personal_phone: this.application.personal_phone,
        mobile_phone: this.application.mobile_phone,
        work_phone: this.application.work_phone,
        email_address: this.application.email_address,
        attachments: this.application.attachments ?? [],
      }),
      successMessage: '',
      errorMessage: '',
    }
  },

  methods: {

    async save() {
      console.log('saving');
      this.successMessage = '';
      this.errorMessage = '';

      try {
        const response = await this.form.put(`/api/summer-camps/applications/${this.application.id}`);
        this.successMessage = response.data.message;
        window.scrollTo(0, 0);
      } catch (err) {
          this.errorMessage = err.response?.data?.message ?? 'Παρουσιάστηκε σφάλμα κατά την καταχώρηση της αίτησης';
          window.scrollTo(0, 0);
      }
    },

    async saveAndPreview() {
      this.successMessage = '';
      this.errorMessage = '';

      try {
        await this.form.put(`/api/summer-camps/applications/${this.application.id}`);
        window.location.replace(`/summer-camps/applications/${this.application.id}/`);
        // success(response.message);
      } catch (err) {
        // if (isAxiosError(err)) {
          this.errorMessage = err.response?.data?.message ?? 'Παρουσιάστηκε σφάλμα κατά την καταχώρηση της αίτησης';
          window.scrollTo(0, 0);
        // }
      }
    },
  }
}

</script>
