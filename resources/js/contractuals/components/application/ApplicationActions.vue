<template>
  <div>
    <!--
    Application action buttons are dependent on the application status
     - If application is auto-rated => reject, approve or disapprove
     - If application is under review => re-import, reject, save or save & rate
     - If application is rated => undo rating
     -->
    <div class="row">
      <div class="col-sm-3">
        <a
          :href="`/contractuals/contests/${application.contest_id}`"
          class="btn btn-default btn-block"
        >
          <i class="fa fa-arrow-left" /> Επιστροφή στην προβολή διαγωνισμού
        </a>
      </div>
      <div
        v-if="applicationIsAutoRated && !contest.is_rated && (!contest.is_restricted || contest.can.admin)"
        class="col-sm-3"
      >
        <DisapproveApplicationRatingButton
          :application="application"
          @disapprove-auto-rating="disapproveAutoRating"
        />
      </div>
      <div
        v-if="applicationIsAutoRated && !contest.is_rated && (!contest.is_restricted || contest.can.admin)"
        class="col-sm-3"
      >
        <ApproveApplicationRatingButton
          :application="application"
          @approve-auto-rating="approveAutoRating"
        />
      </div>
      <div
        v-if="applicationIsUnderEvaluation && !contest.is_rated && (!contest.is_restricted || contest.can.admin)"
        class="col-sm-3"
      >
        <a
          class="btn btn-primary btn-block"
          :href="`/contractuals/contests/${application.contest_id}/applications/${application.id}/edit`"
        >
          <i class="fa fa-pencil-square-o" /> Aξιολόγηση αίτησης
        </a>
      </div>
      <div
        v-if="applicationIsEvaluated && !contest.is_rated && (!contest.is_restricted || contest.can.admin)"
        class="col-sm-3"
      >
        <UnrateApplicationButton
          :application="application"
          @unrate-application="unrateApplication"
        />
      </div>
    </div>
  </div>
</template>

<script>
import ApproveApplicationRatingButton from '@/contractuals/components/application/ApproveApplicationRatingButton.vue';
import DisapproveApplicationRatingButton from '@/contractuals/components/application/DisapproveApplicationRatingButton.vue';
import UnrateApplicationButton from '@/contractuals/components/application/UnrateApplicationButton.vue';

export default {
  name: 'ApplicationActions',
  components: {
    UnrateApplicationButton,
    DisapproveApplicationRatingButton,
    ApproveApplicationRatingButton,
  },
  props: {
    application: {
      type: Object,
      required: true,
    },
    contest: {
      type: Object,
      required: true,
    },
  },
  computed: {
    applicationIsAutoRated() {
      return this.application.is_auto_rated === true;
    },
    applicationIsEvaluated() {
      return this.application.rated_at !== null && !this.applicationIsAutoRated;
    },
    applicationIsUnderEvaluation() {
      return this.application.rated_at === null;
    },
    // userCanEdit() {
    //   return this.$can('contractuals.admin'); // || this.application.contest.restricted_at === null
    // },
  },
  methods: {
    approveAutoRating(data) {
      this.$emit('auto-rating-approved', data);
    },
    disapproveAutoRating(application) {
      this.$emit('auto-rating-disapproved', application);
    },
    unrateApplication(application) {
      this.$emit('application-unrated', application);
    },
  },
};
</script>
