<template>
  <div
    class="box-body"
    :class="{'bg-paper': isEditForm}"
  >
    <form
      id="conservatorForm"
      action="/conservations/conservator"
      method="POST"
      @submit.prevent="onSubmit"
    >
      <div
        class="row"
        style="padding-top:20px"
      >
        <div class="col-sm-2">
          <text-field
            v-model="form.data.registration_number"
            name="registration_number"
            title="Αρ. Μητρώου *:"
            :error="form.errors.get('registration_number')"
            @valid="form.errors.clear('registration_number')"
          />
        </div>
        <div class="col-sm-3">
          <text-field
            v-model="form.data.name"
            name="name"
            title="Όνομα *:"
            :error="form.errors.get('name')"
            @valid="form.errors.clear('name')"
          />
        </div>
        <div class="col-sm-4">
          <text-field
            v-model="form.data.surname"
            name="surname"
            title="Επώνυμο *:"
            :error="form.errors.get('surname')"
            @valid="form.errors.clear('surname')"
          />
        </div>
        <div class="col-sm-3">
          <text-field
            v-model="form.data.fathername"
            name="fathername"
            title="Πατρώνυμο *:"
            :error="form.errors.get('fathername')"
            @valid="form.errors.clear('fathername')"
          />
        </div>
      </div>

      <hr>

      <div class="row">
        <div class="col-sm-12">
          <h4 class="form-header">
            Προσωπικά Στοιχεία
          </h4>
          <input
            v-if="form.data.id"
            v-model="form.data.id"
            type="hidden"
            name="id"
          >
        </div>
      </div>

      <div class="row">
        <div class="col-sm-6">
          <selectfilter-field
            v-model="form.data.degree_id"
            name="degree_id"
            title="Πτυχίο:"
            :options="degrees"
            :error="form.errors.get('degree_id')"
            @valid="form.errors.clear('degree_id')"
          />
        </div>
        <div class="col-sm-6">
          <selectfilter-field
            v-model="form.data.school_id"
            name="school_id"
            title="Εκπαιδευτικό Ίδρυμα:"
            :options="schools"
            :error="form.errors.get('school_id')"
            @valid="form.errors.clear('school_id')"
          />
        </div>
      </div>
      <div class="row">
        <div class="col-sm-3">
          <text-field
            v-model="form.data.amka"
            name="amka"
            title="ΑΜΚΑ:"
            :error="form.errors.get('amka')"
            @valid="form.errors.clear('amka')"
          />
        </div>
        <div class="col-sm-3">
          <text-field
            v-model="form.data.afm"
            name="afm"
            title="ΑΦΜ:"
            :error="form.errors.get('afm')"
            @valid="form.errors.clear('afm')"
          />
        </div>
        <div class="col-sm-6">
          <text-field
            v-model="form.data.doy"
            name="doy"
            title="ΔΟΥ:"
            :error="form.errors.get('doy')"
            @valid="form.errors.clear('doy')"
          />
        </div>
      </div>

      <div class="row">
        <div class="col-sm-3">
          <date-field
            v-model="form.data.birthdate"
            name="birthdate"
            title="Ημ. Γέννησης:"
            :error="form.errors.get('birthdate')"
          />
        </div>
        <div class="col-sm-3">
          <text-field
            v-model="form.data.policeid_number"
            name="policeid_number"
            title="Αρ. Δελτίου Ταυτότητας:"
            :error="form.errors.get('policeid_number')"
            @valid="form.errors.clear('policeid_number')"
          />
        </div>
        <div class="col-sm-3">
          <date-field
            v-model="form.data.policeid_date"
            name="policeid_date"
            title="Ημ. Ταυτότητας:"
            :error="form.errors.get('policeid_date')"
            @valid="form.errors.clear('policeid_date')"
          />
        </div>
      </div>

      <hr>

      <div class="row">
        <div class="col-sm-12">
          <h4 class="form-header">
            Στοιχεία Επικοινωνίας
          </h4>
        </div>
      </div>

      <div class="row">
        <div class="col-sm-8">
          <text-field
            v-model="form.data.street"
            name="street"
            title="Οδός:"
            :error="form.errors.get('street')"
            @valid="form.errors.clear('street')"
          />
        </div>
        <div class="col-sm-2">
          <text-field
            v-model="form.data.street_number"
            name="street_number"
            title="Αριθμός:"
            :error="form.errors.get('street_number')"
            @valid="form.errors.clear('street_number')"
          />
        </div>
        <div class="col-sm-2">
          <text-field
            v-model="form.data.postcode"
            name="postcode"
            title="Τ.Κ.:"
            :error="form.errors.get('postcode')"
            @valid="form.errors.clear('postcode')"
          />
        </div>
      </div>

      <div class="row">
        <div class="col-sm-4">
          <text-field
            v-model="form.data.city"
            name="city"
            title="Πόλη:"
            :error="form.errors.get('city')"
            @valid="form.errors.clear('city')"
          />
        </div>
        <div class="col-sm-4">
          <selectfilter-field
            v-model="form.data.region_id"
            name="region_id"
            title="Περιφέρεια:"
            :options="regions"
            :error="form.errors.get('region_id')"
            @valid="form.errors.clear('region_id')"
            @input="updatePrefectures"
          />
        </div>
        <div class="col-sm-4">
          <selectfilter-field
            ref="prefecture-field"
            v-model="form.data.prefecture_id"
            name="prefecture_id"
            title="Περιφερειακή Ενότητα:"
            :options="prefecturesList"
            :error="form.errors.get('prefecture_id')"
            @valid="form.errors.clear('prefecture_id')"
          />
        </div>
      </div>

      <div class="row">
        <div class="col-sm-4">
          <text-field
            v-model="form.data.phonenumber1"
            name="phonenumber1"
            title="Αρ. Τηλεφώνου 1:"
            :error="form.errors.get('phonenumber1')"
            @valid="form.errors.clear('phonenumber1')"
          />
        </div>
        <div class="col-sm-4">
          <text-field
            v-model="form.data.phonenumber2"
            name="phonenumber2"
            title="Αρ. Τηλεφώνου 2:"
            :error="form.errors.get('phonenumber2')"
            @valid="form.errors.clear('phonenumber2')"
          />
        </div>
        <div class="col-sm-4">
          <text-field
            v-model="form.data.email"
            name="email"
            title="E-mail:"
            :error="form.errors.get('email')"
            @valid="form.errors.clear('email')"
          />
        </div>
      </div>

      <hr>

      <div class="row">
        <div class="col-sm-12">
          <h4 class="form-header">
            Κατάσταση
          </h4>
        </div>
      </div>

      <div class="row">
        <div class="col-sm-2">
          <switch-field
            v-model="form.data.active"
            name="active"
            title="Ενεργός:"
            :error="form.errors.get('active')"
            @valid="form.errors.clear('active')"
            @input="updateActive"
          />
        </div>
        <div class="col-sm-3">
          <date-field
            v-model="form.data.status_date"
            name="status_date"
            title="Ημ Κατάστασης:"
            :error="form.errors.get('status_date')"
            @valid="form.errors.clear('status_date')"
          />
        </div>
        <div class="col-sm-3">
          <selectfilter-field
            v-model="form.data.status_type_id"
            name="status_type_id"
            title="Αιτιολογία:"
            :options="status_types"
            :error="form.errors.get('status_type_id')"
            @valid="form.errors.clear('status_type_id')"
          />
        </div>
        <div class="col-sm-4">
          <selectfilter-field
            v-show="form.data.status_type_id == 2"
            v-model="form.data.unit_id"
            name="unit_id"
            title="Υπηρεσία:"
            :options="units"
            :error="form.errors.get('unit_id')"
            @valid="form.errors.clear('unit_id')"
          />
        </div>
        <div class="col-sm-4">
          <text-field
            v-show="form.data.status_type_id == 5"
            v-model="form.data.government_position"
            name="government_position"
            title="Θέση Δημοσίου:"
            :error="form.errors.get('government_position')"
            @valid="form.errors.clear('government_position')"
          />
        </div>
      </div>

      <hr>

      <div class="row">
        <div class="col-sm-12">
          <h4 class="form-header">
            Υλικά
          </h4>
        </div>
      </div>
      <div class="row">
        <div class="col-sm-12">
          <h5 v-show="form.data.materials.length == 0">
            Δεν έχουν καταχωρηθεί υλικά μέχρι στιγμής
          </h5>
        </div>
      </div>
      <div class="row">
        <div class="col-sm-12">
          <table
            v-show="form.data.materials.length > 0"
            class="table table-condensed"
          >
            <thead>
              <tr class="bg-primary">
                <th>A/A</th>
                <th>Υλικό</th>
                <th>Ημ. Χορήγησης</th>
                <th>Αρ. Πρακτικού</th>
                <th><!--delete material--></th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(material, index) in form.data.materials">
                <td>
                  <!--No.-->
                  {{ index+1 }}
                </td>
                <td width="42%">
                  <!--Material name-->
                  <selectfilter-field
                    v-model="material.material_id"
                    :name="`material.${ index }.material_id`"
                    :options="materials"
                    :error="form.errors.get(`materials.${ index }.material_id`)"
                    @valid="form.errors.clear(`materials.${ index }.material_id`)"
                  />
                </td>
                <td width="42%">
                  <!--Material issued_at-->
                  <date-field
                    v-model="material.issued_at"
                    :name="`materials.${ index }.issued_at`"
                    :error="form.errors.get(`materials.${ index }.issued_at`)"
                    @valid="form.errors.clear(`materials.${ index }.issued_at`)"
                  />
                </td>
                <td width="14%">
                  <!--Material issued_record_number-->
                  <text-field
                    v-model="material.issued_record_number"
                    :name="`materials.${ index }.issued_record_number`"
                    :error="form.errors.get(`materials.${ index }.issued_record_number`)"
                    @valid="form.errors.clear(`materials.${ index }.issued_record_number`)"
                  />
                </td>
                <td>
                  <!--Delete material-->
                  <a
                    href="#"
                    @click.prevent="removeMaterial(index)"
                  >
                    <i
                      class="fa fa-remove fa-2x text-red"
                      aria-hidden="true"
                    />
                  </a>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <div class="row">
        <div class="col-sm-12">
          <!--Add Material-->
          <button
            class="btn bg-light-blue"
            type="button"
            @click="addMaterial"
          >
            <i
              class="fa fa-plus"
              aria-hidden="true"
            />
            Προσθήκη
          </button>
        </div>
      </div>

      <hr>

      <div class="row">
        <div class="col-sm-3">
          <a
            class="btn btn-default btn-block"
            :href="backUrl"
          >
            <i class="fa fa-arrow-circle-left" /> Επιστροφή
          </a>
        </div>
        <div class="col-sm-9">
          <button
            class="btn btn-success btn-block"
            :disabled="form.errors.has()"
          >
            <i class="fa fa-check-circle" /> Καταχώρηση
          </button>
        </div>
      </div>
    </form>
  </div>
</template>

<script>

import Form from '../../shared/Form';
import { conservatorFormReceiveRule, conservatorFormSendRule } from '../ServerDataRules';
import TextField from '../../shared/components/ui/FormFields/TextField.vue';
import DateField from '../../shared/components/ui/FormFields/DateField.vue';
import SelectfilterField from '../../shared/components/ui/FormFields/SelectfilterField.vue';
import SwitchField from '../../shared/components/ui/FormFields/SwitchField.vue';

export default {
  name: 'ConservatorForm',

  components: {
    TextField,
    DateField,
    SelectfilterField,
    SwitchField,
  },

  props: ['conservator', 'new_registration_number', 'degrees', 'schools', 'status_types', 'materials', 'prefectures', 'regions', 'units'],

  data() {
    return {
      // formRelatedModels: '', // will be populated with form's related models through an API ajax call
      conservatorModel: this.conservator,
      prefecturesList: [],
      form: new Form({
        id: '',
        name: '',
        surname: '',
        fathername: '',
        degree_id: '',
        school_id: '',
        afm: '',
        doy: '',
        policeid_number: '',
        policeid_date: '',
        amka: '',
        birthdate: '',
        street: '',
        street_number: '',
        postcode: '',
        city: '',
        prefecture_id: '',
        region_id: '',
        phonenumber1: '',
        phonenumber2: '',
        email: '',
        registration_number: this.new_registration_number,
        active: true,
        status_date: '',
        status_type_id: '',
        unit_id: '',
        government_position: '',
        materials: [],
      },
      {
        // these are the rules for incoming and outgoing data
        incoming: conservatorFormReceiveRule,
        outgoing: conservatorFormSendRule,
      }),
    };
  },

  computed: {
    isEditForm() {
      if (typeof this.conservatorModel === 'undefined') {
        return false;
      }
      return true;
    },
    backUrl() {
      if (typeof this.conservatorModel === 'undefined') {
        return '\\conservations';
      }
      return `\\conservations\\conservator\\${this.conservatorModel.id}`;
    },
  },

  created() {
    if (this.conservatorModel) {
      this.form.populate(this.conservatorModel);
    }
    if (this.isEditForm) {
      if (this.conservatorModel.region_id) this.updatePrefectures(this.form.data.region_id);
      if (this.conservatorModel.prefecture_id) this.form.data.prefecture_id = this.conservatorModel.prefecture_id;
    }
  },

  mounted() {
    console.log('Component mounted.');
  },

  methods: {
    onSubmit() {
      let submitUrl = '/conservations/conservator';
      let submitMethod = 'post';

      if (this.isEditForm) {
        submitMethod = 'put';
        submitUrl = `/conservations/conservator/${this.conservatorModel.id}`;
      }

      this.form[submitMethod](submitUrl)
        .then((response) => {
          // console.log(response.data.message);
          // TODO: redirect and notify user through flash messages
          // in case there was data flashed to the session, the redirect will trigger another request to server
          // After the user is redirected, the flashed message from the session can be displayed using Blade syntax
          // window.location.replace(`/conservations/conservator/${response.data.id}/edit`); //this will trigger another request to url
          this.conservatorModel = response.data.conservator;

          this.form.populate(this.conservatorModel);

          swal({
            type: 'success',
            title: 'Επιτυχία!',
            text: response.data.message,
            showConfirmButton: true,
          });
        })
        .catch((error) => {
          swal({
            type: 'error',
            title: 'Σφάλμα καταχώρησης!',
            text: 'Ελένξτε τις τιμές των πεδίων καταχώρησης.',
            showConfirmButton: false,
            timer: 2500,
          });
        });
    },

    addMaterial() {
      this.form.data.materials.push({
        material_id: '',
        issued_at: '',
        issued_record_number: '',
      });
    },

    removeMaterial(index) {
      this.form.data.materials.splice(index, 1);
      this.form.errors.clear(`materials.${index}.material_id`);
      this.form.errors.clear(`materials.${index}.issued_at`);
      this.form.errors.clear(`materials.${index}.issued_record_number`);
    },

    // Update prefectures options based on region chosen
    updatePrefectures(region_id) {
      this.form.data.prefecture_id = '';
      this.prefecturesList = this.prefectures.filter((el) => el.region_id == region_id);
    },

    // Reset relevant fields if conservator is set to active
    updateActive(value) {
      if (value === true) {
        this.form.data.status_date = this.form.data.status_type_id = this.form.data.unit_id = '';
      }
    },
  },
};
</script>
