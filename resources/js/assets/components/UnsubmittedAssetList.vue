<script setup lang="ts">
import AssetDeleteConfirmationButton from '@/assets/components/AssetDeleteConfirmationButton.vue';
import AssetSubmitConfirmationButton from '@/assets/components/AssetSubmitConfirmationButton.vue';
import type { AssetList } from '@/assets/types';
import type {
  Filterabe, Listable, Paginator, ResourceFilters,
  Sortable,
} from '@/global';
import BaseButton from '@/shared/components/ui/Buttons/BaseButton.vue';
import ResourceViewer from '@/shared/components/ui/Data/ResourceViewer.vue';

defineProps<{
  assets: Paginator<AssetList>;
  filterables: Filterabe[];
  sortables: Sortable[];
  listables: Listable[];
  activeFiltersCookieKey: string;
}>();

const emit = defineEmits<{
  (e: 'fetch-data', filters: ResourceFilters): void;
  (e: 'asset-submitted'): void;
  (e: 'asset-deleted'): void;
}>();

</script>

<script lang="ts">
export default {
  name: 'UnsubmittedAssetList',
};
</script>

<template>
  <div>
    <ResourceViewer
      :paginated-data="assets"
      :filterables="filterables"
      :sortables="sortables"
      :listables="listables"
      :filter-cookie="activeFiltersCookieKey"
      export-url="/api/assets/unsubmitted-assets-export"
      @fetch-data="(filters) => emit('fetch-data', filters)"
    >
      <template #default="{ tableData }">
        <div
          v-if="tableData.column.field === 'actions'"
          class="action-buttons pull-right"
        >
          <BaseButton
            as="a"
            :href="`/assets/asset/${tableData.row.id}`"
            icon="tabler:info-circle"
            icon-only
            title="View Details"
          />
          <BaseButton
            as="a"
            :href="`/assets/asset/${tableData.row.id}/edit`"
            icon="tabler:edit"
            icon-only
            title="Edit Asset"
          />
          <AssetSubmitConfirmationButton
            v-if="tableData.row.can?.submit"
            :asset="tableData.row"
            @submitted="emit('asset-submitted')"
          />
          <AssetDeleteConfirmationButton
            v-if="tableData.row.can?.delete"
            :asset="tableData.row"
            @deleted="emit('asset-deleted')"
          />
        </div>
        <div v-else-if="tableData.column.field === 'contract'">
          <BaseButton
            as="a"
            :href="`/assets/contract/${tableData.row.contract_id}`"
            variation="plain"
          >
            {{ tableData.row.contract }}
          </BaseButton>
        </div>
      </template>
    </ResourceViewer>
  </div>
</template>

<style scoped>
.action-buttons {
  display: flex;
  justify-content: space-around;
  gap: 10px;
}

.action-button {
  color: var(--gray-600);
}

.action-button:hover {
  color: var(--brand-color);
}
</style>
