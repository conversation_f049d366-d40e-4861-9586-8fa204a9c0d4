<script setup lang="ts">
import { computed } from 'vue';

import type { ContractForm } from '@/assets/types';
import CancelButton from '@/shared/components/ui/Buttons/CancelButton.vue';
import SubmitButton from '@/shared/components/ui/Buttons/SubmitButton.vue';
import TextField from '@/shared/components/ui/FormFields/TextField.vue';
import useForm from '@/shared/composables/useForm';
import { isAxiosError } from '@/shared/composables/useHttp';

const emit = defineEmits<{
  (e: 'success', data: { response: Record<string, unknown>, isEdit: boolean }): void;
  (e: 'error', data: { message: string, error: unknown }): void;
  (e: 'cancel'): void;
}>();

const props = defineProps<{
  contract?: ContractForm;
}>();

// Initialize form with contract data or default values
const form = useForm<ContractForm>({
  contract_number: props.contract?.contract_number ?? '',
});

const isEdit = computed(() => !!props.contract?.id);
const formUrl = computed(() => (isEdit.value ? `/api/assets/contract/${props.contract?.id}` : '/api/assets/contract'));
const formMethod = computed(() => (isEdit.value ? 'put' : 'post'));

const handleSubmit = async () => {
  try {
    const response = await form[formMethod.value](formUrl.value);
    emit('success', {
      response,
      isEdit: isEdit.value,
    });
  } catch (err) {
    let errorMessage = 'Παρουσιάστηκε σφάλμα κατά την αποθήκευση. Παρακαλώ προσπαθήστε ξανά.';
    if (isAxiosError(err)) {
      errorMessage = err.response?.data?.message || errorMessage;
    }
    emit('error', {
      message: errorMessage,
      error: err,
    });
  }
};

const handleCancel = () => {
  emit('cancel');
};
</script>

<script lang="ts">
export default {
  name: 'ContractForm',
};
</script>

<template>
  <form @submit.prevent="handleSubmit">
    <div class="row">
      <div class="col-md-6">
        <TextField
          v-model="form.contract_number"
          title="Αριθμός Σύμβασης"
          name="contract_number"
          :error="form.errors.contract_number"
          required
        />
      </div>
    </div>

    <hr>
    <div class="row mt-4">
      <div class="col-sm-6">
        <CancelButton @cancel="handleCancel">
          <i class="fa fa-arrow-left" /> Επιστροφή
        </CancelButton>
      </div>
      <div class="col-sm-6 text-right">
        <SubmitButton :busy="form.processing">
          <i class="fa fa-floppy-o" /> {{ isEdit ? 'Ενημέρωση' : 'Αποθήκευση' }}
        </SubmitButton>
      </div>
    </div>
  </form>
</template>
