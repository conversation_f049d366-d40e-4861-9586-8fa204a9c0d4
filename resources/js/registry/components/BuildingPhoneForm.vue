<template>
  <form action="/registry/building/phone" method="POST"
        @submit.prevent="onSubmit(onUpdate)"
        @keydown.enter.prevent
        class="form-inline card-panel"
        v-bind:class="{'bg-paper': editing}"
  >
    <div class="loading-overlay" v-show="loading" tabindex="-1" style="opacity: 1.1; display: block;"></div>
    <div id="loader" v-show="loading"></div>

    <div class="row">
      <div class="col-sm-12">
        <input type="hidden" name="id" v-model="form.data.id" v-if="form.data.id">
        <input type="hidden" name="building_id" v-model="form.data.building_id">
        <input type="hidden" name="phone_provider_id" v-model="form.data.phone_provider_id"> <!-- OTE -->

        <text-field name="number" title="Αριθμός Κύριας Τηλ. Γραμμής" :required=true v-model="form.data.number"
                    :error="form.errors.get('number')" @valid="form.errors.clear('number')"></text-field>
        <text-field name="supply_number" title="Αριθμός ΕΣΥΠ" v-model="form.data.supply_number"
                    :error="form.errors.get('supply_number')" @valid="form.errors.clear('supply_number')"></text-field>
      </div>
    </div>
    <div class="row">
      <div class="col-sm-4">
        <switch-field name="has_internet" title="Σύνδεση Internet" v-model="form.data.has_internet"
                      :error="form.errors.get('has_internet')"
                      @valid="form.errors.clear('has_internet')"></switch-field>
      </div>
      <div class="col-sm-4">
        <switch-field name="by_syzefxis" title="Internet Εντός Σύζευξης" v-model="form.data.by_syzefxis"
                      :error="form.errors.get('by_syzefxis')" @valid="form.errors.clear('by_syzefxis')"></switch-field>
      </div>
      <div class="col-sm-4">
        <switch-field name="keles_linked" title="Σύνδεση ΚΕΛΕΣΣ" v-model="form.data.keles_linked"
                      :error="form.errors.get('keles_linked')"
                      @valid="form.errors.clear('keles_linked')"></switch-field>
      </div>

    </div>

    <div class="row">
      <div class="col-sm-4 col-md-4">
        <switch-field name="paid_centrally" title="Πληρωμή Κεντρικά (ΓΔΟΥ)" v-model="form.data.paid_centrally"
                      :error="form.errors.get('paid_centrally')"
                      @valid="form.errors.clear('paid_centrally')"></switch-field>
      </div>
      <div class="col-md-8" v-if="form.data.id != ''">
        <label>Υπηρεσία</label>
        <input type="text" v-if="form.data.id" readonly="readonly" class="form-control" style="width: 70%"
               :value="this.form.data.unit.name">
      </div>
    </div>


    <div class="row">
      <div class="col-sm-12">
        <date-field v-model="form.data.valid_from"
                    title="Έναρξη Χρήσης"
                    :required=true
                    name="valid_from"
                    :error="form.errors.get('valid_from')"
                    @valid="form.errors.clear('valid_from')"
        >
        </date-field>
        <date-field v-model="form.data.valid_to"
                    title="Λήξη Χρήσης"
                    name="valid_to"
                    :error="form.errors.get('valid_to')"
                    @valid="form.errors.clear('valid_to')"
        >
        </date-field>
      </div>

    </div>

    <div v-if="editing" class="row">
      <div class="col-sm-3">
        <a class="btn btn-default btn-block" @click.prevent="editOff">
          <i class="fa fa-arrow-circle-left"></i> Ακύρωση
        </a>
      </div>
      <div class="col-sm-9">
        <button class="btn btn-success btn-block" :disabled="form.errors.has()">
          <i class="fa fa-check-circle"></i> Αποθήκευση
        </button>
      </div>
    </div>
    <div v-else class="row">
      <div class="col-sm-3 col-sm-offset-6">
        <a class="btn btn-danger btn-block" href="#" @click.prevent="removeItem(onDelete)"
           v-if="this.permissions.delete || form.data.id == ''">
          <i class="fa fa-times"></i> Διαγραφή
        </a>
      </div>

      <div class="col-sm-3">
        <a class="btn btn-warning btn-block" @click.prevent="editOn"
           v-if="this.permissions.update || form.data.id == ''">
          <i class="fa fa-edit"></i> Επεξεργασία
        </a>
      </div>
    </div>

  </form>

</template>

<script>

import DateField from '../../shared/components/ui/FormFields/DateField.vue';
import SwitchField from '../../shared/components/ui/FormFields/SwitchField.vue';
import TextField from '../../shared/components/ui/FormFields/TextField.vue';
import Form from '../../shared/Form';
import formControls from '../formControls';
import Authorization from "../mixins/Authorization";
import {formReceiveRule, formSendRule} from '../ServerDataRules';

export default {
  name: 'BuildingPhoneForm',

  components: {
    DateField,
    TextField,
    SwitchField
  },

  mixins: [formControls, Authorization],

  props: {
    phone: {
      type: Object,
      default: () => {
        return {}
      }
    },
  },

  data() {
    return {
      // formRelatedModels: '', // will be populated with form's related models through an API ajax call
      loading: false,
      editing: true,
      model: this.phone,
      endpoint: '/registry/phone',
      form: new Form({
            id: '',
            phone_provider_id: '',
            number: '',
            supply_number: '',
            has_internet: '',
            by_syzefxis: '',
            paid_centrally: '',
            keles_linked: '',
            valid_from: '',
            valid_to: '',
            building_id: ''
          },
          {
            // these are the rules for incoming and outgoing data
            incoming: formReceiveRule,
            outgoing: formSendRule
          }),
      permissions: {
        create: false,
        read: false,
        update: false,
        delete: false
      }
    }
  },

  created() {
    if (this.model) {
      console.log('updating form with buildingPhone model');
      this.form.populate(this.model);
      this.$getModelPermissions(this, 'registry', 'phone', this.model.id);
    }

    this.editing = !this.assetExistsOnServer;
  },

  methods: {
    onDelete(data) {
      this.$emit('deleted', this.model);
    },

    onUpdate(data) {
      this.model = data.phone;
      this.form.populate(this.model);
      this.$emit('updated', this.model);
      this.editing = false;
      this.loading = false;
    }
  }
}
</script>
