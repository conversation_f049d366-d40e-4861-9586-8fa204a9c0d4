<template>
  <div>
    <!--Resource Buttons (Print, Delete)-->
    <div class="row" style="padding-bottom: 0.7rem" v-if="this.assetExistsOnServer">
      <div class="col-sm-12" style="text-align: right">
        <div>
          <a class="btn btn-flat btn-info btn-outline btn-sm"
             role="button"
             @click="printout"
             v-if="this.permissions.read">
            <i class="fa fa-download"></i> Εξαγωγή PDF
          </a>&nbsp;
          <a class="btn btn-flat btn-danger btn-outline btn-sm"
             role="button"
             @click="deleteResource(onDelete)"
             v-if="this.permissions.delete">
            <i class="fa fa-times"></i> Διαγραφή Οχήματος
          </a>
        </div>
      </div>
    </div>
    <!--TABS-->
    <horizontal-tabs style="min-height: 500px">
      <tab name="Στοιχεία Οχήματος" :selected="true">
        <vehicle-form
            :vehicle="this.model"
            :vehicle_types="this.vehicle_types"
            :permissions="this.permissions"
            :inside_modal="this.inside_modal"
            @updated="updateResource"
        >
        </vehicle-form>

      </tab>

      <tab name="Ασφαλίσεις" :selected="false" v-if="this.assetExistsOnServer">
        <vehicle-insurances
            :data="this.model.vehicle_insurances"
            :vehicle="this.model"
            :permissions="this.permissions"
            @updated="$emit('updated', this.model)"
            @removed="$emit('updated', this.model)"
        >
        </vehicle-insurances>
      </tab>
    </horizontal-tabs>
  </div>
</template>

<script>

import HorizontalTabs from '../../shared/components/ui/Tabs/HorizontalTabs.vue';
import Tab from '../../shared/components/ui/Tabs/TabItem.vue';
import formControls from '../formControls'
import Authorization from "../mixins/Authorization";
import VehicleForm from './VehicleForm.vue';
import VehicleInsurances from './VehicleInsurances.vue';

export default {
  name: 'VehicleTabs',

  components: {
    HorizontalTabs,
    Tab,
    VehicleForm,
    VehicleInsurances,
  },

  mixins: [formControls, Authorization],

  props: {
    vehicle: {
      type: Object,
      default: () => {
        return {}
      }
    },
    vehicle_types: {
      type: Array,
      default: () => []
    },
    inside_modal: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      loading: false,
      endpoint: '/registry/vehicle',
      permissions: {
        create: false,
        read: false,
        update: false,
        delete: false
      },
      model: this.vehicle,
    }
  },

  created() {
    if (this.assetExistsOnServer) {
      console.log('updating vehicle tabs with incoming data...');
      this.$getModelPermissions(this, 'registry', 'vehicle', this.model.id);
    }
  },

  mounted() {
    console.log('VehicleTabs Component mounted.');
  },

  watch: {
    // Whenever the vehicle Model changes
    model: function (newModel, oldModel) {
      // Check if we have a new ID (after first save and get permissions for this model)
      if (newModel.id != oldModel.id) {
        this.$getModelPermissions(this, 'registry', 'vehicle', newModel.id);
      }
    }
  },

  methods: {
    onDelete(data) {
      if (this.inside_modal) {
        this.$emit('deleted', this.model);
      } else {
        window.location.replace('/registry/');
      }
    },

    updateResource(value) {
      this.model = value;
      this.$emit('updated', this.model);
    },
  }

}

</script>
