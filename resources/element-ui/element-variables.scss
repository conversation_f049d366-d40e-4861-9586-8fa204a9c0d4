/* theme color */
$--color-primary: hsl(191, 87%, 36.3%);
$--color-text-primary: hsl(218, 16.9%, 34.9%); //gray-700
$--color-text-regular: hsl(218, 16.9%, 34.9%); //gray-700
$--color-text-placeholder: hsl(214, 20.3%, 69%); //gray-500
$--border-color-base: hsl(214, 20.3%, 69%); // gray-500
$--background-color-base: hsl(214, 31.8%, 91.4%); // gray-300

$--input-height: 34px;
$--input-border-radius: 'border-radius-zero';

/* icon font path, required */
$--font-path: '~element-ui/lib/theme-chalk/fonts';

@import "~element-ui/packages/theme-chalk/src/index";