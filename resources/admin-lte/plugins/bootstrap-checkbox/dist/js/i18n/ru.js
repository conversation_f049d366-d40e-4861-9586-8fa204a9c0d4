/*!
 * Bootstrap-checkbox v1.4.0 (https://vsn4ik.github.io/bootstrap-checkbox/)
 * Copyright 2013-2016 V<PERSON>. (https://github.com/vsn4ik)
 * Licensed under the MIT license
 */

'use strict';

(function($) {
  $.extend($.fn.checkboxpicker.defaults, {
    offLabel: 'Нет',
    onLabel: 'Да',
    warningMessage: 'Bootstrap-checkbox не поддерживает использование внутри label элемента.'
  });
})(jQuery);
