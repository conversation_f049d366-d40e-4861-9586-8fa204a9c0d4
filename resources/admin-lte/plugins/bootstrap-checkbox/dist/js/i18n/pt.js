/*!
 * Bootstrap-checkbox v1.4.0 (https://vsn4ik.github.io/bootstrap-checkbox/)
 * Copyright 2013-2016 Vasily A. (https://github.com/vsn4ik)
 * Licensed under the MIT license
 */

'use strict';

(function($) {
  $.extend($.fn.checkboxpicker.defaults, {
    offLabel: 'Não',
    onLabel: 'Sim',
    warningMessage: 'Elementos do tipo label não são suportados dentro do Bootstrap-checkbox.'
  });
})(jQuery);
