@extends('layouts.opendata')

@section('contentheader_description')
    Προβολή Συνόλου Δεδομένων
@endsection


@section('main-content')
    <div class="row">
        <div class="col-xs-12">
            <div class="box box-primary">
                <div class="box-header">
                    <h3 class="box-title">Προβολή Συνόλου Δεδομένων </h3>
                    <div class="box-tools pull-right">
                        @can('update', $dataset)
                            <a href="{{ route('opendata.dataset.edit', $dataset->id ) }}">
                                <i class='fa fa-2x fa-edit'></i>
                            </a>
                        @endcan
                    </div>
                </div>
                <div class="box-body">
                    <table class="table" id="dataset_tbl">
                        <tr>
                            <th width="350">Τίτλος</th>
                            <td>{{ $dataset->name }}</td>
                        </tr>
                        <tr>
                            <th>Περιγραφή</th>
                            <td>{{ $dataset->description }}</td>
                        </tr>
                        <tr>
                            <th>Θέση Αρχείου/Κάτοχος</th>
                            <td>{{ $dataset->location }}</td>
                        </tr>
                        <tr>
                            <th>Αριθμός Εγγραφών</th>
                            <td>{{ $dataset->records }}</td>
                        </tr>
                        <tr>
                            <th>Ρυθμός Ανανέωσης</th>
                            <td>{{ $dataset->updaterate->name ?? '' }}</td>
                        </tr>
                        <tr>
                            <th>Διάθεση</th>
                            <td>
                                @if ($dataset->obtainable)
                                    NAI
                                @else
                                    OXI
                                @endif
                            </td>
                        </tr>
                        @if (!$dataset->obtainable)
                            <tr>
                                <th>
                                    Αιτιολογία:
                                </th>
                                <td>
                                    @foreach($dataset->unobtainable_reasons as $unobtainable_reason)
                                        {{ $unobtainable_reason->name }}
                                        @if ($unobtainable_reason->open && $dataset->unobtainable_reason_other)
                                            : {{ $dataset->unobtainable_reason_other }}
                                        @endif.
                                    @endforeach
                                </td>
                            </tr>
                        @endif
                        <tr>
                            <th>Τύπος Αρχείου</th>
                            <td>{{ $dataset->type->name }}</td>
                        </tr>
                        @if ($dataset->type_id == 1)
                            <tr>
                                <th>Μορφή Αρχείων</th>
                                <td>
                                    @foreach($dataset->filetypes as $filetype)
                                        .{{ $filetype->name }}&nbsp;
                                    @endforeach
                                </td>
                            </tr>
                            <tr>
                                <th>Τρόπος Διάθεσης</th>
                                <td>{{ $dataset->digital_distribution }}</td>
                            </tr>
                            <tr>
                                <th>Url</th>
                                <td>{{ $dataset->url }}</td>
                            </tr>
                            <tr>
                                <th>Api</th>
                                <td>{{ $dataset->api }}</td>
                            </tr>
                        @else
                            <tr>
                                <th>Μορφή</th>
                                <td>{{ $dataset->physical_format }}</td>
                            </tr>
                            <tr>
                                <th>Τρόπος Διάθεσης</th>
                                <td>{{ $dataset->physical_distribution }}</td>
                            </tr>
                            <tr>
                                <th>Διάθεση Μέσω Ηλεκτρονικής Αίτησης</th>
                                <td>
                                    @if ($dataset->e_application)
                                        NAI
                                    @else
                                        ΟΧΙ
                                    @endif
                                </td>
                            </tr>
                            <tr>
                                <th>Διάθεση Μέσω Έντυπης Αίτησης</th>
                                <td>
                                    @if ($dataset->application)
                                        NAI
                                    @else
                                        ΟΧΙ
                                    @endif
                                </td>
                            </tr>
                        @endif
                        <tr>
                            <th>Εμπεριέχονται Προσωπικά Δεδομένα</th>
                            <td>
                                @if ($dataset->personaldata)
                                    NAI<br>
                                    Αιτιολογία: {{ $dataset->personaldata_info }}
                                @else
                                    ΟΧΙ
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <th>Συντρέχουν οι λοιποί περιορισμοί του Αρ.3 του Ν.4305/14</th>
                            <td>
                                @if ($dataset->restricted)
                                    NAI
                                @else
                                    ΟΧΙ
                                @endif
                            </td>
                        </tr>
                        @if ($dataset->restricted)
                            <tr>
                                <th>Αιτιολογία:</th>
                                <td>
                                    @foreach($dataset->restrictions as $restriction)
                                        {{ $restriction->name }}
                                        @if ($restriction->open && $dataset->restriction_other)
                                            : {{ $dataset->restriction_other }}
                                        @endif.
                                    @endforeach
                                </td>
                            </tr>
                        @endif
                        <tr>
                            <th>Διάθεση Μέσω Τελών</th>
                            <td>
                                @if ($dataset->fees)
                                    NAI
                                @else
                                    ΟΧΙ
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <th>Διάθεση Μέσω Αδειοδότησης</th>
                            <td>
                                @if ($dataset->licenced)
                                    NAI
                                @else
                                    ΟΧΙ
                                @endif
                            </td>
                        </tr>
                        @if ($dataset->licence_id)
                            <tr>
                                <th>Άδεια</th>
                                <td>{{ $dataset->licence->fullname }}</td>
                            </tr>
                        @endif
                        <tr>
                            <th>Σχόλια</th>
                            <td>{{ $dataset->comments }}</td>
                        </tr>
                        <tr>
                            <th>Υπηρεσία</th>
                            <td>{{ $dataset->unit->name }}</td>
                        </tr>
                        <tr>
                            <th>Τελευταία Ενημέρωση</th>
                            <td>Από {{ $dataset->user->name }},
                                στις {{ $dataset->updated_at->format('d-m-Y, H:i') }}</td>
                        </tr>
                    </table>
                </div>
                <div class="box-footer">
                    <div class="row">
                        <div class="col-sm-4">
                            <a href="{{ route('opendata.home') }}" class="btn btn-block btn-default">Επιστροφή</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('page-scripts')
    <!-- Load dataset form js -->
    <script src="{{ mix('js/opendata/dataset_form.js') }}" type="text/javascript"></script>
@endsection
