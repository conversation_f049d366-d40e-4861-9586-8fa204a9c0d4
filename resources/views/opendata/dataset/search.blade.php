@extends('layouts.opendata')

@section('contentheader_description')
  Σύνολα Δεδομένων
@endsection

@section('main-content')
  <div>
    <div class="row">
      <div class="col-sm-12">
        <div class="box box-primary">
          <div class="box-header">
            <h3 class="box-title">Αναζήτηση Συνόλων Δεδομένων</h3>
          </div>
          <form action="{{ route('opendata.dataset.search') }}" method="post">
            <div class="box-body">
              <div class="row">
                <div class="col-sm-6">
                  <div class="form-group">
                    <label for="name" class="control-label">Τίτλος:</label>
                    <input type="text" name="name" id="name" class="form-control"
                           value="{{ old('name', $dataset->name ?? null) }}">
                  </div>
                </div>
                <div class="col-sm-6">
                  <div class="form-group">
                    <label for="type_id" class="control-label">Τύπος:</label>
                    <select name="type_id" id="type_id" class="form-control select2">
                      <option value="">Επιλέξτε...</option>
                      @foreach(\App\Models\Opendata\Type::all() as $type)
                        <option
                          value="{{ $type->id }}" @selected(old('type_id', $dataset->type_id ?? null) == $type->id)>
                          {{ $type->name }}
                        </option>
                      @endforeach
                    </select>
                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col-xs-3">
                  <label>Διάθεση</label>
                </div>
                <div class="col-xs-9">
                  <label class="radio-inline">
                    <input
                      type="radio"
                      name="obtainable_status"
                      id="obtainable"
                      class="is-iCheck"
                      value="1"
                      @checked(old('obtainable_status', $dataset->obtainable_status ?? '') == 1)
                    >
                    ΝΑΙ
                  </label>
                  <label class="radio-inline">
                    <input
                      type="radio"
                      name="obtainable_status"
                      id="obtainable_not"
                      class="is-iCheck"
                      value="0"
                      @checked(old('obtainable_status', $dataset->obtainable_status ?? '') == 0)
                    >
                    ΟΧΙ
                  </label>
                </div>

                <div class="col-lg-12 col-xl-9 col-xl-offset-3" id="unobtainable_reasons"
                     style="display:none; margin-bottom:1em">
                  @foreach(App\Models\Opendata\UnobtainableReason::orderBy('open')->orderBy('name')->get() as $unobtainable_reason)
                    <label class="checkbox-inline">
                      <input
                        type="checkbox"
                        name="unobtainable_reasons[]"
                        id="unobtainable_reason_{{ $unobtainable_reason->id }}"
                        value="{{ $unobtainable_reason->id }}"
                        class="is-iCheck"
                        @checked(in_array($unobtainable_reason->id, old('unobtainable_reasons', $dataset->unobtainable_reasons->pluck('id')->toArray() ?? [])))
                      >
                      {{ $unobtainable_reason->name }}
                    </label>
                  @endforeach
                </div>

              </div>
              <div class="row">
                <div class="col-xs-3">
                  <label>Προσωπικά Δεδομένα</label>
                </div>
                <div class="col-xs-3">
                  <label class="checkbox-inline">
                    <input
                      type="checkbox"
                      name="personaldata"
                      id="personaldata"
                      class="is-iCheck"
                      value="1"
                      @checked(old('personaldata', $dataset->personaldata ?? false))
                    >
                    ΝΑΙ
                  </label>
                  <label class="checkbox-inline">
                    <input
                      type="checkbox"
                      name="personaldata_not"
                      id="personaldata_not"
                      class="is-iCheck"
                      value="1"
                      @checked(old('personaldata_not', $dataset->personaldata_not ?? false))
                    >
                    ΟΧΙ
                  </label>
                </div>
              </div>
              <div class="row">
                <div class="col-xs-3">
                  <label>Περιορισμοί Αρ.3 του Ν.4305/14</label>
                </div>
                <div class="col-xs-3">
                  <label class="radio-inline">
                    <input
                      type="radio"
                      name="restricted_status"
                      id="restricted"
                      class="is-iCheck"
                      value="1"
                      @checked(old('restricted_status', $dataset->restricted_status ?? '') == '1')
                    >
                    ΝΑΙ
                  </label>
                  <label class="radio-inline">
                    <input
                      type="radio"
                      name="restricted_status"
                      id="restricted_not"
                      class="is-iCheck"
                      value="0"
                      @checked(old('restricted_status', $dataset->restricted_status ?? '') == '0')
                    >
                    ΟΧΙ
                  </label>
                </div>
              </div>
              <div class="row">
                <div class="col-xs-3">
                  <label>Διάθεση με Τέλη</label>
                </div>
                <div class="col-xs-3">
                  <label class="radio-inline">
                    <input
                      type="radio"
                      name="fees_status"
                      id="fees"
                      class="is-iCheck"
                      value="1"
                      @checked(old('fees_status', $dataset->fees_status ?? '') == '1')
                    >
                    ΝΑΙ
                  </label>
                  <label class="radio-inline">
                    <input
                      type="radio"
                      name="fees_status"
                      id="fees_not"
                      class="is-iCheck"
                      value="0"
                      @checked(old('fees_status', $dataset->fees_status ?? '') == '0')
                    >
                    ΟΧΙ
                  </label>
                </div>
              </div>
              <div class="row">
                <div class="col-xs-3">
                  <label>Διάθεση με Αδειοδότηση</label>
                </div>
                <div class="col-xs-3">
                  <label class="radio-inline">
                    <input
                      type="radio"
                      name="licenced_status"
                      id="licenced"
                      class="is-iCheck"
                      value="1"
                      @checked(old('licenced_status', $dataset->licenced_status ?? '') == '1')
                    >
                    ΝΑΙ
                  </label>
                  <label class="radio-inline">
                    <input
                      type="radio"
                      name="licenced_status"
                      id="licenced_not"
                      class="is-iCheck"
                      value="0"
                      @checked(old('licenced_status', $dataset->licenced_status ?? '') == '0')
                    >
                    ΟΧΙ
                  </label>
                </div>
              </div>
              <br>
              <div class="row">
                <div class="col-sm-12">
                  <tree-view-input
                    input-name="units"
                    :allow-showing-departments="false"
                    :show-collapsed="true"
                    authorize-for="opendata"
                    :input-value="{{json_encode($filteredUnits)}}"
                  ></tree-view-input>
                  {{--                            @include('partials.directorateInput', ['job' => 'opendata.read'])--}}
                </div>
              </div>
            </div>
            <div class="box-footer">
              <div class="row">
                <div class="col-sm-4">
                  <a class="btn btn-default btn-block" href="{{ url('opendata') }}">Επιστροφή</a>
                </div>
                <div class="col-sm-4 col-sm-offset-4">
                  <button type="submit" class="btn btn-primary btn-block">Αναζήτηση</button>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
@endsection

@section('page-scripts')
  <!-- Load dataset search form js -->
  <script src="{{ mix('js/opendata/dataset_search.js') }}" type="text/javascript"></script>
  <script src="{{ mix('js/opendata/index.js') }}" type="text/javascript"></script>
@endsection
