@extends('layouts.contractuals')

@section('contentheader_description')
    Στατιστικά Front Office
@endsection

@section('main-content')
    <div class="row">
        <div class="col-sm-12">
            <form action="{{ route('contractuals.statistics.front-office') }}" method="GET">
                <div class="form-group">
                    <label for="contest__dropdown">Επιλογή διαγωνισμού</label>
                    <div class="row">
                        <div class="col-sm-3">
                            <select name="contest_id" id="contest__dropdown" class="form-control">
                                @foreach($contests as $contest)
                                    <option
                                        value="{{$contest->id}}"
                                        @if($contest->id == $contestId) selected @endif
                                    >
                                        {{$contest->name}}
                                        ({{$contest->start_date->format('d-m-Y')}}
                                        - {{$contest->end_date->format('d-m-Y')}}
                                        )
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-sm-3">
                            <button type="submit" class="btn btn-primary">Επιλογή</button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <br>
    <div class="row">
        <div class="col-md-3 col-sm-6 col-xs-12">
            <div class="info-box">
                <span class="info-box-icon bg-blue"><i class="fa fa-files-o"></i></span>
                <div class="info-box-content">
                    <span class="info-box-text">Συνολικές Αιτήσεις</span>
                    <span class="info-box-number">{{ number_format($totalApplications) }}</span>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 col-xs-12">
            <div class="info-box">
                <span class="info-box-icon bg-blue"><i class="fa fa-envelope-open-o"></i></span>
                <div class="info-box-content">
                    <span class="info-box-text">Υποβληθείσες Αιτήσεις</span>
                    <span class="info-box-number">{{ number_format($submittedApplications) }}</span>
                    @if($totalApplications > 0)
                        <small>Ποσοστό (<strong>{{ round($submittedApplications/$totalApplications, 2)*100 }}%</strong>)</small>
                    @endif
                </div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 col-xs-12">
            <div class="info-box">
                <span class="info-box-icon bg-blue"><i class="fa fa-user-o"></i></span>
                <div class="info-box-content">
                    <span class="info-box-text">Εγγεγραμμένοι χρήστες</span>
                    <span class="info-box-number">{{ number_format($totalUsers) }}</span>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 col-xs-12">
            <div class="info-box">
                <span class="info-box-icon bg-blue"><i class="fa fa-user-circle-o"></i></span>
                <div class="info-box-content">
                    <span class="info-box-text">Online χρήστες</span>
                    <span class="info-box-number">{{ number_format($currentOnlineUsers) }}</span>
                </div>
            </div>
        </div>
    </div>
    <br>
    <div class="row">
        <div class="col-sm-12">
            <canvas id="online-users-chart" height="48px"></canvas>
        </div>
    </div>
    <div class="row">
        <div class="col-sm-6">
            <canvas id="myChart1"></canvas>
        </div>
        <div class="col-sm-6">
            <canvas id="myChart2"></canvas>
        </div>
    </div>
    <br>
    <div class="row">
        <div class="col-sm-4">
            <div class="row">
                <div class="col-sm-12">
                    <div class="row">
                        <div class="col-sm-12">
                            <canvas id="myChart4"></canvas>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <canvas id="myChart5"></canvas>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <canvas id="myChart3"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-sm-8">
            <h6 class="text-center text-bold">Υποβληθείσες αιτήσεις ανά νομό</h6>
            <div id="chartdiv" style="width: 100%; height: 800px;"></div>
        </div>
    </div>
    <div class="row">
        <div class="col-sm-4">
            <a href="{{route('contractuals.contests.show', $contestId)}}" class="btn btn-default btn-block">
                <i class="fa fa-arrow-left"></i> Επιστροφή στην προβολή διαγωνισμού
            </a>
        </div>
    </div>
@endsection

@section('page-scripts')
    @parent
    <script src="https://cdn.amcharts.com/lib/5/index.js"></script>
    <script src="https://cdn.amcharts.com/lib/5/map.js"></script>
    <script src="{{mix('js/okxegeodata_greece4326.min.js')}}"></script>
    <script src="//cdn.amcharts.com/lib/5/themes/Animated.js"></script>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js" integrity="sha512-ElRFoEQdI5Ht6kZvyzXhYG9NqjtkmlkfYk0wr6wHxU9JEHakS7UJZNeml5ALk+8IKlU6jDgMabC3vkumRokgJA==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script
        src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
    <script>
        $(function () {
            /**
             * **************************************
             * Online users chart
             * **************************************
             */

            const data = {
                datasets: [{
                    label: 'Online χρήστες',
                    data: @json($onlineUsers),
                    backgroundColor: '#689eb0',
                    borderColor: '#689eb0',
                    borderWidth: 1,
                    fill: true,
                    tension: 0.4,
                }],
            };

            const config = {
                type: 'line',
                data,
                options: {
                    plugins: {
                        filler: {
                            propagate: true,
                        },
                    },
                    scales: {
                        x: {
                            type: 'time',
                            time: {
                                unit: 'minute',
                                displayFormats: {
                                    minute: 'dd-MMM HH:mm'
                                },
                                stepSize: 480
                            },
                            display: true
                        },
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            };
            const onlineUsersChart = new Chart(
                document.getElementById('online-users-chart'),
                config
            );

            /**
             * **************************************
             * Geodata chart: Applications per region
             * **************************************
             */
                // Create root and chart
            var root = am5.Root.new("chartdiv");
            // Set themes
            root.setThemes([
                am5themes_Animated.new(root)
            ]);
            var chart = root.container.children.push(
                am5map.MapChart.new(root, {
                    panX: "rotateX",
                    panY: "rotateY",
                    layout: root.horizontalLayout,
                    wheelY: "none"
                })
            );
            chart.events.on("wheel", function (ev) {
                if (ev.originalEvent.shiftKey) {
                    ev.originalEvent.preventDefault();
                    chart.set("wheelY", "zoom");
                } else {
                    chart.set("wheelY", "none");
                }
            });
            // Create polygon series
            var polygonSeries = chart.series.push(
                am5map.MapPolygonSeries.new(root, {
                    geoJSON: okxegeodata_greece4326,
                    valueField: "value",
                    calculateAggregates: true,
                })
            );
            polygonSeries.mapPolygons.template.setAll({
                stroke: am5.color(0xffffff),
                strokeWidth: 2,
                tooltipText: "{name}: {value}",
                fill: am5.color(0xd0d0d0),
                // interactive: true,
                // fillOpacity: 1
            });
            polygonSeries.set("heatRules", [{
                target: polygonSeries.mapPolygons.template,
                dataField: "value",
                min: am5.color(0xc4e0eb),
                max: am5.color(0x00283b),
                key: "fill"
            }]);

            // Legend
            polygonSeries.mapPolygons.template.events.on("pointerover", function (ev) {
                heatLegend.showValue(ev.target.dataItem.get("value"));
            });

            polygonSeries.data.setAll(@json($prefectureData));

            var heatLegend = chart.children.push(
                am5.HeatLegend.new(root, {
                    orientation: "vertical",
                    startColor: am5.color(0xc4e0eb),
                    endColor: am5.color(0x00283b),
                    startText: "Ελάχιστο",
                    endText: "Μέγιστο",
                    stepCount: 5
                })
            );

            heatLegend.startLabel.setAll({
                fontSize: 12,
                fill: heatLegend.get("startColor")
            });

            heatLegend.endLabel.setAll({
                fontSize: 12,
                fill: heatLegend.get("endColor")
            });

            // change this to template when possible
            polygonSeries.events.on("datavalidated", function () {
                heatLegend.set("startValue", polygonSeries.getPrivate("valueLow"));
                heatLegend.set("endValue", polygonSeries.getPrivate("valueHigh"));
            });

            /**
             * **************************************
             * Daily new/submitted applications
             * **************************************
             */
            const dailyPendingSubmittedApplicationData = {
                labels: @json($period),
                datasets: [
                    {
                        label: 'Νέες αιτήσεις',
                        data: @json($dailyTotalApplicationData),
                        backgroundColor: '#96bfcd',
                        borderColor: '#96bfcd',
                        borderWidth: 1,
                    },
                    {
                        label: 'Υποβληθείσες αιτήσεις',
                        data: @json($dailySubmittedApplicationData),
                        backgroundColor: '#3a7d93',
                        borderColor: '#3a7d93',
                        borderWidth: 1,
                    },
                ],
            };
            const myChart1 = new Chart(document.getElementById('myChart1'), {
                type: 'bar',
                data: dailyPendingSubmittedApplicationData,
                options: {
                    interaction: {
                        intersect: false,
                        mode: 'index',
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: 'Αριθμός νέων/υποβληθεισών αιτήσεων (ανά ημέρα)',
                        },
                    },
                    responsive: true,
                    scales: {
                        x: {
                            // stacked: true,
                            title: {
                                display: true,
                                text: 'Ημερομηνία',
                            },
                        },
                        y: {
                            beginAtZero: true,
                            // stacked: true,
                            title: {
                                display: true,
                                text: 'Αριθμός αιτήσεων',
                            },
                        },
                    },
                },
            });

            /**
             * **************************************
             * Accumulated submitted vs. total applications
             * **************************************
             */
            const accumulatedTotalSubmittedApplicationData = {
                labels: @json($period),
                datasets: [
                    {
                        label: 'Υποβληθείσες αιτήσεις',
                        data: @json($accumulatedSubmittedApplicationData),
                        backgroundColor: '#3a7d93',
                        borderColor: '#3a7d93',
                        borderWidth: 1,
                        fill: true,
                        tension: 0.4,
                    },
                    {
                        label: 'Συνολικές αιτήσεις',
                        data: @json($accumulatedTotalApplicationData),
                        backgroundColor: '#c4e0eb',
                        borderColor: '#c4e0eb',
                        borderWidth: 1,
                        fill: true,
                        tension: 0.4,
                    },
                ],
            };
            const myChart2 = new Chart(document.getElementById('myChart2'), {
                type: 'line',
                data: accumulatedTotalSubmittedApplicationData,
                options: {
                    interaction: {
                        intersect: false,
                        mode: 'index',
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: 'Αριθμός συνολικών/υποβληθεισών αιτήσεων (αθροιστικά)',
                        },
                        tooltip: {
                            callbacks: {
                                footer: (tooltipItems) => {
                                    let rem = tooltipItems[1].parsed.y - tooltipItems [0].parsed.y;

                                    return 'Εκκρεμείς αιτήσεις: ' + rem;
                                },
                            },
                        },
                    },
                    responsive: true,
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: 'Ημερομηνία',
                            },
                        },
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Αριθμός αιτήσεων',
                            },
                        },
                    },
                },
            });

            /**
             * **************************************
             * Total applications per specialization type
             * **************************************
             */
            const applicatioBySpecializationTypeData = {
                labels: ['ΠΕ', 'ΤΕ', 'ΔΕ', 'ΥΕ'],
                datasets: [
                    {
                        label: 'Υποβληθείσες αιτήσεις',
                        data: [@json($peApplications), @json($teApplications), @json($deApplications), @json($yeApplications)],
                        backgroundColor: ['#c4e0eb', '#adcfdc', '#689eb0', '#3a7d93'],
                    }
                ]
            }
            const myChart3 = new Chart(document.getElementById('myChart3'), {
                type: 'doughnut',
                data: applicatioBySpecializationTypeData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        title: {
                            display: true,
                            text: 'Υποβληθείσες αιτήσεις ανά κατηγορία εκπαίδευσης',
                        },
                        tooltip: {
                            callbacks: {
                                footer: (tooltipItems) => {
                                    const totalApplications = tooltipItems[0].dataset.data.reduce((acc, val) => acc + val, 0);
                                    const categoryApplications = tooltipItems[0].parsed
                                    return 'Ποσοστό ' + Math.round(categoryApplications / totalApplications * 100) + '%'
                                },
                            },
                        },
                    },
                },
            });

            /**
             * **************************************
             * Most popular
             * **************************************
             */
            const mostPopularUnits = {
                labels: @json($mostPopularUnitData['units']),
                datasets: [
                    {
                        label: 'ΥΕ',
                        data: @json($mostPopularUnitData['ye']),
                        backgroundColor: '#3a7d93',
                    },
                    {
                        label: 'ΔΕ',
                        data: @json($mostPopularUnitData['de']),
                        backgroundColor: '#689eb0',
                    },
                    {
                        label: 'ΤΕ',
                        data: @json($mostPopularUnitData['te']),
                        backgroundColor: '#adcfdc',
                    },
                    {
                        label: 'ΠΕ',
                        data: @json($mostPopularUnitData['pe']),
                        backgroundColor: '#c4e0eb',
                    },
                ],
            };
            const myChart4 = new Chart(document.getElementById('myChart4'), {
                type: 'bar',
                data: mostPopularUnits,
                options: {
                    interaction: {
                        intersect: false,
                        mode: 'index',
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: 'Υπηρεσίες με τις περισσότερες υποβληθείσες αιτήσεις',
                        },
                        tooltip: {
                            callbacks: {
                                footer: (tooltipItems) => {
                                    let sum = tooltipItems[0].parsed.y + tooltipItems [1].parsed.y + tooltipItems[2].parsed.y + tooltipItems[3].parsed.y;

                                    return 'Σύνολο: ' + sum;
                                },
                            },
                        },
                    },
                    responsive: true,
                    scales: {
                        x: {
                            stacked: true,
                            title: {
                                display: true,
                                text: 'Υπηρεσίες',
                            },
                        },
                        y: {
                            beginAtZero: true,
                            stacked: true,
                            title: {
                                display: true,
                                text: 'Αριθμός αιτήσεων',
                            },
                        },
                    },
                },
            });


            /**
             * **************************************
             * Least popular units
             * **************************************
             */
            const leastPopularUnits = {
                labels: @json($leastPopularUnitData['units']),
                datasets: [
                    {
                        label: 'ΥΕ',
                        data: @json($leastPopularUnitData['ye']),
                        backgroundColor: '#3a7d93',
                    },
                    {
                        label: 'ΔΕ',
                        data: @json($leastPopularUnitData['de']),
                        backgroundColor: '#689eb0',
                    },
                    {
                        label: 'ΤΕ',
                        data: @json($leastPopularUnitData['te']),
                        backgroundColor: '#adcfdc',
                    },
                    {
                        label: 'ΠΕ',
                        data: @json($leastPopularUnitData['pe']),
                        backgroundColor: '#c4e0eb',
                    },
                ],
            };
            const myChart5 = new Chart(document.getElementById('myChart5'), {
                type: 'bar',
                data: leastPopularUnits,
                options: {
                    interaction: {
                        intersect: false,
                        mode: 'index',
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: 'Υπηρεσίες με τις λιγότερες υποβληθείσες αιτήσεις',
                        },
                        tooltip: {
                            callbacks: {
                                footer: (tooltipItems) => {
                                    let sum = tooltipItems[0].parsed.y + tooltipItems [1].parsed.y + tooltipItems[2].parsed.y + tooltipItems[3].parsed.y;

                                    return 'Σύνολο: ' + sum;
                                },
                            },
                        },
                    },
                    responsive: true,
                    scales: {
                        x: {
                            stacked: true,
                            title: {
                                display: true,
                                text: 'Υπηρεσίες',
                            },
                        },
                        y: {
                            beginAtZero: true,
                            stacked: true,
                            title: {
                                display: true,
                                text: 'Αριθμός αιτήσεων',
                            },
                        },
                    },
                },
            });
        })
        ;
    </script>
@endsection
