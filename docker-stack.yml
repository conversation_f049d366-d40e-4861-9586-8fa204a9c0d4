version: "3.9"

services:

  web:
    image: ***********:5000/culturegr/apptree/web:latest
    build:
      context: ./
      dockerfile: ./docker/web/Dockerfile
      target: production-build
      args:
        NGINX_VERSION: ${DOCKER_NGINX_VERSION}
        NODE_VERSION: ${DOCKER_NODE_VERSION}
    ports:
      - "80:80"
      - "443:443"
#    healthcheck:
#      # Check that webserver is up and running
#      # indirectly checks that app and db
#      test: curl --fail -ILk localhost:80 || exit 1
#      interval: 30s
#      retries: 2
#      start_period: 30s
#      timeout: 5s
    networks:
      - frontend
    volumes:
      - /home/<USER>/ssl:/etc/nginx/ssl
      - /mnt/storage/data/apptree:/var/www/html/storage/app

  app:
    image: ***********:5000/culturegr/apptree/app:latest
    build:
      context: ./
      dockerfile: ./docker/app/Dockerfile
      target: production-build
      args:
        PHP_VERSION: ${DOCKER_PHP_VERSION}
        COMPOSER_VERSION: ${DOCKER_COMPOSER_VERSION}
        NODE_VERSION: ${DOCKER_NODE_VERSION}
        MYSQL_VERSION: ${DOCKER_MYSQL_VERSION}
      labels:
        - "commit.sha=${COMMIT_SHA:-}"
    ports:
      - "6001:6001"
    environment:
      APP_ENV: production
      APP_KEY_FILE: /run/secrets/app_key
      DB_USERNAME_FILE: /run/secrets/db_username
      DB_PASSWORD_FILE: /run/secrets/db_password
      LDAP_USERNAME_FILE: /run/secrets/ldap_username
      LDAP_PASSWORD_FILE: /run/secrets/ldap_password
      BUGSNAG_API_KEY_FILE: /run/secrets/bugsnag_api_key
      PUBLIC_CONTRACTUALS_API_KEY_FILE: /run/secrets/public_contractuals_api_key
      PUSHER_APP_KEY_FILE: /run/secrets/pusher_app_key
      PUSHER_APP_SECRET_FILE: /run/secrets/pusher_app_secret
      MAIL_PASSWORD_FILE: /run/secrets/mail_password
    secrets:
      - app_key
      - db_password
      - db_username
      - ldap_username
      - ldap_password
      - bugsnag_api_key
      - public_contractuals_api_key
      - pusher_app_key
      - pusher_app_secret
      - mail_password
#    healthcheck:
#      # Check that fileserver is up and running
#      test: ([ -d /var/www/html/storage/app/medialibrary/contractuals ]) || exit 1
#      interval: 30s
#      retries: 2
#      start_period: 30s
#      timeout: 5s
    deploy:
      resources:
        limits:
          cpus: '3.50'
          memory: 6500M
        reservations:
          cpus: '1'
          memory: 1000M
    networks:
      - frontend
      - backend
    volumes:
      - /mnt/storage/data/apptree:/var/www/html/storage/app
      - /mnt/backup_compass:/mnt/backup_compass
      - /home/<USER>/ssl:/etc/ssl/websockets:ro
      - /home/<USER>/ssl/certs:/usr/local/share/ca-certificates/custom:ro

  redis:
    image: "redis:7.2"
    command: redis-server --save 20 1 --loglevel warning --requirepass admin
    ports:
      - "6379:6379"
    networks:
      - backend
    volumes:
      - redis_data:/data


secrets:
  app_key:
    external: true
  db_password:
    external: true
  db_username:
    external: true
  ldap_username:
    external: true
  ldap_password:
    external: true
  bugsnag_api_key:
    external: true
  public_contractuals_api_key:
    external: true
  pusher_app_key:
    external: true
  pusher_app_secret:
    external: true
  mail_password:
    external: true

networks:
  frontend:
  backend:

volumes:
  redis_data:
    driver: local
