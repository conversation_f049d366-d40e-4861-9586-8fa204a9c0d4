{"name": "culturegr/apptree", "type": "project", "description": "Official web portal of Hellenic Ministry of Culture and Sports", "keywords": ["culturegr", "yppo", "dhd", "apptree"], "license": "proprietary", "require": {"php": "^8.4", "barryvdh/laravel-dompdf": "^v3.0", "bugsnag/bugsnag-laravel": "^2.22", "culturegr/custom-relation": "^1.4", "culturegr/filterer": "^2.2", "culturegr/presenter": "^1.6", "directorytree/ldaprecord-laravel": "^3.3", "guzzlehttp/guzzle": "^7.9", "h4cc/wkhtmltoimage-amd64": "^0.12", "h4cc/wkhtmltopdf-amd64": "^0.12", "laravel/fortify": "^1.24", "laravel/framework": "^12.0", "laravel/horizon": "^5.29", "laravel/passport": "^12.0", "laravel/scout": "^v10.0", "laravel/tinker": "^2.10", "lavary/laravel-menu": "^1.8", "maatwebsite/excel": "^3.1", "spatie/laravel-activitylog": "^4.8", "spatie/laravel-medialibrary": "^11.9", "staudenmeir/laravel-adjacency-list": "^v1.22"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.14", "fakerphp/faker": "^1.23", "friendsofphp/php-cs-fixer": "^3.64", "kubawerlos/php-cs-fixer-custom-fixers": "^3.22", "larastan/larastan": "^3.2", "laravel/legacy-factories": "^1.0", "laravel/pint": "^1.22", "mockery/mockery": "^1.4.2", "nunomaduro/collision": "^8.1", "phpunit/phpunit": "^11.0", "spatie/laravel-ignition": "^2.0"}, "config": {"preferred-install": "dist", "sort-packages": true, "optimize-autoloader": true}, "extra": {"laravel": {"dont-discover": []}}, "autoload": {"classmap": ["database"], "files": ["app/helpers.php"], "psr-4": {"App\\": "app/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}}