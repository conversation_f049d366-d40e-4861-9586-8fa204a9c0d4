<?php

namespace Database\Seeders;

use App\Models\Personnel\Employee;
use App\Models\Personnel\Position;
use App\Models\Personnel\Specialization;
use App\Models\Personnel\SpecializationType;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class PersonnelDataMigrationSeeder extends Seeder
{
/***
# Personnel data migration

1. Build and Run migration script to update table structure (2023_05_08_155213_update_personnel_tables.php)

2. Update from compass (simple data entry)
occupations.compass_id

```sql
UPDATE `personnel`.`occupations` SET `compass_id` = '01' WHERE (`id` = '1');
UPDATE `personnel`.`occupations` SET `compass_id` = '11' WHERE (`id` = '2');
```

3. Update specialization_types

```sql
UPDATE `personnel`.`specialization_types` SET `compass_id` = '1' WHERE (`id` = '1');
UPDATE `personnel`.`specialization_types` SET `compass_id` = '5' WHERE (`id` = '2');
UPDATE `personnel`.`specialization_types` SET `compass_id` = '2' WHERE (`id` = '3');
UPDATE `personnel`.`specialization_types` SET `compass_id` = '3' WHERE (`id` = '4');
UPDATE `personnel`.`specialization_types` SET `compass_id` = '4' WHERE (`id` = '5');
UPDATE `personnel`.`specialization_types` SET `compass_id` = '1' WHERE (`id` = '6');
UPDATE `personnel`.`specialization_types` SET `compass_id` = '5' WHERE (`id` = '7');
UPDATE `personnel`.`specialization_types` SET `compass_id` = '2' WHERE (`id` = '8');
UPDATE `personnel`.`specialization_types` SET `compass_id` = '3' WHERE (`id` = '9');
UPDATE `personnel`.`specialization_types` SET `compass_id` = '4' WHERE (`id` = '10');

```

4. Update based on data
positions.occupation_id

```sql
update positions p
inner join specializations s on p.specialization_id = s.id
set p.occupation_id = s.occupation_id;
```

5. Update based on data
employees.occupation_id

```sql
update employees e
inner join specializations s on e.specialization_id = s.id
set e.occupation_id = s.occupation_id;
```

5. Update specializations.compass_specialization_type_id
 **NOT NEEDED?**

```sql
update specializations s
inner join specialization_types st
on s.specialization_type_id = st.id
set s.compass_specialization_type_id = st.compass_id;
```

6. Insert new specialization types
```sql
INSERT INTO `personnel`.`specialization_types` (`name`, `compass_id`) VALUES ('ΠΕ', '1');
INSERT INTO `personnel`.`specialization_types` (`name`, `compass_id`) VALUES ('ΤΕ', '2');
INSERT INTO `personnel`.`specialization_types` (`name`, `compass_id`) VALUES ('ΔΕ', '3');
INSERT INTO `personnel`.`specialization_types` (`name`, `compass_id`) VALUES ('ΥΕ', '4');
INSERT INTO `personnel`.`specialization_types` (`name`, `compass_id`) VALUES ('ΕΕΠ', '5');
```

7. Create new specializations.

field
name
fullname
compass_id
specialization_type_id
specialization_parent_id
compass_specialization_type_id
compass_specialization_parent_id


insert into hroffice_specialization
insert into department_board_specialization
update positions.compass_specialization_id, positions.compass_specialization_parent_id

8. Fill in hroffice_specialization

9. Fill in department_board_specialization
*/

    public function run()
    {
        // occupations:
        $monimoi = 1;
        $idax = 2;

        $specializationTypes = SpecializationType::whereNull('compass_id_old')->get();
        $specTypes = $specializationTypes->groupBy('compass_id')->toArray();

        // Αντιστοίχηση, key => value, key = new_compass_id
        // Parents first
        $specs = [
            // ΕΙΔΙΚΟΤΗΤΕΣ ΠΕ
            '102' => [
                'field' => 'ΠΕ ΑΡΧΑΙΟΛΟΓΩΝ',
                'name' => 'ΠΕ ΑΡΧΑΙΟΛΟΓΩΝ',
                'fullname' => 'ΠΕ ΑΡΧΑΙΟΛΟΓΩΝ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '1',
                'old_compass_ids' => ['Α02'],
                'old_compass_ids_idax' => [],
            ],
            '1021' => [
                'field' => 'ΠΕ ΑΡΧΑΙΟΛΟΓΩΝ',
                'name' => 'ΑΡΧΑΙΟΒΟΤΑΝΟΛΟΓΩΝ',
                'fullname' => 'ΠΕ ΑΡΧΑΙΟΛΟΓΩΝ ΑΡΧΑΙΟΒΟΤΑΝΟΛΟΓΩΝ',
                'compass_specialization_parent_id' => '102',
                'compass_specialization_type_id' => '1',
                'old_compass_ids' => ['Α029'],
                'old_compass_ids_idax' => ['Β029'],
            ],
            '1022' => [
                'field' => 'ΠΕ ΑΡΧΑΙΟΛΟΓΩΝ',
                'name' => 'ΑΡΧΑΙΟΖΩΟΛΟΓΩΝ',
                'fullname' => 'ΠΕ ΑΡΧΑΙΟΛΟΓΩΝ ΑΡΧΑΙΟΖΩΟΛΟΓΩΝ',
                'compass_specialization_parent_id' => '102',
                'compass_specialization_type_id' => '1',
                'old_compass_ids' => ['Α028'],
                'old_compass_ids_idax' => ['Β028'],
            ],
            '1023' => [
                'field' => 'ΠΕ ΑΡΧΑΙΟΛΟΓΩΝ',
                'name' => 'ΒΥΖΑΝΤΙΝΩΝ-ΜΕΤΑΒΥΖΑΝΤΙΝΩΝ',
                'fullname' => 'ΠΕ ΑΡΧΑΙΟΛΟΓΩΝ ΒΥΖΑΝΤΙΝΩΝ-ΜΕΤΑΒΥΖΑΝΤΙΝΩΝ',
                'compass_specialization_parent_id' => '102',
                'compass_specialization_type_id' => '1',
                'old_compass_ids' => ['Α022'],
                'old_compass_ids_idax' => ['Β022'],
            ],
            '1024' => [
                'field' => 'ΠΕ ΑΡΧΑΙΟΛΟΓΩΝ',
                'name' => 'ΕΠΙΓΡΑΦΟΛΟΓΩΝ',
                'fullname' => 'ΠΕ ΑΡΧΑΙΟΛΟΓΩΝ ΕΠΙΓΡΑΦΟΛΟΓΩΝ',
                'compass_specialization_parent_id' => '102',
                'compass_specialization_type_id' => '1',
                'old_compass_ids' => ['Α025'],
                'old_compass_ids_idax' => [],
            ],
            '1025' => [
                'field' => 'ΠΕ ΑΡΧΑΙΟΛΟΓΩΝ',
                'name' => 'ΙΣΤΟΡΙΚΩΝ ΤΕΧΝΗΣ',
                'fullname' => 'ΠΕ ΑΡΧΑΙΟΛΟΓΩΝ ΙΣΤΟΡΙΚΩΝ ΤΕΧΝΗΣ',
                'compass_specialization_parent_id' => '102',
                'compass_specialization_type_id' => '1',
                'old_compass_ids' => ['Α023'],
                'old_compass_ids_idax' => ['Β023'],
            ],
            '1026' => [
                'field' => 'ΠΕ ΑΡΧΑΙΟΛΟΓΩΝ',
                'name' => 'ΜΟΥΣΕΙΟΛΟΓΩΝ',
                'fullname' => 'ΠΕ ΑΡΧΑΙΟΛΟΓΩΝ ΜΟΥΣΕΙΟΛΟΓΩΝ',
                'compass_specialization_parent_id' => '102',
                'compass_specialization_type_id' => '1',
                'old_compass_ids' => ['Α024'],
                'old_compass_ids_idax' => ['Β024'],
            ],
            '1027' => [
                'field' => 'ΠΕ ΑΡΧΑΙΟΛΟΓΩΝ',
                'name' => 'ΝΟΜΙΣΜΑΤΟΛΟΓΩΝ',
                'fullname' => 'ΠΕ ΑΡΧΑΙΟΛΟΓΩΝ ΝΟΜΙΣΜΑΤΟΛΟΓΩΝ',
                'compass_specialization_parent_id' => '102',
                'compass_specialization_type_id' => '1',
                'old_compass_ids' => ['Α026'],
                'old_compass_ids_idax' => ['Β026'],
            ],
            '1028' => [
                'field' => 'ΠΕ ΑΡΧΑΙΟΛΟΓΩΝ',
                'name' => 'ΠΟΛΙΤΙΣΤΙΚΗΣ ΔΙΑΧΕΙΡΙΣΗΣ',
                'fullname' => 'ΠΕ ΑΡΧΑΙΟΛΟΓΩΝ ΠΟΛΙΤΙΣΤΙΚΗΣ ΔΙΑΧΕΙΡΙΣΗΣ',
                'compass_specialization_parent_id' => '102',
                'compass_specialization_type_id' => '1',
                'old_compass_ids' => ['Α027'],
                'old_compass_ids_idax' => ['Β027'],
            ],
            '1029' => [
                'field' => 'ΠΕ ΑΡΧΑΙΟΛΟΓΩΝ',
                'name' => 'ΠΡΟΙΣΤΟΡΙΚΩΝ-ΚΛΑΣΙΚΩΝ',
                'fullname' => 'ΠΕ ΑΡΧΑΙΟΛΟΓΩΝ ΠΡΟΙΣΤΟΡΙΚΩΝ-ΚΛΑΣΙΚΩΝ',
                'compass_specialization_parent_id' => '102',
                'compass_specialization_type_id' => '1',
                'old_compass_ids' => ['Α021'],
                'old_compass_ids_idax' => ['Β021'],
            ],
            '103' => [
                'field' => 'ΠΕ ΑΡΧΕΙΟΝΟΜΩΝ ΚΑΙ ΒΙΒΛΙΟΘΗΚΟΝΟΜΩΝ',
                'name' => 'ΠΕ ΑΡΧΕΙΟΝΟΜΩΝ ΚΑΙ ΒΙΒΛΙΟΘΗΚΟΝΟΜΩΝ',
                'fullname' => 'ΠΕ ΑΡΧΕΙΟΝΟΜΩΝ ΚΑΙ ΒΙΒΛΙΟΘΗΚΟΝΟΜΩΝ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '1',
                'old_compass_ids' => [],
                'old_compass_ids_idax' => [],
            ],
            '1031' => [
                'field' => 'ΠΕ ΑΡΧΕΙΟΝΟΜΩΝ ΚΑΙ ΒΙΒΛΙΟΘΗΚΟΝΟΜΩΝ',
                'name' => 'ΠΕ ΒΙΒΛΙΟΘΗΚΟΝΟΜΩΝ',
                'fullname' => 'ΠΕ ΑΡΧΕΙΟΝΟΜΩΝ ΚΑΙ ΒΙΒΛΙΟΘΗΚΟΝΟΜΩΝ - ΒΙΒΛΙΟΘΗΚΟΝΟΜΩΝ',
                'compass_specialization_parent_id' => '103',
                'compass_specialization_type_id' => '1',
                'old_compass_ids' => ['Α0Δ'],
                'old_compass_ids_idax' => ['Β0Ψ'],
            ],
            '106' => [
                'field' => 'ΠΕ ΓΕΩΤΕΧΝΙΚΩΝ',
                'name' => 'ΠΕ ΓΕΩΤΕΧΝΙΚΩΝ',
                'fullname' => 'ΠΕ ΓΕΩΤΕΧΝΙΚΩΝ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '1',
                'old_compass_ids' => [],
                'old_compass_ids_idax' => [],
            ],
            '1061' => [
                'field' => 'ΠΕ ΓΕΩΤΕΧΝΙΚΩΝ',
                'name' => 'ΠΕ ΓΕΩΛΟΓΩΝ',
                'fullname' => 'ΠΕ ΓΕΩΤΕΧΝΙΚΩΝ - ΓΕΩΛΟΓΩΝ',
                'compass_specialization_parent_id' => '106',
                'compass_specialization_type_id' => '1',
                'old_compass_ids' => ['Α042'],
                'old_compass_ids_idax' => ['Β0Β'],
            ],
            '1062' => [
                'field' => 'ΠΕ ΓΕΩΤΕΧΝΙΚΩΝ',
                'name' => 'ΠΕ ΓΕΩΠΟΝΩΝ',
                'fullname' => 'ΠΕ ΓΕΩΤΕΧΝΙΚΩΝ - ΓΕΩΠΟΝΩΝ',
                'compass_specialization_parent_id' => '106',
                'compass_specialization_type_id' => '1',
                'old_compass_ids' => ['Α041'],
                'old_compass_ids_idax' => ['Β0Γ'],
            ],
            '108' => [
                'field' => 'ΠΕ ΔΙΟΙΚΗΤΙΚΟΥ-ΟΙΚΟΝΟΜΙΚΟΥ',
                'name' => 'ΠΕ ΔΙΟΙΚΗΤΙΚΟΥ-ΟΙΚΟΝΟΜΙΚΟΥ',
                'fullname' => 'ΠΕ ΔΙΟΙΚΗΤΙΚΟΥ-ΟΙΚΟΝΟΜΙΚΟΥ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '1',
                'old_compass_ids' => ['Α01'],
                'old_compass_ids_idax' => ['Β0Ε'],
            ],
            '1081' => [
                'field' => 'ΠΕ ΔΙΟΙΚΗΤΙΚΟΥ-ΟΙΚΟΝΟΜΙΚΟΥ',
                'name' => 'ΠΕ ΔΙΟΙΚΗΤΙΚΟΥ (ΝΟΜΙΚΩΝ)',
                'fullname' => 'ΠΕ ΔΙΟΙΚΗΤΙΚΟΥ-ΟΙΚΟΝΟΜΙΚΟΥ - ΔΙΟΙΚΗΤΙΚΟΥ (ΝΟΜΙΚΩΝ)',
                'compass_specialization_parent_id' => '108',
                'compass_specialization_type_id' => '1',
                'old_compass_ids' => ['Α0Η'],
                'old_compass_ids_idax' => [],
            ],
            '109' => [
                'field' => 'ΠΕ ΕΠΙΚΟΙΝΩΝΙΑΣ, ΕΝΗΜΕΡΩΣΗΣ ΚΑΙ ΔΗΜΟΣΙΩΝ ΣΧΕΣΕΩΝ',
                'name' => 'ΠΕ ΕΠΙΚΟΙΝΩΝΙΑΣ, ΕΝΗΜΕΡΩΣΗΣ ΚΑΙ ΔΗΜΟΣΙΩΝ ΣΧΕΣΕΩΝ',
                'fullname' => 'ΠΕ ΕΠΙΚΟΙΝΩΝΙΑΣ, ΕΝΗΜΕΡΩΣΗΣ ΚΑΙ ΔΗΜΟΣΙΩΝ ΣΧΕΣΕΩΝ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '1',
                'old_compass_ids' => ['Α054'],
                'old_compass_ids_idax' => ['Β0Ζ'],
            ],
            '111' => [
                'field' => 'ΠΕ ΘΕΑΤΡΙΚΩΝ ΣΠΟΥΔΩΝ',
                'name' => 'ΠΕ ΘΕΑΤΡΙΚΩΝ ΣΠΟΥΔΩΝ',
                'fullname' => 'ΠΕ ΘΕΑΤΡΙΚΩΝ ΣΠΟΥΔΩΝ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '1',
                'old_compass_ids' => ['Α055'],
                'old_compass_ids_idax' => [],
            ],
            '112' => [
                'field' => 'ΠΕ ΙΣΤΟΡΙΚΩΝ',
                'name' => 'ΠΕ ΙΣΤΟΡΙΚΩΝ',
                'fullname' => 'ΠΕ ΙΣΤΟΡΙΚΩΝ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '1',
                'old_compass_ids' => [],
                'old_compass_ids_idax' => ['Β0Ξ'],
            ],
            '1121' => [
                'field' => 'ΠΕ ΙΣΤΟΡΙΚΩΝ',
                'name' => 'ΠΕ ΙΣΤΟΡΙΚΩΝ ΤΕΧΝΗΣ',
                'fullname' => 'ΠΕ ΙΣΤΟΡΙΚΩΝ - ΙΣΤΟΡΙΚΩΝ ΤΕΧΝΗΣ',
                'compass_specialization_parent_id' => '112',
                'compass_specialization_type_id' => '1',
                'old_compass_ids' => [],
                'old_compass_ids_idax' => [],
            ],
            '113' => [
                'field' => 'ΠΕ ΚΑΛΩΝ ΤΕΧΝΩΝ',
                'name' => 'ΠΕ ΚΑΛΩΝ ΤΕΧΝΩΝ',
                'fullname' => 'ΠΕ ΚΑΛΩΝ ΤΕΧΝΩΝ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '1',
                'old_compass_ids' => ['Α05Α'],
                'old_compass_ids_idax' => [],
            ],
            '1131' => [
                'field' => 'ΠΕ ΚΑΛΩΝ ΤΕΧΝΩΝ',
                'name' => 'ΠΕ ΓΛΥΠΤΩΝ',
                'fullname' => 'ΠΕ ΚΑΛΩΝ ΤΕΧΝΩΝ - ΓΛΥΠΤΩΝ',
                'compass_specialization_parent_id' => '113',
                'compass_specialization_type_id' => '1',
                'old_compass_ids' => ['Α083'],
                'old_compass_ids_idax' => ['Β0Δ'],
            ],
            '1132' => [
                'field' => 'ΠΕ ΚΑΛΩΝ ΤΕΧΝΩΝ',
                'name' => 'ΠΕ ΖΩΓΡΑΦΩΝ',
                'fullname' => 'ΠΕ ΚΑΛΩΝ ΤΕΧΝΩΝ - ΖΩΓΡΑΦΩΝ',
                'compass_specialization_parent_id' => '113',
                'compass_specialization_type_id' => '1',
                'old_compass_ids' => ['Α082'],
                'old_compass_ids_idax' => ['Β0Η'],
            ],
            '1133' => [
                'field' => 'ΠΕ ΚΑΛΩΝ ΤΕΧΝΩΝ',
                'name' => 'ΠΕ ΧΑΡΑΚΤΩΝ',
                'fullname' => 'ΠΕ ΚΑΛΩΝ ΤΕΧΝΩΝ - ΧΑΡΑΚΤΩΝ',
                'compass_specialization_parent_id' => '113',
                'compass_specialization_type_id' => '1',
                'old_compass_ids' => ['Α081'],
                'old_compass_ids_idax' => [],
            ],
            '114' => [
                'field' => 'ΠΕ ΚΙΝΗΜΑΤΟΓΡΑΦΟΥ',
                'name' => 'ΠΕ ΚΙΝΗΜΑΤΟΓΡΑΦΟΥ',
                'fullname' => 'ΠΕ ΚΙΝΗΜΑΤΟΓΡΑΦΟΥ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '1',
                'old_compass_ids' => ['Α056'],
                'old_compass_ids_idax' => [],
            ],
            '118' => [
                'field' => 'ΠΕ ΛΑΟΓΡΑΦΩΝ-ΚΟΙΝΩΝΙΚΩΝ ΑΝΘΡΩΠΟΛΟΓΩΝ-ΕΘΝΟΛΟΓΩΝ',
                'name' => 'ΠΕ ΛΑΟΓΡΑΦΩΝ-ΚΟΙΝΩΝΙΚΩΝ ΑΝΘΡΩΠΟΛΟΓΩΝ-ΕΘΝΟΛΟΓΩΝ',
                'fullname' => 'ΠΕ ΛΑΟΓΡΑΦΩΝ-ΚΟΙΝΩΝΙΚΩΝ ΑΝΘΡΩΠΟΛΟΓΩΝ-ΕΘΝΟΛΟΓΩΝ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '1',
                'old_compass_ids' => ['Α09'],
                'old_compass_ids_idax' => ['Β0Ο'],
            ],
            '120' => [
                'field' => 'ΠΕ ΜΕΤΑΦΡΑΣΤΩΝ-ΔΙΕΡΜΗΝΕΩΝ',
                'name' => 'ΠΕ ΜΕΤΑΦΡΑΣΤΩΝ-ΔΙΕΡΜΗΝΕΩΝ',
                'fullname' => 'ΠΕ ΜΕΤΑΦΡΑΑΣΤΩΝ-ΔΙΕΡΜΗΝΕΩΝ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '1',
                'old_compass_ids' => ['Α0Γ'],
                'old_compass_ids_idax' => ['Β0Π'],
            ],
            '121' => [
                'field' => 'ΠΕ ΜΗΧΑΝΙΚΩΝ',
                'name' => 'ΠΕ ΜΗΧΑΝΙΚΩΝ',
                'fullname' => 'ΠΕ ΜΗΧΑΝΙΚΩΝ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '1',
                'old_compass_ids' => [],
                'old_compass_ids_idax' => [],
            ],
            '1211' => [
                'field' => 'ΠΕ ΜΗΧΑΝΙΚΩΝ',
                'name' => 'ΠΕ ΑΓΡΟΝΟΜΩΝ ΤΟΠΟΓΡΑΦΩΝ ΜΗΧΑΝΙΚΩΝ',
                'fullname' => 'ΠΕ ΜΗΧΑΝΙΚΩΝ - ΑΓΡΟΝΟΜΩΝ ΤΟΠΟΓΡΑΦΩΝ ΜΗΧΑΝΙΚΩΝ',
                'compass_specialization_parent_id' => '121',
                'compass_specialization_type_id' => '1',
                'old_compass_ids' => ['Α036'],
                'old_compass_ids_idax' => ['Β0Μ'],
            ],
            '1212' => [
                'field' => 'ΠΕ ΜΗΧΑΝΙΚΩΝ',
                'name' => 'ΠΕ ΑΡΧΙΤΕΚΤΟΝΩΝ ΜΗΧΑΝΙΚΩΝ',
                'fullname' => 'ΠΕ ΜΗΧΑΝΙΚΩΝ - ΑΡΧΙΤΕΚΤΟΝΩΝ ΜΗΧΑΝΙΚΩΝ',
                'compass_specialization_parent_id' => '121',
                'compass_specialization_type_id' => '1',
                'old_compass_ids' => ['Α031'],
                'old_compass_ids_idax' => ['Β0Α'],
            ],
            '1213' => [
                'field' => 'ΠΕ ΜΗΧΑΝΙΚΩΝ',
                'name' => 'ΠΕ ΗΛΕΚΤΡΟΛΟΓΩΝ ΜΗΧΑΝΙΚΩΝ',
                'fullname' => 'ΠΕ ΜΗΧΑΝΙΚΩΝ - ΗΛΕΚΤΡΟΛΟΓΩΝ ΜΗΧΑΝΙΚΩΝ',
                'compass_specialization_parent_id' => '121',
                'compass_specialization_type_id' => '1',
                'old_compass_ids' => ['Α033'],
                'old_compass_ids_idax' => ['Β0Θ'],
            ],
            '1215' => [
                'field' => 'ΠΕ ΜΗΧΑΝΙΚΩΝ',
                'name' => 'ΠΕ ΜΗΧΑΝΙΚΩΝ ΕΠΙΣΤΗΜΗΣ ΤΩΝ ΥΛΙΚΩΝ',
                'fullname' => 'ΠΕ ΜΗΧΑΝΙΚΩΝ - ΜΗΧΑΝΙΚΩΝ ΕΠΙΣΤΗΜΗΣ ΤΩΝ ΥΛΙΚΩΝ',
                'compass_specialization_parent_id' => '121',
                'compass_specialization_type_id' => '1',
                'old_compass_ids' => ['Α038'],
                'old_compass_ids_idax' => [],
            ],
            '1216' => [
                'field' => 'ΠΕ ΜΗΧΑΝΙΚΩΝ',
                'name' => 'ΠΕ ΜΗΧΑΝΙΚΩΝ ΜΕΤΑΛΛΕΙΩΝ ΚΑΙ ΜΕΤΑΛΛΟΥΡΓΩΝ ΜΗΧΑΝΙΚΩΝ',
                'fullname' => 'ΠΕ ΜΗΧΑΝΙΚΩΝ - ΜΗΧΑΝΙΚΩΝ ΜΕΤΑΛΛΕΙΩΝ ΚΑΙ ΜΕΤΑΛΛΟΥΡΓΩΝ ΜΗΧΑΝΙΚΩΝ',
                'compass_specialization_parent_id' => '121',
                'compass_specialization_type_id' => '1',
                'old_compass_ids' => ['Α037'],
                'old_compass_ids_idax' => [],
            ],
            '1217' => [
                'field' => 'ΠΕ ΜΗΧΑΝΙΚΩΝ',
                'name' => 'ΠΕ ΜΗΧΑΝΙΚΩΝ ΠΕΡΙΒΑΛΛΟΝΤΟΣ',
                'fullname' => 'ΠΕ ΜΗΧΑΝΙΚΩΝ - ΜΗΧΑΝΙΚΩΝ ΠΕΡΙΒΑΛΛΟΝΤΟΣ',
                'compass_specialization_parent_id' => '121',
                'compass_specialization_type_id' => '1',
                'old_compass_ids' => [],
                'old_compass_ids_idax' => [],
            ],
            '1218' => [
                'field' => 'ΠΕ ΜΗΧΑΝΙΚΩΝ',
                'name' => 'ΠΕ ΜΗΧΑΝΟΛΟΓΩΝΜΗΧΑΝΙΚΩΝ',
                'fullname' => 'ΠΕ ΜΗΧΑΝΙΚΩΝ - ΜΗΧΑΝΟΛΟΓΩΝ ΜΗΧΑΝΙΚΩΝ',
                'compass_specialization_parent_id' => '121',
                'compass_specialization_type_id' => '1',
                'old_compass_ids' => ['Α034'],
                'old_compass_ids_idax' => ['Β0Ι'],
            ],
            '1219' => [
                'field' => 'ΠΕ ΜΗΧΑΝΙΚΩΝ',
                'name' => 'ΠΕ ΠΟΛΙΤΙΚΩΝ ΜΗΧΑΝΙΚΩΝ',
                'fullname' => 'ΠΕ ΜΗΧΑΝΙΚΩΝ - ΠΟΛΙΤΙΚΩΝ ΜΗΧΑΝΙΚΩΝ',
                'compass_specialization_parent_id' => '121',
                'compass_specialization_type_id' => '1',
                'old_compass_ids' => ['Α032'],
                'old_compass_ids_idax' => ['Β0Λ'],
            ],
            '121Α' => [
                'field' => 'ΠΕ ΜΗΧΑΝΙΚΩΝ',
                'name' => 'ΠΕ ΧΗΜΙΚΩΝ ΜΗΧΑΝΙΚΩΝ',
                'fullname' => 'ΠΕ ΜΗΧΑΝΙΚΩΝ - ΧΗΜΙΚΩΝ ΜΗΧΑΝΙΚΩΝ',
                'compass_specialization_parent_id' => '121',
                'compass_specialization_type_id' => '1',
                'old_compass_ids' => ['Α035'],
                'old_compass_ids_idax' => ['Β0Ν'],
            ],
            '122' => [
                'field' => 'ΠΕ ΜΟΥΣΕΙΟΛΟΓΩΝ',
                'name' => 'ΠΕ ΜΟΥΣΕΙΟΛΟΓΩΝ',
                'fullname' => 'ΠΕ ΜΟΥΣΕΙΟΛΟΓΩΝ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '1',
                'old_compass_ids' => ['Α057'],
                'old_compass_ids_idax' => ['Β04'],
            ],
            '123' => [
                'field' => 'ΠΕ ΜΟΥΣΙΚΗΣ ΕΠΙΣΤΗΜΗΣ ΚΑΙ ΤΕΧΝΗΣ',
                'name' => 'ΠΕ ΜΟΥΣΙΚΗΣ ΕΠΙΣΤΗΜΗΣ ΚΑΙ ΤΕΧΝΗΣ',
                'fullname' => 'ΠΕ ΜΟΥΣΙΚΗΣ ΕΠΙΣΤΗΜΗΣ ΚΑΙ ΤΕΧΝΗΣ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '1',
                'old_compass_ids' => [],
                'old_compass_ids_idax' => [],
            ],
            '1231' => [
                'field' => 'ΠΕ ΜΟΥΣΙΚΗΣ ΕΠΙΣΤΗΜΗΣ ΚΑΙ ΤΕΧΝΗΣ',
                'name' => 'ΠΕ ΜΟΥΣΙΚΟΛΟΓΩΝ',
                'fullname' => 'ΠΕ ΜΟΥΣΙΚΗΣ ΕΠΙΣΤΗΜΗΣ ΚΑΙ ΤΕΧΝΗΣ - ΜΟΥΣΙΚΟΛΟΓΩΝ',
                'compass_specialization_parent_id' => '123',
                'compass_specialization_type_id' => '1',
                'old_compass_ids' => ['Α053', 'Ά059'],
                'old_compass_ids_idax' => ['Β07'],
            ],
            '126' => [
                'field' => 'ΠΕ ΠΑΙΔΑΓΩΓΩΝ',
                'name' => 'ΠΕ ΠΑΙΔΑΓΩΓΩΝ',
                'fullname' => 'ΠΕ ΠΑΙΔΑΓΟΓΩΝ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '1',
                'old_compass_ids' => [],
                'old_compass_ids_idax' => ['Β05'],
            ],
            '127' => [
                'field' => 'ΠΕ ΠΕΡΙΒΑΛΛΟΝΤΟΣ',
                'name' => 'ΠΕ ΠΕΡΙΒΑΛΛΟΝΤΟΣ',
                'fullname' => 'ΠΕ ΠΕΡΙΒΑΛΛΟΝΤΟΣ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '1',
                'old_compass_ids' => ['Α0Α1', 'Α0Α3', 'Α0Α4', 'Α0Α5', 'Α0Α6', 'Α0Α7', 'Α0Α8', 'Α0Α9', 'Α0ΑΑ', 'Α0ΑΓ'],
                'old_compass_ids_idax' => ['Β0Υ', 'Β0Φ'],
            ],
            '1271' => [
                'field' => 'ΠΕ ΠΕΡΙΒΑΛΛΟΝΤΟΣ',
                'name' => 'ΠΕ ΓΕΩΓΡΑΦΙΑΣ',
                'fullname' => 'ΠΕ ΠΕΡΙΒΑΛΛΟΝΤΟΣ - ΓΕΩΓΡΑΦΙΑΣ',
                'compass_specialization_parent_id' => '127',
                'compass_specialization_type_id' => '1',
                'old_compass_ids' => ['Α0ΑΒ'],
                'old_compass_ids_idax' => [],
            ],
            '128' => [
                'field' => 'ΠΕ ΠΛΗΡΟΦΟΡΙΚΗΣ',
                'name' => 'ΠΕ ΠΛΗΡΟΦΟΡΙΚΗΣ',
                'fullname' => 'ΠΕ ΠΛΗΡΟΦΟΡΙΚΗΣ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '1',
                'old_compass_ids' => [],
                'old_compass_ids_idax' => [],
            ],
            '1283' => [
                'field' => 'ΠΕ ΠΛΗΡΟΦΟΡΙΚΗΣ',
                'name' => 'ΠΕ ΠΛΗΡΟΦΟΡΙΚΗΣ (SOFTWARE-HARDWARE)',
                'fullname' => 'ΠΕ ΠΛΗΡΟΦΟΡΙΚΗΣ - ΠΛΗΡΟΦΟΡΙΚΗΣ (SOFTWARE-HARDWARE)',
                'compass_specialization_parent_id' => '128',
                'compass_specialization_type_id' => '1',
                'old_compass_ids' => ['Α0Β'],
                'old_compass_ids_idax' => ['Β0Ρ'],
            ],
            '129' => [
                'field' => 'ΠΕ ΠΟΛΙΤΙΣΤΙΚΗΣ ΔΙΑΧΕΙΡΙΣΗΣ',
                'name' => 'ΠΕ ΠΟΛΙΤΙΣΤΙΚΗΣ ΔΙΑΧΕΙΡΙΣΗΣ',
                'fullname' => 'ΠΕ ΠΟΛΙΤΙΣΤΙΚΗΣ ΔΙΑΧΕΙΡΙΣΗΣ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '1',
                'old_compass_ids' => ['Α051'],
                'old_compass_ids_idax' => [],
            ],
            '130' => [
                'field' => 'ΠΕ ΣΤΑΤΙΣΤΙΚΩΝ',
                'name' => 'ΠΕ ΣΤΑΤΙΣΤΙΚΩΝ',
                'fullname' => 'ΠΕ ΣΤΑΤΙΣΤΙΚΩΝ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '1',
                'old_compass_ids' => ['Α0Ζ'],
                'old_compass_ids_idax' => [],
            ],
            '131' => [
                'field' => 'ΠΕ ΣΥΝΤΗΡΗΤΩΝ ΑΡΧΑΙΟΤΗΤΩΝ ΚΑΙ ΕΡΓΩΝ ΤΕΧΝΗΣ',
                'name' => 'ΠΕ ΣΥΝΤΗΡΗΤΩΝ ΑΡΧΑΙΟΤΗΤΩΝ ΚΑΙ ΕΡΓΩΝ ΤΕΧΝΗΣ',
                'fullname' => 'ΠΕ ΣΥΝΤΗΡΗΤΩΝ ΑΡΧΑΙΟΤΗΤΩΝ ΚΑΙ ΕΡΓΩΝ ΤΕΧΝΗΣ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '1',
                'old_compass_ids' => ['Α07'],
                'old_compass_ids_idax' => ['Β0Τ'],
            ],
            '133' => [
                'field' => 'ΠΕ ΦΥΣΙΚΗΣ ΑΓΩΝΓΗΣ',
                'name' => 'ΠΕ ΦΥΣΙΚΗΣ ΑΓΩΝΓΗΣ',
                'fullname' => 'ΠΕ ΦΥΣΙΚΗΣ ΑΓΩΝΓΗΣ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '1',
                'old_compass_ids' => [],
                'old_compass_ids_idax' => ['Β06'],
            ],
            // ΕΙΔΙΚΟΤΗΤΕΣ ΤΕ
            '201' => [
                'field' => 'ΤΕ ΒΙΒΛΙΟΘΗΚΟΝΟΜΙΑΣ',
                'name' => 'ΤΕ ΒΙΒΛΙΟΘΗΚΟΝΟΜΙΑΣ',
                'fullname' => 'ΤΕ ΒΙΒΛΙΟΘΗΚΟΝΟΜΙΑΣ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '2',
                'old_compass_ids' => ['Α1Δ5'],
                'old_compass_ids_idax' => [],
            ],
            '204' => [
                'field' => 'ΤΕ ΓΡΑΦΙΚΩΝ ΤΕΧΝΩΝ',
                'name' => 'ΤΕ ΓΡΑΦΙΚΩΝ ΤΕΧΝΩΝ',
                'fullname' => 'ΤΕ ΓΡΑΦΙΚΩΝ ΤΕΧΝΩΝ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '2',
                'old_compass_ids' => ['Α131', 'Α132'],
                'old_compass_ids_idax' => ['Β11'],
            ],
            '206' => [
                'field' => 'ΤΕ ΔΙΟΙΚΗΤΙΚΟΥ-ΛΟΓΙΣΤΙΚΟΥ',
                'name' => 'ΤΕ ΔΙΟΙΚΗΤΙΚΟΥ-ΛΟΓΙΣΤΙΚΟΥ',
                'fullname' => 'ΤΕ ΔΙΟΙΚΗΤΙΚΟΥ-ΛΟΓΙΣΤΙΚΟΥ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '2',
                'old_compass_ids' => ['Α112', 'Α1Δ6'],
                'old_compass_ids_idax' => ['Β12'],
            ],
            '2062' => [
                'field' => 'ΤΕ ΔΙΟΙΚΗΤΙΚΟΥ-ΛΟΓΙΣΤΙΚΟΥ',
                'name' => 'ΤΕ ΛΟΓΙΣΤΙΚΟΥ',
                'fullname' => 'ΤΕ ΔΙΟΙΚΗΤΙΚΟΥ-ΛΟΓΙΣΤΙΚΟΥ - ΛΟΓΙΣΤΙΚΟΥ',
                'compass_specialization_parent_id' => '206',
                'compass_specialization_type_id' => '2',
                'old_compass_ids' => ['Α111'],
                'old_compass_ids_idax' => ['Β13'],
            ],
            '210' => [
                'field' => 'ΤΕ ΚΙΝΗΜΑΤΟΓΡΑΦΟΥ',
                'name' => 'ΤΕ ΚΙΝΗΜΑΤΟΓΡΑΦΟΥ',
                'fullname' => 'ΤΕ ΚΙΝΗΜΑΤΟΓΡΑΦΟΥ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '2',
                'old_compass_ids' => ['Α1Δ1'],
                'old_compass_ids_idax' => [],
            ],
            '214' => [
                'field' => 'ΤΕ ΜΗΧΑΝΙΚΩΝ ΕΜΠΟΡΙΚΟΥ ΝΑΥΤΙΚΟΥ',
                'name' => 'ΤΕ ΜΗΧΑΝΙΚΩΝ ΕΜΠΟΡΙΚΟΥ ΝΑΥΤΙΚΟΥ',
                'fullname' => 'ΤΕ ΜΗΧΑΝΙΚΩΝ ΕΜΠΟΡΙΚΟΥ ΝΑΥΤΙΚΟΥ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '2',
                'old_compass_ids' => [],
                'old_compass_ids_idax' => [],
            ],
            '215' => [
                'field' => 'ΤΕ ΜΟΥΣΕΙΟΛΟΓΙΑΣ',
                'name' => 'ΤΕ ΜΟΥΣΕΙΟΛΟΓΙΑΣ',
                'fullname' => 'ΤΕ ΜΟΥΣΕΙΟΛΟΓΙΑΣ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '2',
                'old_compass_ids' => ['Α1Δ3'],
                'old_compass_ids_idax' => [],
            ],
            '216' => [
                'field' => 'ΤΕ ΜΟΥΣΙΚΩΝ ΣΠΟΥΔΩΝ',
                'name' => 'ΤΕ ΜΟΥΣΙΚΩΝ ΣΠΟΥΔΩΝ',
                'fullname' => 'ΤΕ ΜΟΥΣΙΚΩΝ ΣΠΟΥΔΩΝ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '2',
                'old_compass_ids' => ['Α1Δ4'],
                'old_compass_ids_idax' => [],
            ],
            '218' => [
                'field' => 'ΤΕ ΠΑΙΔΑΓΩΓΩΝ ΠΡΩΙΜΗΣ ΠΑΙΔΙΚΗΣ ΗΛΙΚΙΑΣ',
                'name' => 'ΤΕ ΠΑΙΔΑΓΩΓΩΝ ΠΡΩΙΜΗΣ ΠΑΙΔΙΚΗΣ ΗΛΙΚΙΑΣ',
                'fullname' => 'ΤΕ ΠΑΙΔΑΓΩΓΩΝ ΠΡΩΙΜΗΣ ΠΑΙΔΙΚΗΣ ΗΛΙΚΙΑΣ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '2',
                'old_compass_ids' => [],
                'old_compass_ids_idax' => ['Β18'],
            ],
            '219' => [
                'field' => 'ΤΕ ΠΛΗΡΟΦΟΡΙΚΗΣ',
                'name' => 'ΤΕ ΠΛΗΡΟΦΟΡΙΚΗΣ',
                'fullname' => 'ΤΕ ΠΛΗΡΟΦΟΡΙΚΗΣ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '2',
                'old_compass_ids' => [],
                'old_compass_ids_idax' => [],
            ],
            '2191' => [
                'field' => 'ΤΕ ΠΛΗΡΟΦΟΡΙΚΗΣ',
                'name' => 'ΤΕ ΠΛΗΡΟΦΟΡΙΚΗΣ (SOFTWARE-HARDWARE)',
                'fullname' => 'ΤΕ ΠΛΗΡΟΦΟΡΙΚΗΣ - ΠΛΗΡΟΦΟΡΙΚΗΣ (SOFTWARE-HARDWARE)',
                'compass_specialization_parent_id' => '219',
                'compass_specialization_type_id' => '2',
                'old_compass_ids' => ['Α14'],
                'old_compass_ids_idax' => ['Β15'],
            ],
            '220' => [
                'field' => 'ΤΕ ΣΥΝΤΗΡΗΤΩΝ ΑΡΧΑΙΟΤΗΤΩΝ ΚΑΙ ΕΡΓΩΝ ΤΕΧΝΗΣ',
                'name' => 'ΤΕ ΣΥΝΤΗΡΗΤΩΝ ΑΡΧΑΙΟΤΗΤΩΝ ΚΑΙ ΕΡΓΩΝ ΤΕΧΝΗΣ',
                'fullname' => 'ΤΕ ΣΥΝΤΗΡΗΤΩΝ ΑΡΧΑΙΟΤΗΤΩΝ ΚΑΙ ΕΡΓΩΝ ΤΕΧΝΗΣ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '2',
                'old_compass_ids' => ['Α12'],
                'old_compass_ids_idax' => ['Β16'],
            ],
            '221' => [
                'field' => 'ΤΕ ΜΗΧΑΝΙΚΩΝ',
                'name' => 'ΤΕ ΜΗΧΑΝΙΚΩΝ',
                'fullname' => 'ΤΕ ΜΗΧΑΝΙΚΩΝ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '2',
                'old_compass_ids' => [],
                'old_compass_ids_idax' => [],
            ],
            '2211' => [
                'field' => 'ΤΕ ΜΗΧΑΝΙΚΩΝ',
                'name' => 'ΤΕ ΑΝΑΚΑΙΝΙΣΗΣ ΚΑΙ ΑΠΟΚΑΤΑΣΤΑΣΗΣ ΚΤΙΡΙΩΝ',
                'fullname' => 'ΤΕ ΜΗΧΑΝΙΚΩΝ - ΑΝΑΚΑΙΝΙΣΗΣ ΚΑΙ ΑΠΟΚΑΤΑΣΤΑΣΗΣ ΚΤΙΡΙΩΝ',
                'compass_specialization_parent_id' => '221',
                'compass_specialization_type_id' => '2',
                'old_compass_ids' => [],
                'old_compass_ids_idax' => [],
            ],
            '2212' => [
                'field' => 'ΤΕ ΜΗΧΑΝΙΚΩΝ',
                'name' => 'ΤΕ ΒΙΟΜΗΧΑΝΙΚΟΥ ΣΧΕΔΙΑΣΜΟΥ',
                'fullname' => 'ΤΕ ΜΗΧΑΝΙΚΩΝ - ΒΙΟΜΗΧΑΝΙΚΟΥ ΣΧΕΔΙΑ7ΣΜΟΥ',
                'compass_specialization_parent_id' => '221',
                'compass_specialization_type_id' => '2',
                'old_compass_ids' => [],
                'old_compass_ids_idax' => [],
            ],
            '2213' => [
                'field' => 'ΤΕ ΜΗΧΑΝΙΚΩΝ',
                'name' => 'ΤΕ ΗΛΕΚΤΡΟΛΟΓΩΝ ΜΗΧΑΝΙΚΩΝ',
                'fullname' => 'ΤΕ ΜΗΧΑΝΙΚΩΝ - ΗΛΕΚΤΡΟΛΟΓΩΝ ΜΗΧΑΝΙΚΩΝ',
                'compass_specialization_parent_id' => '221',
                'compass_specialization_type_id' => '2',
                'old_compass_ids' => [],
                'old_compass_ids_idax' => [],
            ],
            '2214' => [
                'field' => 'ΤΕ ΜΗΧΑΝΙΚΩΝ',
                'name' => 'ΤΕ ΗΛΕΚΤΡΟΝΙΚΩΝ',
                'fullname' => 'ΤΕ ΜΗΧΑΝΙΚΩΝ - ΗΛΕΚΤΡΟΝΙΚΩΝ',
                'compass_specialization_parent_id' => '221',
                'compass_specialization_type_id' => '2',
                'old_compass_ids' => [],
                'old_compass_ids_idax' => [],
            ],
            '2215' => [
                'field' => 'ΤΕ ΜΗΧΑΝΙΚΩΝ',
                'name' => 'ΤΕ ΚΛΩΣΤΟΥΦΑΝΤΟΥΡΓΩΝ',
                'fullname' => 'ΤΕ ΜΗΧΑΝΙΚΩΝ - ΚΛΩΣΤΟΥΦΑΝΤΟΥΡΓΩΝ',
                'compass_specialization_parent_id' => '221',
                'compass_specialization_type_id' => '2',
                'old_compass_ids' => [],
                'old_compass_ids_idax' => [],
            ],
            '2216' => [
                'field' => 'ΤΕ ΜΗΧΑΝΙΚΩΝ',
                'name' => 'ΤΕ ΜΗΧΑΝΟΛΟΓΩΝ ΜΗΧΑΝΙΚΩΝ',
                'fullname' => 'ΤΕ ΜΗΧΑΝΙΚΩΝ - ΜΗΧΑΝΟΛΟΓΩΝ ΜΗΧΑΝΙΚΩΝ',
                'compass_specialization_parent_id' => '221',
                'compass_specialization_type_id' => '2',
                'old_compass_ids' => [],
                'old_compass_ids_idax' => [],
            ],
            '2217' => [
                'field' => 'ΤΕ ΜΗΧΑΝΙΚΩΝ',
                'name' => 'ΤΕ ΠΟΛΙΤΙΚΩΝ ΜΗΧΑΝΙΚΩΝ',
                'fullname' => 'ΤΕ ΜΗΧΑΝΙΚΩΝ - ΠΟΛΙΤΙΚΩΝ ΜΗΧΑΝΙΚΩΝ',
                'compass_specialization_parent_id' => '221',
                'compass_specialization_type_id' => '2',
                'old_compass_ids' => ['Α161', 'Α162'],
                'old_compass_ids_idax' => [],
            ],
            '2218' => [
                'field' => 'ΤΕ ΜΗΧΑΝΙΚΩΝ',
                'name' => 'ΤΕ ΤΕΧΝΙΚΟΥ ΑΣΦΑΛΕΙΑΣ',
                'fullname' => 'ΤΕ ΜΗΧΑΝΙΚΩΝ - ΤΕΧΝΙΚΟΥ ΑΣΦΑΛΕΙΑΣ',
                'compass_specialization_parent_id' => '221',
                'compass_specialization_type_id' => '2',
                'old_compass_ids' => [],
                'old_compass_ids_idax' => [],
            ],
            '2219' => [
                'field' => 'ΤΕ ΜΗΧΑΝΙΚΩΝ',
                'name' => 'ΤΕ ΤΟΠΟΓΡΑΦΩΝ ΜΗΧΑΝΙΚΩΝ',
                'fullname' => 'ΤΕ ΜΗΧΑΝΙΚΩΝ - ΤΟΠΟΓΡΑΦΩΝ ΜΗΧΑΝΙΚΩΝ',
                'compass_specialization_parent_id' => '221',
                'compass_specialization_type_id' => '2',
                'old_compass_ids' => [],
                'old_compass_ids_idax' => [],
            ],
            '224' => [
                'field' => 'ΤΕ ΦΩΤΟΓΡΑΦΙΑΣ',
                'name' => 'ΤΕ ΦΩΤΟΓΡΑΦΙΑΣ',
                'fullname' => 'ΤΕ ΦΩΤΟΓΡΑΦΙΑΣ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '2',
                'old_compass_ids' => ['Α133'],
                'old_compass_ids_idax' => ['Β17'],
            ],
            // ΕΙΔΙΚΟΤΗΤΕΣ ΔΕ
            '303' => [
                'field' => 'ΔΕ ΒΟΗΘΩΝ ΒΡΕΦΟΝΗΠΙΟΚΟΜΩΝ',
                'name' => 'ΔΕ ΒΟΗΘΩΝ ΒΡΕΦΟΝΗΠΙΟΚΟΜΩΝ',
                'fullname' => 'ΔΕ ΒΟΗΘΩΝ ΒΡΕΦΟΝΗΠΙΟΚΟΜΩΝ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '3',
                'old_compass_ids' => [],
                'old_compass_ids_idax' => ['Β2Γ'],
            ],
            '310' => [
                'field' => 'ΔΕ ΓΡΑΦΙΚΩΝ ΤΕΧΝΩΝ',
                'name' => 'ΔΕ ΓΡΑΦΙΚΩΝ ΤΕΧΝΩΝ',
                'fullname' => 'ΔΕ ΓΡΑΦΙΚΩΝ ΤΕΧΝΩΝ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '3',
                'old_compass_ids' => [],
                'old_compass_ids_idax' => ['Β21'],
            ],
            '312' => [
                'field' => 'ΔΕ ΔΙΟΙΚΗΤΙΚΟΥ-ΛΟΓΙΣΤΙΚΟΥ',
                'name' => 'ΔΕ ΔΙΟΙΚΗΤΙΚΟΥ-ΛΟΓΙΣΤΙΚΟΥ',
                'fullname' => 'ΔΕ ΔΙΟΙΚΗΤΙΚΟΥ-ΛΟΓΙΣΤΙΚΟΥ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '3',
                'old_compass_ids' => ['Α21'],
                'old_compass_ids_idax' => ['Β22'],
            ],
            '313' => [
                'field' => 'ΔΕ ΔΥΤΩΝ',
                'name' => 'ΔΕ ΔΥΤΩΝ',
                'fullname' => 'ΔΕ ΔΥΤΩΝ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '3',
                'old_compass_ids' => ['Α2Β'],
                'old_compass_ids_idax' => [],
            ],
            '314' => [
                'field' => 'ΔΕ ΕΡΓΑΤΟΤΕΧΝΙΤΩΝ',
                'name' => 'ΔΕ ΕΡΓΑΤΟΤΕΧΝΙΤΩΝ',
                'fullname' => 'ΔΕ ΕΡΓΑΤΟΤΕΧΝΙΤΩΝ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '3',
                'old_compass_ids' => [],
                'old_compass_ids_idax' => [],
            ],
            '3141' => [
                'field' => 'ΔΕ ΕΡΓΑΤΟΤΕΧΝΙΤΩΝ',
                'name' => 'ΔΕ ΕΡΓΑΤΟΤΕΧΝΙΤΩΝ ΑΡΧΑΙΟΛΟΓΙΚΩΝ/ΑΝΑΣΤΗΛΩΤΙΚΩΝ ΕΡΓΑΣΙΩΝ',
                'fullname' => 'ΔΕ ΕΡΓΑΤΟΤΕΧΝΙΤΩΝ - ΕΡΓΑΤΟΤΕΧΝΙΤΩΝ ΑΡΧΑΙΟΛΟΓΙΚΩΝ/ΑΝΑΣΤΗΛΩΤΙΚΩΝ ΕΡΓΑΣΙΩΝ',
                'compass_specialization_parent_id' => '314',
                'compass_specialization_type_id' => '3',
                'old_compass_ids' => ['Α28'],
                'old_compass_ids_idax' => ['Β23'],
            ],
            '317' => [
                'field' => 'ΔΕ ΜΟΥΣΙΚΩΝ',
                'name' => 'ΔΕ ΜΟΥΣΙΚΩΝ',
                'fullname' => 'ΔΕ ΜΟΥΣΙΚΩΝ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '3',
                'old_compass_ids' => ['Α2Γ'],
                'old_compass_ids_idax' => [],
            ],
            '318' => [
                'field' => 'ΔΕ ΟΔΗΓΩΝ',
                'name' => 'ΔΕ ΟΔΗΓΩΝ',
                'fullname' => 'ΔΕ ΟΔΗΓΩΝ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '3',
                'old_compass_ids' => ['Α2Α'],
                'old_compass_ids_idax' => ['Β27'],
            ],
            '319' => [
                'field' => 'ΔΕ ΠΛΗΡΟΦΟΡΙΚΗΣ',
                'name' => 'ΔΕ ΠΛΗΡΟΦΟΡΙΚΗΣ',
                'fullname' => 'ΔΕ ΠΛΗΡΟΦΟΡΙΚΗΣ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '3',
                'old_compass_ids' => [],
                'old_compass_ids_idax' => [],
            ],
            '3191' => [
                'field' => 'ΔΕ ΠΛΗΡΟΦΟΡΙΚΗΣ',
                'name' => 'ΔΕ ΠΡΟΓΡΑΜΜΑΤΙΣΤΩΝ Η/Υ',
                'fullname' => 'ΔΕ ΠΛΗΡΟΦΟΡΙΚΗΣ - ΠΡΟΓΡΑΜΜΑΤΙΣΤΩΝ Η/Υ',
                'compass_specialization_parent_id' => '319',
                'compass_specialization_type_id' => '3',
                'old_compass_ids' => [],
                'old_compass_ids_idax' => [],
            ],
            '3192' => [
                'field' => 'ΔΕ ΠΛΗΡΟΦΟΡΙΚΗΣ',
                'name' => 'ΔΕ ΠΡΟΣΩΠΙΚΟΥ Η/Υ',
                'fullname' => 'ΔΕ ΠΛΗΡΟΦΟΡΙΚΗΣ - ΠΡΟΣΩΠΙΚΟΥ Η/Υ',
                'compass_specialization_parent_id' => '319',
                'compass_specialization_type_id' => '3',
                'old_compass_ids' => ['Α25'],
                'old_compass_ids_idax' => ['Β28'],
            ],
            '320' => [
                'field' => 'ΔΕ ΠΡΟΣΩΠΙΚΟΥ ΕΣΤΙΑΣΗΣ',
                'name' => 'ΔΕ ΠΡΟΣΩΠΙΚΟΥ ΕΣΤΙΑΣΗΣ',
                'fullname' => 'ΔΕ ΠΡΟΣΩΠΙΚΟΥ ΕΣΤΙΑΣΗΣ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '3',
                'old_compass_ids' => [],
                'old_compass_ids_idax' => [],
            ],
            '3201' => [
                'field' => 'ΔΕ ΠΡΟΣΩΠΙΚΟΥ ΕΣΤΙΑΣΗΣ',
                'name' => 'ΔΕ ΜΑΓΕΙΡΩΝ',
                'fullname' => 'ΔΕ ΠΡΟΣΩΠΙΚΟΥ ΕΣΤΙΑΣΗΣ - ΜΑΓΕΙΡΩΝ',
                'compass_specialization_parent_id' => '320',
                'compass_specialization_type_id' => '3',
                'old_compass_ids' => [],
                'old_compass_ids_idax' => ['Β2Δ'],
            ],
            '321' => [
                'field' => 'ΔΕ ΣΥΝΤΗΡΗΤΩΝ ΑΡΧΑΙΟΤΗΤΩΝ ΚΑΙ ΕΡΓΩΝ ΤΕΧΝΗΣ',
                'name' => 'ΔΕ ΣΥΝΤΗΡΗΤΩΝ ΑΡΧΑΙΟΤΗΤΩΝ ΚΑΙ ΕΡΓΩΝ ΤΕΧΝΗΣ',
                'fullname' => 'ΔΕ ΣΥΝΤΗΡΗΤΩΝ ΑΡΧΑΙΟΤΗΤΩΝ ΚΑΙ ΕΡΓΩΝ ΤΕΧΝΗΣ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '3',
                'old_compass_ids' => ['Α23'],
                'old_compass_ids_idax' => ['Β29'],
            ],
            '322' => [
                'field' => 'ΔΕ ΤΕΧΝΙΚΟΥ',
                'name' => 'ΔΕ ΤΕΧΝΙΚΟΥ',
                'fullname' => 'ΔΕ ΤΕΧΝΙΚΟΥ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '3',
                'old_compass_ids' => [],
                'old_compass_ids_idax' => [],
            ],
            '3221' => [
                'field' => 'ΔΕ ΤΕΧΝΙΚΟΥ',
                'name' => 'ΔΕ ΔΟΜΙΚΩΝ ΕΡΓΩΝ',
                'fullname' => 'ΔΕ ΤΕΧΝΙΚΟΥ - ΔΟΜΙΚΩΝ ΕΡΓΩΝ',
                'compass_specialization_parent_id' => '322',
                'compass_specialization_type_id' => '3',
                'old_compass_ids' => [],
                'old_compass_ids_idax' => [],
            ],
            '3224' => [
                'field' => 'ΔΕ ΤΕΧΝΙΚΟΥ',
                'name' => 'ΔΕ ΗΛΕΚΤΡΟΛΟΓΩΝ',
                'fullname' => 'ΔΕ ΤΕΧΝΙΚΟΥ - ΗΛΕΚΤΡΟΛΟΓΩΝ',
                'compass_specialization_parent_id' => '322',
                'compass_specialization_type_id' => '3',
                'old_compass_ids' => [],
                'old_compass_ids_idax' => [],
            ],
            '3225' => [
                'field' => 'ΔΕ ΤΕΧΝΙΚΟΥ',
                'name' => 'ΔΕ ΗΛΕΚΤΡΟΝΙΚΩΝ',
                'fullname' => 'ΔΕ ΤΕΧΝΙΚΟΥ - ΗΛΕΚΤΡΟΝΙΚΩΝ',
                'compass_specialization_parent_id' => '322',
                'compass_specialization_type_id' => '3',
                'old_compass_ids' => [],
                'old_compass_ids_idax' => [],
            ],
            '3227' => [
                'field' => 'ΔΕ ΤΕΧΝΙΚΟΥ',
                'name' => 'ΔΕ ΗΛΕΚΤΡΟΤΕΧΝΙΤΩΝ',
                'fullname' => 'ΔΕ ΤΕΧΝΙΚΟΥ - ΗΛΕΚΤΡΟΤΕΧΝΙΤΩΝ',
                'compass_specialization_parent_id' => '322',
                'compass_specialization_type_id' => '3',
                'old_compass_ids' => [],
                'old_compass_ids_idax' => [],
            ],
            '3229' => [
                'field' => 'ΔΕ ΤΕΧΝΙΚΟΥ',
                'name' => 'ΔΕ ΘΕΡΜΑΣΤΩΝ',
                'fullname' => 'ΔΕ ΤΕΧΝΙΚΟΥ - ΘΕΡΜΑΣΤΩΝ',
                'compass_specialization_parent_id' => '322',
                'compass_specialization_type_id' => '3',
                'old_compass_ids' => [],
                'old_compass_ids_idax' => [],
            ],
            '322Β' => [
                'field' => 'ΔΕ ΤΕΧΝΙΚΟΥ',
                'name' => 'ΔΕ ΜΗΧΑΝΟΛΟΓΩΝ',
                'fullname' => 'ΔΕ ΤΕΧΝΙΚΟΥ - ΜΗΧΑΝΟΛΟΓΩΝ',
                'compass_specialization_parent_id' => '322',
                'compass_specialization_type_id' => '3',
                'old_compass_ids' => [],
                'old_compass_ids_idax' => [],
            ],
            '322Γ' => [
                'field' => 'ΔΕ ΤΕΧΝΙΚΟΥ',
                'name' => 'ΔΕ ΜΗΧΑΝΟΤΕΧΝΙΤΩΝ ΟΧΗΜΑΤΩΝ',
                'fullname' => 'ΔΕ ΤΕΧΝΙΚΟΥ - ΜΗΧΑΝΟΤΕΧΝΙΤΩΝ ΟΧΗΜΑΤΩΝ',
                'compass_specialization_parent_id' => '322',
                'compass_specialization_type_id' => '3',
                'old_compass_ids' => [],
                'old_compass_ids_idax' => [],
            ],
            '322Η' => [
                'field' => 'ΔΕ ΤΕΧΝΙΚΟΥ',
                'name' => 'ΔΕ ΣΧΕΔΙΑΣΤΩΝ',
                'fullname' => 'ΔΕ ΤΕΧΝΙΚΟΥ - ΣΧΕΔΙΑΣΤΩΝ',
                'compass_specialization_parent_id' => '322',
                'compass_specialization_type_id' => '3',
                'old_compass_ids' => ['Α22'],
                'old_compass_ids_idax' => ['Β2Θ'],
            ],
            '322Κ' => [
                'field' => 'ΔΕ ΤΕΧΝΙΚΟΥ',
                'name' => 'ΔΕ ΤΕΧΝΙΚΩΝ ΣΚΗΝΗΣ ΚΑΙ ΚΑΤΑΣΚΕΥΗΣ ΣΚΗΝΙΚΩΝ',
                'fullname' => 'ΔΕ ΤΕΧΝΙΚΟΥ - ΤΕΧΝΙΚΩΝ ΣΚΗΝΗΣ ΚΑΙ ΚΑΤΑΣΚΕΥΗΣ ΣΚΗΝΙΚΩΝ',
                'compass_specialization_parent_id' => '322',
                'compass_specialization_type_id' => '3',
                'old_compass_ids' => [],
                'old_compass_ids_idax' => [],
            ],
            '322Μ' => [
                'field' => 'ΔΕ ΤΕΧΝΙΚΟΥ',
                'name' => 'ΔΕ ΤΕΧΝΙΤΩΝ ΣΥΝΤΗΡΗΤΩΝ ΚΑΛΟΡΙΦΕΡ',
                'fullname' => 'ΔΕ ΤΕΧΝΙΚΟΥ - ΤΕΧΝΙΤΩΝ ΣΥΝΤΗΡΗΤΩΝ ΚΑΛΟΡΙΦΕΡ',
                'compass_specialization_parent_id' => '322',
                'compass_specialization_type_id' => '3',
                'old_compass_ids' => [],
                'old_compass_ids_idax' => [],
            ],
            '322Ο' => [
                'field' => 'ΔΕ ΤΕΧΝΙΚΟΥ',
                'name' => 'ΔΕ ΥΔΡΑΥΛΙΚΩΝ',
                'fullname' => 'ΔΕ ΤΕΧΝΙΚΟΥ - ΥΔΡΑΥΛΙΚΩΝ',
                'compass_specialization_parent_id' => '322',
                'compass_specialization_type_id' => '3',
                'old_compass_ids' => [],
                'old_compass_ids_idax' => [],
            ],
            '322Π' => [
                'field' => 'ΔΕ ΤΕΧΝΙΚΟΥ',
                'name' => 'ΔΕ ΦΡΟΝΤΙΣΤΩΝ ΣΚΗΝΗΣ',
                'fullname' => 'ΔΕ ΤΕΧΝΙΚΟΥ - ΦΡΟΝΤΙΣΤΩΝ ΣΚΗΝΗΣ',
                'compass_specialization_parent_id' => '322',
                'compass_specialization_type_id' => '3',
                'old_compass_ids' => ['Α2Ε'],
                'old_compass_ids_idax' => [],
            ],
            '322Σ' => [
                'field' => 'ΔΕ ΤΕΧΝΙΚΟΥ',
                'name' => 'ΔΕ ΨΥΚΤΙΚΩΝ',
                'fullname' => 'ΔΕ ΤΕΧΝΙΚΟΥ - ΨΥΚΤΙΚΩΝ',
                'compass_specialization_parent_id' => '322',
                'compass_specialization_type_id' => '3',
                'old_compass_ids' => [],
                'old_compass_ids_idax' => [],
            ],
            '324' => [
                'field' => 'ΔΕ ΤΕΧΝΙΚΟΥ ΠΡΟΣΘΕΤΙΚΩΝ ΚΑΙ ΟΡΘΩΤΙΚΩΝ ΚΑΤΑΣΚΕΥΩΝ',
                'name' => 'ΔΕ ΤΕΧΝΙΚΟΥ ΠΡΟΣΘΕΤΙΚΩΝ ΚΑΙ ΟΡΘΩΤΙΚΩΝ ΚΑΤΑΣΚΕΥΩΝ',
                'fullname' => 'ΔΕ ΤΕΧΝΙΚΟΥ ΠΡΟΣΘΕΤΙΚΩΝ ΚΑΙ ΟΡΘΩΤΙΚΩΝ ΚΑΤΑΣΚΕΥΩΝ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '3',
                'old_compass_ids' => [],
                'old_compass_ids_idax' => [],
            ],
            '325' => [
                'field' => 'ΔΕ ΤΗΛΕΦΩΝΗΤΩΝ',
                'name' => 'ΔΕ ΤΗΛΕΦΩΝΗΤΩΝ',
                'fullname' => 'ΔΕ ΤΗΛΕΦΩΝΗΤΩΝ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '3',
                'old_compass_ids' => ['Α24'],
                'old_compass_ids_idax' => [],
            ],
            '326' => [
                'field' => 'ΔΕ ΦΥΛΑΚΩΝ',
                'name' => 'ΔΕ ΦΥΛΑΚΩΝ',
                'fullname' => 'ΔΕ ΦΥΛΑΚΩΝ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '3',
                'old_compass_ids' => [],
                'old_compass_ids_idax' => ['Β2Β'],
            ],
            '327' => [
                'field' => 'ΔΕ ΦΥΛΑΞΗΣ-ΠΛΗΡΟΦΟΡΗΣΗΣ',
                'name' => 'ΔΕ ΦΥΛΑΞΗΣ-ΠΛΗΡΟΦΟΡΗΣΗΣ',
                'fullname' => 'ΔΕ ΦΥΛΑΞΗΣ-ΠΛΗΡΟΦΟΡΗΣΗΣ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '3',
                'old_compass_ids' => [],
                'old_compass_ids_idax' => [],
            ],
            '3271' => [
                'field' => 'ΔΕ ΦΥΛΑΞΗΣ-ΠΛΗΡΟΦΟΡΗΣΗΣ',
                'name' => 'ΔΕ ΗΜΕΡΗΣΙΩΝ ΦΥΛΑΚΩΝ ΜΟΥΣΕΙΩΝ ΚΑΙ ΑΡΧΑΙΟΛΟΓΙΚΩΝ ΧΩΡΩΝ',
                'fullname' => 'ΔΕ ΦΥΛΑΞΗΣ-ΠΛΗΡΟΦΟΡΗΣΗΣ - ΗΜΕΡΗΣΙΩΝ ΦΥΛΑΚΩΝ ΜΟΥΣΕΙΩΝ ΚΑΙ ΑΡΧΑΙΟΛΟΓΙΚΩΝ ΧΩΡΩΝ',
                'compass_specialization_parent_id' => '327',
                'compass_specialization_type_id' => '3',
                'old_compass_ids' => ['Α291'],
                'old_compass_ids_idax' => ['Β2Α'],
            ],
            '3272' => [
                'field' => 'ΔΕ ΦΥΛΑΞΗΣ-ΠΛΗΡΟΦΟΡΗΣΗΣ',
                'name' => 'ΔΕ ΝΥΧΤΟΦΥΛΑΚΩΝ ΜΟΥΣΕΙΩΝ ΚΑΙ ΑΡΧΑΙΟΛΟΓΙΚΩΝ ΧΩΡΩΝ',
                'fullname' => 'ΔΕ ΦΥΛΑΞΗΣ-ΠΛΗΡΟΦΟΡΗΣΗΣ - ΝΥΧΤΟΦΥΛΑΚΩΝ ΜΟΥΣΕΙΩΝ ΚΑΙ ΑΡΧΑΙΟΛΟΓΙΚΩΝ ΧΩΡΩΝ',
                'compass_specialization_parent_id' => '327',
                'compass_specialization_type_id' => '3',
                'old_compass_ids' => ['Α292'],
                'old_compass_ids_idax' => ['Β26'],
            ],
            '328' => [
                'field' => 'ΔΕ ΦΩΤΟΓΡΑΦΩΝ',
                'name' => 'ΔΕ ΦΩΤΟΓΡΑΦΩΝ',
                'fullname' => 'ΔΕ ΦΩΤΟΓΡΑΦΩΝ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '3',
                'old_compass_ids' => ['Α27'],
                'old_compass_ids_idax' => ['Β2Ε'],
            ],
            '329' => [
                'field' => 'ΔΕ ΧΕΙΡΙΣΤΩΝ ΜΗΧΑΝΗΜΑΤΩΝ ΕΡΓΟΥ',
                'name' => 'ΔΕ ΧΕΙΡΙΣΤΩΝ ΜΗΧΑΝΗΜΑΤΩΝ ΕΡΓΟΥ',
                'fullname' => 'ΔΕ ΧΕΙΡΙΣΤΩΝ ΜΗΧΑΝΗΜΑΤΩΝ ΕΡΓΟΥ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '3',
                'old_compass_ids' => [],
                'old_compass_ids_idax' => ['Β2Ζ'],
            ],
            // ΕΙΔΙΚΟΤΗΤΕΣ ΥΕ.
            '402' => [
                'field' => 'ΥΕ ΕΠΙΜΕΛΗΤΩΝ-ΚΛΗΤΗΡΩΝ',
                'name' => 'ΥΕ ΕΠΙΜΕΛΗΤΩΝ-ΚΛΗΤΗΡΩΝ',
                'fullname' => 'ΥΕ ΕΠΙΜΕΛΗΤΩΝ-ΚΛΗΤΗΡΩΝ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '4',
                'old_compass_ids' => [],
                'old_compass_ids_idax' => [],
            ],
            '4022' => [
                'field' => 'ΥΕ ΕΠΙΜΕΛΗΤΩΝ-ΚΛΗΤΗΡΩΝ',
                'name' => 'ΥΕ ΚΛΗΤΗΡΩΝ',
                'fullname' => 'ΥΕ ΕΠΙΜΕΛΗΤΩΝ-ΚΛΗΤΗΡΩΝ - ΚΛΗΤΗΡΩΝ',
                'compass_specialization_parent_id' => '402',
                'compass_specialization_type_id' => '4',
                'old_compass_ids' => ['Α311'],
                'old_compass_ids_idax' => ['Β35'],
            ],
            '406' => [
                'field' => 'ΥΕ ΕΡΓΑΤΩΝ ΤΕΧΝΙΚΩΝ ΕΡΓΑΣΙΩΝ',
                'name' => 'ΥΕ ΕΡΓΑΤΩΝ ΤΕΧΝΙΚΩΝ ΕΡΓΑΣΙΩΝ',
                'fullname' => 'ΥΕ ΕΡΓΑΤΩΝ ΤΕΧΝΙΚΩΝ ΕΡΓΑΣΙΩΝ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '4',
                'old_compass_ids' => [],
                'old_compass_ids_idax' => [],
            ],
            '4061' => [
                'field' => 'ΥΕ ΕΡΓΑΤΩΝ ΤΕΧΝΙΚΩΝ ΕΡΓΑΣΙΩΝ',
                'name' => 'ΥΕ ΕΡΓΑΤΩΝ ΑΡΧΑΙΟΛΟΓΙΚΩΝ / ΑΝΑΣΤΗΛΩΤΙΚΩΝ ΕΡΓΑΣΙΩΝ',
                'fullname' => 'ΥΕ ΕΡΓΑΤΩΝ ΤΕΧΝΙΚΩΝ ΕΡΓΑΣΙΩΝ - ΕΡΓΑΤΩΝ ΑΡΧΑΙΟΛΟΓΙΚΩΝ / ΑΝΑΣΤΗΛΩΤΙΚΩΝ ΕΡΓΑΣΙΩΝ',
                'compass_specialization_parent_id' => '406',
                'compass_specialization_type_id' => '4',
                'old_compass_ids' => ['Α312'],
                'old_compass_ids_idax' => ['Β31', 'Β32'],
            ],
            '407' => [
                'field' => 'ΥΕ ΟΔΗΓΩΝ',
                'name' => 'ΥΕ ΟΔΗΓΩΝ',
                'fullname' => 'ΥΕ ΟΔΗΓΩΝ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '4',
                'old_compass_ids' => [],
                'old_compass_ids_idax' => ['Β39'],
            ],
            '408' => [
                'field' => 'ΥΕ ΠΡΟΣΩΠΙΚΟΥ ΕΣΤΙΑΣΗΣ',
                'name' => 'ΥΕ ΠΡΟΣΩΠΙΚΟΥ ΕΣΤΙΑΣΗΣ',
                'fullname' => 'ΥΕ ΠΡΟΣΩΠΙΚΟΥ ΕΣΤΙΑΣΗΣ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '4',
                'old_compass_ids' => [],
                'old_compass_ids_idax' => [],
            ],
            '4082' => [
                'field' => 'ΥΕ ΠΡΟΣΩΠΙΚΟΥ ΕΣΤΙΑΣΗΣ',
                'name' => 'ΥΕ ΠΡΟΣΩΠΙΚΟΥ ΜΑΓΕΙΡΩΝ',
                'fullname' => 'ΥΕ ΠΡΟΣΩΠΙΚΟΥ ΕΣΤΙΑΣΗΣ - ΠΡΟΣΩΠΙΚΟΥ ΜΑΓΕΙΡΩΝ',
                'compass_specialization_parent_id' => '408',
                'compass_specialization_type_id' => '4',
                'old_compass_ids' => [],
                'old_compass_ids_idax' => ['Β3Α'],
            ],
            '409' => [
                'field' => 'ΥΕ ΠΡΟΣΩΠΙΚΟΥ ΚΑΘΑΡΙΟΤΗΤΑΣ ΕΣΩΤΕΡΙΚΩΝ ΧΩΡΩΝ',
                'name' => 'ΥΕ ΠΡΟΣΩΠΙΚΟΥ ΚΑΘΑΡΙΟΤΗΤΑΣ ΕΣΩΤΕΡΙΚΩΝ ΧΩΡΩΝ',
                'fullname' => 'ΥΕ ΠΡΟΣΩΠΙΚΟΥ ΚΑΘΑΡΙΟΤΗΤΑΣ ΕΣΩΤΕΡΙΚΩΝ ΧΩΡΩΝ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '4',
                'old_compass_ids' => ['Α313'],
                'old_compass_ids_idax' => ['Β34'],
            ],
            '410' => [
                'field' => 'ΥΕ ΤΕΧΝΙΤΩΝ',
                'name' => 'ΥΕ ΤΕΧΝΙΤΩΝ',
                'fullname' => 'ΥΕ ΤΕΧΝΙΤΩΝ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '4',
                'old_compass_ids' => [],
                'old_compass_ids_idax' => [],
            ],
            '4101' => [
                'field' => 'ΥΕ ΤΕΧΝΙΤΩΝ',
                'name' => 'ΥΕ ΚΛΕΚΤΡΟΤΕΧΝΙΤΩΝ',
                'fullname' => 'ΥΕ ΤΕΧΝΙΤΩΝ - ΚΛΕΚΤΡΟΤΕΧΝΙΤΩΝ',
                'compass_specialization_parent_id' => '410',
                'compass_specialization_type_id' => '4',
                'old_compass_ids' => ['Α38'],
                'old_compass_ids_idax' => ['Β33'],
            ],
            '4102' => [
                'field' => 'ΥΕ ΤΕΧΝΙΤΩΝ',
                'name' => 'ΥΕ ΜΑΡΜΑΡΟΤΕΧΝΙΤΩΝ',
                'fullname' => 'ΥΕ ΤΕΧΝΙΤΩΝ - ΜΑΡΜΑΡΟΤΕΧΝΙΤΩΝ',
                'compass_specialization_parent_id' => '410',
                'compass_specialization_type_id' => '4',
                'old_compass_ids' => [],
                'old_compass_ids_idax' => ['Β36'],
            ],
            '4103' => [
                'field' => 'ΥΕ ΤΕΧΝΙΤΩΝ',
                'name' => 'ΥΕ ΤΕΧΝΙΤΩΝ ΥΔΡΑΥΛΙΚΩΝ',
                'fullname' => 'ΥΕ ΤΕΧΝΙΤΩΝ - ΤΕΧΝΙΤΩΝ ΥΔΡΑΥΛΙΚΩΝ',
                'compass_specialization_parent_id' => '410',
                'compass_specialization_type_id' => '4',
                'old_compass_ids' => ['Α34'],
                'old_compass_ids_idax' => [],
            ],
            '411' => [
                'field' => 'ΥΕ ΦΥΛΑΚΩΝ',
                'name' => 'ΥΕ ΦΥΛΑΚΩΝ',
                'fullname' => 'ΥΕ ΦΥΛΑΚΩΝ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '4',
                'old_compass_ids' => ['Α36'],
                'old_compass_ids_idax' => ['Β3Δ'],
            ],
            '4111' => [
                'field' => 'ΥΕ ΦΥΛΑΚΩΝ',
                'name' => 'ΥΕ ΗΜΕΡΗΣΙΩΝ ΦΥΛΑΚΩΝ ΜΟΥΣΕΙΩΝ ΚΑΙ ΑΡΧΑΙΟΛΟΓΙΚΩΝ ΧΩΡΩΝ',
                'fullname' => 'ΥΕ ΦΥΛΑΚΩΝ - ΗΜΕΡΗΣΙΩΝ ΦΥΛΑΚΩΝ ΜΟΥΣΕΙΩΝ ΚΑΙ ΑΡΧΑΙΟΛΟΓΙΚΩΝ ΧΩΡΩΝ',
                'compass_specialization_parent_id' => '411',
                'compass_specialization_type_id' => '4',
                'old_compass_ids' => [],
                'old_compass_ids_idax' => ['Β38'],
            ],
            '4112' => [
                'field' => 'ΥΕ ΦΥΛΑΚΩΝ',
                'name' => 'ΥΕ ΝΥΧΤΟΦΥΛΑΚΩΝ ΜΟΥΣΕΙΩΝ ΚΑΙ ΑΡΧΑΙΟΛΟΓΙΚΩΝ ΧΩΡΩΝ',
                'fullname' => 'ΥΕ ΦΥΛΑΚΩΝ - ΝΥΧΤΟΦΥΛΑΚΩΝ ΜΟΥΣΕΙΩΝ ΚΑΙ ΑΡΧΑΙΟΛΟΓΙΚΩΝ ΧΩΡΩΝ',
                'compass_specialization_parent_id' => '411',
                'compass_specialization_type_id' => '4',
                'old_compass_ids' => [],
                'old_compass_ids_idax' => ['Β37'],
            ],
            '412' => [
                'field' => 'ΥΕ ΧΕΙΡΙΣΤΩΝ ΜΗΧΑΝΗΜΑΤΩΝ ΕΡΓΟΥ',
                'name' => 'ΥΕ ΧΕΙΡΙΣΤΩΝ ΜΗΧΑΝΗΜΑΤΩΝ ΕΡΓΟΥ',
                'fullname' => 'ΥΕ ΧΕΙΡΙΣΤΩΝ ΜΗΧΑΝΗΜΑΤΩΝ ΕΡΓΟΥ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '4',
                'old_compass_ids' => [],
                'old_compass_ids_idax' => ['Β3Β'],
            ],
            // ΕΙΔΙΚΟΤΗΤΕΣ ΕΕΠ / ΙΑΤΡΟΙ
            '509' => [
                'field' => 'ΠΕ ΙΑΤΡΩΝ',
                'name' => 'ΙΑΤΡΙΚΗΣ ΕΡΓΑΣΙΑΣ',
                'fullname' => 'ΠΕ ΙΑΤΡΩΝ ΙΑΤΡΙΚΗΣ ΕΡΓΑΣΙΑΣ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '4',
                'old_compass_ids' => [],
                'old_compass_ids_idax' => [],
            ],
            '523' => [
                'field' => 'ΠΕ ΙΑΤΡΩΝ',
                'name' => 'ΠΕ ΕΛΕΓΚΤΩΝ ΙΑΤΡΩΝ',
                'fullname' => 'ΠΕ ΕΛΕΓΚΤΩΝ ΙΑΤΡΩΝ',
                'compass_specialization_parent_id' => null,
                'compass_specialization_type_id' => '4',
                'old_compass_ids' => [],
                'old_compass_ids_idax' => ['Β002'],
            ],
        ];


        $this->command->info('Start...');

        foreach ($specs as $key => $val) {
            $parentId = null;
            $typeId = $specTypes[$val['compass_specialization_type_id']][0]['id'];
            // find parent from specializations table if needed.
            if (!empty($val['compass_specialization_parent_id'])) {
                $parent = Specialization::where('compass_id', $val['compass_specialization_parent_id'])->first();
                $parentId = $parent->id;
            }

            try {
                DB::connection('mysql_personnel')->beginTransaction();

                // Create new specialization
                $specialization = Specialization::create([
                    'field' => $val['field'],
                    'name' => $val['name'],
                    'fullname' => $val['fullname'],
                    'compass_id' => $key,
                    'specialization_type_id' => $typeId,
                    'specialization_parent_id' => $parentId,
                    'compass_specialization_type_id' => $val['compass_specialization_type_id'],
                    'compass_specialization_parent_id' => $val['compass_specialization_parent_id'],
                ]);

                $this->command->info("Created $specialization->id: $specialization->fullname ($specialization->compass_id). parent_id: $parentId");

                $hrOfficeIds = Specialization::whereNotNull('hroffice_id')
                    ->where(function ($query) use ($val) {
                        $query->whereIn('compass_id_old', $val['old_compass_ids'])
                            ->orWhereIn('compass_id_old', $val['old_compass_ids_idax']);
                    })
                    ->pluck('hroffice_id');
                $departmentBoardIds = Specialization::whereNotNull('department_board_id')
                    ->where(function ($query) use ($val){
                        $query->whereIn('compass_id_old', $val['old_compass_ids'])
                            ->orWhereIn('compass_id_old', $val['old_compass_ids_idax']);
                    })
                    ->pluck('department_board_id');

                // insert into hroffice_specialization
                if (!empty($hrOfficeIds)) {
                    $specialization->hroffices()->attach($hrOfficeIds);
                }

                // insert into department_board_specialization
                if (!empty($departmentBoardIds)) {
                    $specialization->departmentBoards()->attach($departmentBoardIds);
                }

                // Create positions
                $oldSpecsMonimoiIds = Specialization::whereIn('compass_id_old', $val['old_compass_ids'])->pluck('id');
                if (!empty($oldSpecsMonimoiIds)) {
                    $this->buildPositions($specialization, $oldSpecsMonimoiIds, $monimoi);

                    $this->updateEmployees($specialization, $oldSpecsMonimoiIds, $monimoi);

                    Specialization::whereIn('id', $oldSpecsMonimoiIds)
                        ->where('id', '!=', $specialization->id)
                        ->delete();
                }

                $oldSpecsIdaxIds = Specialization::whereIn('compass_id_old', $val['old_compass_ids_idax'])->pluck('id');
                if (!empty($oldSpecsIdaxIds)) {
                    $this->buildPositions($specialization, $oldSpecsIdaxIds, $idax);

                    $this->updateEmployees($specialization, $oldSpecsIdaxIds, $idax);

                    Specialization::whereIn('id', $oldSpecsIdaxIds)
                        ->where('id', '!=', $specialization->id)
                        ->delete();
                }

                DB::connection('mysql_personnel')->commit();
                $this->command->info('Specialization migrate success');

            } catch (\Throwable $e) {
                $this->command->error('Migrate error:' . $e->getMessage());

                DB::connection('mysql_personnel')->rollBack();
            }
        }
    }


    protected function buildPositions($specialization, $oldSpecializationIds, $occupationId)
    {
        // Πρέπει στις περιπτώσεις που έχουμε πολλές ειδικότητες που ενωθήκαν (πχ. ΠΕ ΠΕΡΙΒΑΛΛΟΝΤΟΣ) να γινει το σύνολο των θέσεων ανά διεύθυνση
        $sums = DB::connection('mysql_personnel')->table('positions')
            ->whereIn('specialization_id', $oldSpecializationIds)
            ->where('occupation_id', $occupationId)
            ->selectRaw('unit_id, department_id, sum(org_nr) as sum_org_nr, sum(desm_nr) as sum_desm_nr, sum(pros_nr) as sum_pros_nr, sum(epet_yphr_nr) as sum_epet_yphr_nr, sum(epet_pros_nr) as sum_epet_pros_nr, sum(epet_diath_nr) as sum_epet_diath_nr')
            ->groupByRaw('unit_id, department_id')
            ->get();

        foreach ($sums as $sum) {
            // Create one with the new specialization_id.
            // We need to find the unit and department compass_id.
            $compassUnitId = Position::where('unit_id', $sum->unit_id)->first()->compass_unit_id;
            if (!empty($sum->department_id)) {
                $compassDepartmentId = Position::where('department_id', $sum->department_id)->first()->compass_department_id;
            } else {
                $compassDepartmentId = null;
            }

            Position::create([
                'unit_id' => $sum->unit_id,
                'department_id' => $sum->department_id,
                'occupation_id' => $occupationId,
                'specialization_id' => $specialization->id,
                'specialization_parent_id' => $specialization->specialization_parent_id,
                'org_nr' => $sum->sum_org_nr,
                'desm_nr' => $sum->sum_desm_nr,
                'pros_nr' => $sum->sum_pros_nr,
                'epet_yphr_nr' => $sum->sum_epet_yphr_nr,
                'epet_pros_nr' => $sum->sum_epet_pros_nr,
                'epet_diath_nr' => $sum->sum_epet_diath_nr,
                'compass_unit_id' => $compassUnitId,
                'compass_department_id' => $compassDepartmentId,
                'compass_specialization_id' => $specialization->compass_id,
                'compass_specialization_parent_id' => $specialization->compass_specialization_parent_id,
            ]);
        }

        Position::whereIn('specialization_id', $oldSpecializationIds)
            ->where('specialization_id', '!=', $specialization->id)
            ->where('occupation_id', $occupationId)
            ->delete();
    }

    protected function updateEmployees($specialization, $oldSpecializationIds, $occupationId)
    {
        Employee::whereIn('specialization_id', $oldSpecializationIds)
            ->update(['specialization_id' => $specialization->id]);
    }
}
