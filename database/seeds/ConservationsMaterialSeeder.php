<?php

use Illuminate\Database\Seeder;
use App\Models\Conservations\Material;
use App\Models\Conservations\Conservator;

class ConservationsMaterialSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::connection('mysql_conservations')->statement("SET foreign_key_checks = 0");

        DB::connection('mysql_conservations')->table('conservator_material')->truncate();

        DB::connection('mysql_conservations')->statement("SET foreign_key_checks = 1");

        // Seed Specializations
        $materials = array(
            array("reg" => 1, "mat" => 1),
            array("reg" => 1, "mat" => 2),
            array("reg" => 1, "mat" => 4),
            array("reg" => 1, "mat" => 6),
            array("reg" => 1, "mat" => 7),
            array("reg" => 1, "mat" => 9),
            array("reg" => 1, "mat" => 10),
            array("reg" => 1, "mat" => 11),
            array("reg" => 1, "mat" => 12),
            array("reg" => 1, "mat" => 13),
            array("reg" => 2, "mat" => 1),
            array("reg" => 2, "mat" => 2),
            array("reg" => 2, "mat" => 3),
            array("reg" => 2, "mat" => 4),
            array("reg" => 2, "mat" => 6),
            array("reg" => 2, "mat" => 7),
            array("reg" => 2, "mat" => 9),
            array("reg" => 2, "mat" => 10),
            array("reg" => 2, "mat" => 11),
            array("reg" => 2, "mat" => 12),
            array("reg" => 2, "mat" => 13),
            array("reg" => 3, "mat" => 3),
            array("reg" => 3, "mat" => 9),
            array("reg" => 3, "mat" => 11),
            array("reg" => 4, "mat" => 1),
            array("reg" => 4, "mat" => 2),
            array("reg" => 4, "mat" => 6),
            array("reg" => 4, "mat" => 7),
            array("reg" => 5, "mat" => 10),
            array("reg" => 5, "mat" => 12),
            array("reg" => 6, "mat" => 9),
            array("reg" => 7, "mat" => 10),
            array("reg" => 8, "mat" => 1),
            array("reg" => 8, "mat" => 2),
            array("reg" => 8, "mat" => 4),
            array("reg" => 8, "mat" => 7),
            array("reg" => 8, "mat" => 13),
            array("reg" => 9, "mat" => 9),
            array("reg" => 9, "mat" => 10),
            array("reg" => 10, "mat" => 10),
            array("reg" => 11, "mat" => 4),
            array("reg" => 12, "mat" => 1),
            array("reg" => 12, "mat" => 2),
            array("reg" => 13, "mat" => 3),
            array("reg" => 13, "mat" => 6),
            array("reg" => 13, "mat" => 9),
            array("reg" => 13, "mat" => 10),
            array("reg" => 13, "mat" => 11),
            array("reg" => 13, "mat" => 12),
            array("reg" => 14, "mat" => 3),
            array("reg" => 15, "mat" => 1),
            array("reg" => 15, "mat" => 2),
            array("reg" => 16, "mat" => 6),
            array("reg" => 17, "mat" => 2),
            array("reg" => 17, "mat" => 7),
            array("reg" => 17, "mat" => 13),
            array("reg" => 18, "mat" => 3),
            array("reg" => 18, "mat" => 9),
            array("reg" => 18, "mat" => 10),
            array("reg" => 18, "mat" => 11),
            array("reg" => 18, "mat" => 12),
            array("reg" => 19, "mat" => 10),
            array("reg" => 20, "mat" => 11),
            array("reg" => 21, "mat" => 1),
            array("reg" => 21, "mat" => 2),
            array("reg" => 21, "mat" => 4),
            array("reg" => 21, "mat" => 7),
            array("reg" => 21, "mat" => 13),
            array("reg" => 22, "mat" => 9),
            array("reg" => 22, "mat" => 10),
            array("reg" => 22, "mat" => 12),
            array("reg" => 23, "mat" => 1),
            array("reg" => 23, "mat" => 2),
            array("reg" => 23, "mat" => 7),
            array("reg" => 23, "mat" => 13),
            array("reg" => 24, "mat" => 3),
            array("reg" => 24, "mat" => 7),
            array("reg" => 24, "mat" => 9),
            array("reg" => 24, "mat" => 10),
            array("reg" => 24, "mat" => 11),
            array("reg" => 24, "mat" => 12),
            array("reg" => 25, "mat" => 3),
            array("reg" => 25, "mat" => 9),
            array("reg" => 25, "mat" => 10),
            array("reg" => 25, "mat" => 12),
            array("reg" => 25, "mat" => 15),
            array("reg" => 26, "mat" => 1),
            array("reg" => 26, "mat" => 2),
            array("reg" => 26, "mat" => 3),
            array("reg" => 26, "mat" => 6),
            array("reg" => 26, "mat" => 7),
            array("reg" => 26, "mat" => 9),
            array("reg" => 26, "mat" => 10),
            array("reg" => 27, "mat" => 1),
            array("reg" => 27, "mat" => 2),
            array("reg" => 28, "mat" => 1),
            array("reg" => 28, "mat" => 2),
            array("reg" => 29, "mat" => 9),
            array("reg" => 29, "mat" => 10),
            array("reg" => 29, "mat" => 11),
            array("reg" => 29, "mat" => 12),
            array("reg" => 30, "mat" => 1),
            array("reg" => 31, "mat" => 3),
            array("reg" => 31, "mat" => 9),
            array("reg" => 31, "mat" => 10),
            array("reg" => 31, "mat" => 12),
            array("reg" => 31, "mat" => 15),
            array("reg" => 32, "mat" => 1),
            array("reg" => 32, "mat" => 2),
            array("reg" => 33, "mat" => 1),
            array("reg" => 33, "mat" => 2),
            array("reg" => 33, "mat" => 10),
            array("reg" => 33, "mat" => 13),
            array("reg" => 34, "mat" => 1),
            array("reg" => 34, "mat" => 2),
            array("reg" => 35, "mat" => 1),
            array("reg" => 35, "mat" => 2),
            array("reg" => 36, "mat" => 4),
            array("reg" => 37, "mat" => 3),
            array("reg" => 38, "mat" => 2),
            array("reg" => 38, "mat" => 6),
            array("reg" => 39, "mat" => 1),
            array("reg" => 39, "mat" => 2),
            array("reg" => 39, "mat" => 7),
            array("reg" => 40, "mat" => 4),
            array("reg" => 40, "mat" => 7),
            array("reg" => 41, "mat" => 1),
            array("reg" => 41, "mat" => 2),
            array("reg" => 41, "mat" => 7),
            array("reg" => 41, "mat" => 13),
            array("reg" => 42, "mat" => 6),
            array("reg" => 43, "mat" => 1),
            array("reg" => 43, "mat" => 2),
            array("reg" => 43, "mat" => 7),
            array("reg" => 44, "mat" => 1),
            array("reg" => 44, "mat" => 2),
            array("reg" => 44, "mat" => 7),
            array("reg" => 45, "mat" => 10),
            array("reg" => 46, "mat" => 3),
            array("reg" => 46, "mat" => 9),
            array("reg" => 46, "mat" => 12),
            array("reg" => 46, "mat" => 15),
            array("reg" => 47, "mat" => 9),
            array("reg" => 47, "mat" => 10),
            array("reg" => 48, "mat" => 10),
            array("reg" => 49, "mat" => 1),
            array("reg" => 49, "mat" => 4),
            array("reg" => 50, "mat" => 6),
            array("reg" => 50, "mat" => 9),
            array("reg" => 51, "mat" => 1),
            array("reg" => 52, "mat" => 1),
            array("reg" => 52, "mat" => 2),
            array("reg" => 52, "mat" => 6),
            array("reg" => 52, "mat" => 7),
            array("reg" => 52, "mat" => 13),
            array("reg" => 53, "mat" => 9),
            array("reg" => 54, "mat" => 1),
            array("reg" => 54, "mat" => 2),
            array("reg" => 54, "mat" => 7),
            array("reg" => 54, "mat" => 13),
            array("reg" => 55, "mat" => 1),
            array("reg" => 55, "mat" => 2),
            array("reg" => 55, "mat" => 7),
            array("reg" => 55, "mat" => 13),
            array("reg" => 56, "mat" => 9),
            array("reg" => 56, "mat" => 11),
            array("reg" => 57, "mat" => 3),
            array("reg" => 57, "mat" => 6),
            array("reg" => 57, "mat" => 9),
            array("reg" => 57, "mat" => 10),
            array("reg" => 57, "mat" => 11),
            array("reg" => 57, "mat" => 12),
            array("reg" => 58, "mat" => 3),
            array("reg" => 59, "mat" => 9),
            array("reg" => 59, "mat" => 10),
            array("reg" => 60, "mat" => 3),
            array("reg" => 60, "mat" => 6),
            array("reg" => 60, "mat" => 9),
            array("reg" => 60, "mat" => 10),
            array("reg" => 60, "mat" => 12),
            array("reg" => 61, "mat" => 3),
            array("reg" => 62, "mat" => 10),
            array("reg" => 63, "mat" => 3),
            array("reg" => 64, "mat" => 2),
            array("reg" => 64, "mat" => 7),
            array("reg" => 64, "mat" => 13),
            array("reg" => 65, "mat" => 1),
            array("reg" => 65, "mat" => 2),
            array("reg" => 65, "mat" => 7),
            array("reg" => 65, "mat" => 13),
            array("reg" => 66, "mat" => 2),
            array("reg" => 66, "mat" => 13),
            array("reg" => 67, "mat" => 3),
            array("reg" => 68, "mat" => 1),
            array("reg" => 68, "mat" => 2),
            array("reg" => 68, "mat" => 7),
            array("reg" => 68, "mat" => 13),
            array("reg" => 69, "mat" => 1),
            array("reg" => 69, "mat" => 2),
            array("reg" => 69, "mat" => 4),
            array("reg" => 69, "mat" => 7),
            array("reg" => 69, "mat" => 13),
            array("reg" => 70, "mat" => 1),
            array("reg" => 70, "mat" => 2),
            array("reg" => 70, "mat" => 4),
            array("reg" => 70, "mat" => 7),
            array("reg" => 70, "mat" => 13),
            array("reg" => 71, "mat" => 1),
            array("reg" => 71, "mat" => 2),
            array("reg" => 71, "mat" => 3),
            array("reg" => 71, "mat" => 11),
            array("reg" => 71, "mat" => 12),
            array("reg" => 72, "mat" => 1),
            array("reg" => 73, "mat" => 1),
            array("reg" => 74, "mat" => 1),
            array("reg" => 75, "mat" => 3),
            array("reg" => 76, "mat" => 1),
            array("reg" => 77, "mat" => 1),
            array("reg" => 77, "mat" => 2),
            array("reg" => 77, "mat" => 7),
            array("reg" => 77, "mat" => 13),
            array("reg" => 78, "mat" => 3),
            array("reg" => 79, "mat" => 10),
            array("reg" => 80, "mat" => 7),
            array("reg" => 80, "mat" => 8),
            array("reg" => 81, "mat" => 3),
            array("reg" => 82, "mat" => 3),
            array("reg" => 83, "mat" => 3),
            array("reg" => 83, "mat" => 9),
            array("reg" => 83, "mat" => 10),
            array("reg" => 83, "mat" => 12),
            array("reg" => 83, "mat" => 15),
            array("reg" => 84, "mat" => 3),
            array("reg" => 85, "mat" => 3),
            array("reg" => 85, "mat" => 10),
            array("reg" => 86, "mat" => 1),
            array("reg" => 86, "mat" => 2),
            array("reg" => 87, "mat" => 1),
            array("reg" => 87, "mat" => 2),
            array("reg" => 88, "mat" => 9),
            array("reg" => 88, "mat" => 10),
            array("reg" => 88, "mat" => 11),
            array("reg" => 88, "mat" => 12),
            array("reg" => 89, "mat" => 1),
            array("reg" => 89, "mat" => 3),
            array("reg" => 89, "mat" => 6),
            array("reg" => 90, "mat" => 1),
            array("reg" => 90, "mat" => 3),
            array("reg" => 90, "mat" => 6),
            array("reg" => 91, "mat" => 10),
            array("reg" => 91, "mat" => 11),
            array("reg" => 92, "mat" => 3),
            array("reg" => 93, "mat" => 3),
            array("reg" => 93, "mat" => 6),
            array("reg" => 94, "mat" => 9),
            array("reg" => 95, "mat" => 3),
            array("reg" => 96, "mat" => 3),
            array("reg" => 97, "mat" => 1),
            array("reg" => 97, "mat" => 2),
            array("reg" => 98, "mat" => 1),
            array("reg" => 98, "mat" => 2),
            array("reg" => 98, "mat" => 7),
            array("reg" => 98, "mat" => 11),
            array("reg" => 98, "mat" => 13),
            array("reg" => 99, "mat" => 9),
            array("reg" => 99, "mat" => 10),
            array("reg" => 99, "mat" => 11),
            array("reg" => 100, "mat" => 1),
            array("reg" => 100, "mat" => 2),
            array("reg" => 100, "mat" => 7),
            array("reg" => 100, "mat" => 13),
            array("reg" => 101, "mat" => 9),
            array("reg" => 101, "mat" => 10),
            array("reg" => 101, "mat" => 11),
            array("reg" => 101, "mat" => 12),
            array("reg" => 102, "mat" => 1),
            array("reg" => 102, "mat" => 2),
            array("reg" => 102, "mat" => 7),
            array("reg" => 102, "mat" => 13),
            array("reg" => 103, "mat" => 9),
            array("reg" => 103, "mat" => 10),
            array("reg" => 103, "mat" => 12),
            array("reg" => 104, "mat" => 6),
            array("reg" => 104, "mat" => 10),
            array("reg" => 104, "mat" => 14),
            array("reg" => 105, "mat" => 1),
            array("reg" => 105, "mat" => 2),
            array("reg" => 105, "mat" => 4),
            array("reg" => 105, "mat" => 7),
            array("reg" => 105, "mat" => 13),
            array("reg" => 106, "mat" => 1),
            array("reg" => 106, "mat" => 2),
            array("reg" => 106, "mat" => 7),
            array("reg" => 107, "mat" => 3),
            array("reg" => 108, "mat" => 3),
            array("reg" => 108, "mat" => 10),
            array("reg" => 108, "mat" => 12),
            array("reg" => 108, "mat" => 15),
            array("reg" => 109, "mat" => 3),
            array("reg" => 109, "mat" => 10),
            array("reg" => 109, "mat" => 14),
            array("reg" => 110, "mat" => 11),
            array("reg" => 111, "mat" => 6),
            array("reg" => 111, "mat" => 10),
            array("reg" => 111, "mat" => 14),
            array("reg" => 112, "mat" => 6),
            array("reg" => 113, "mat" => 1),
            array("reg" => 113, "mat" => 2),
            array("reg" => 113, "mat" => 7),
            array("reg" => 113, "mat" => 13),
            array("reg" => 114, "mat" => 1),
            array("reg" => 114, "mat" => 2),
            array("reg" => 114, "mat" => 7),
            array("reg" => 115, "mat" => 1),
            array("reg" => 116, "mat" => 1),
            array("reg" => 116, "mat" => 2),
            array("reg" => 116, "mat" => 4),
            array("reg" => 116, "mat" => 7),
            array("reg" => 116, "mat" => 13),
            array("reg" => 117, "mat" => 3),
            array("reg" => 117, "mat" => 9),
            array("reg" => 117, "mat" => 10),
            array("reg" => 117, "mat" => 12),
            array("reg" => 117, "mat" => 15),
            array("reg" => 118, "mat" => 3),
            array("reg" => 119, "mat" => 2),
            array("reg" => 120, "mat" => 3),
            array("reg" => 120, "mat" => 9),
            array("reg" => 120, "mat" => 10),
            array("reg" => 121, "mat" => 9),
            array("reg" => 122, "mat" => 9),
            array("reg" => 122, "mat" => 10),
            array("reg" => 122, "mat" => 12),
            array("reg" => 123, "mat" => 1),
            array("reg" => 123, "mat" => 2),
            array("reg" => 123, "mat" => 7),
            array("reg" => 123, "mat" => 13),
            array("reg" => 124, "mat" => 7),
            array("reg" => 124, "mat" => 8),
            array("reg" => 125, "mat" => 3),
            array("reg" => 126, "mat" => 3),
            array("reg" => 127, "mat" => 3),
            array("reg" => 127, "mat" => 6),
            array("reg" => 127, "mat" => 10),
            array("reg" => 127, "mat" => 15),
            array("reg" => 128, "mat" => 1),
            array("reg" => 128, "mat" => 2),
            array("reg" => 128, "mat" => 7),
            array("reg" => 128, "mat" => 13),
            array("reg" => 129, "mat" => 1),
            array("reg" => 129, "mat" => 2),
            array("reg" => 129, "mat" => 7),
            array("reg" => 129, "mat" => 13),
            array("reg" => 130, "mat" => 1),
            array("reg" => 130, "mat" => 2),
            array("reg" => 131, "mat" => 9),
            array("reg" => 131, "mat" => 11),
            array("reg" => 132, "mat" => 9),
            array("reg" => 133, "mat" => 9),
            array("reg" => 133, "mat" => 11),
            array("reg" => 134, "mat" => 1),
            array("reg" => 134, "mat" => 2),
            array("reg" => 134, "mat" => 6),
            array("reg" => 134, "mat" => 7),
            array("reg" => 135, "mat" => 6),
            array("reg" => 136, "mat" => 1),
            array("reg" => 136, "mat" => 2),
            array("reg" => 136, "mat" => 13),
            array("reg" => 137, "mat" => 9),
            array("reg" => 137, "mat" => 10),
            array("reg" => 138, "mat" => 3),
            array("reg" => 139, "mat" => 1),
            array("reg" => 139, "mat" => 2),
            array("reg" => 140, "mat" => 3),
            array("reg" => 140, "mat" => 6),
            array("reg" => 140, "mat" => 9),
            array("reg" => 140, "mat" => 10),
            array("reg" => 140, "mat" => 11),
            array("reg" => 140, "mat" => 12),
            array("reg" => 141, "mat" => 1),
            array("reg" => 142, "mat" => 3),
            array("reg" => 143, "mat" => 6),
            array("reg" => 144, "mat" => 1),
            array("reg" => 145, "mat" => 1),
            array("reg" => 145, "mat" => 2),
            array("reg" => 145, "mat" => 4),
            array("reg" => 145, "mat" => 7),
            array("reg" => 145, "mat" => 13),
            array("reg" => 146, "mat" => 9),
            array("reg" => 146, "mat" => 10),
            array("reg" => 147, "mat" => 1),
            array("reg" => 147, "mat" => 2),
            array("reg" => 147, "mat" => 7),
            array("reg" => 147, "mat" => 11),
            array("reg" => 148, "mat" => 1),
            array("reg" => 148, "mat" => 2),
            array("reg" => 148, "mat" => 7),
            array("reg" => 148, "mat" => 13),
            array("reg" => 149, "mat" => 1),
            array("reg" => 150, "mat" => 1),
            array("reg" => 150, "mat" => 2),
            array("reg" => 150, "mat" => 11),
            array("reg" => 151, "mat" => 1),
            array("reg" => 151, "mat" => 2),
            array("reg" => 151, "mat" => 7),
            array("reg" => 151, "mat" => 13),
            array("reg" => 152, "mat" => 1),
            array("reg" => 152, "mat" => 2),
            array("reg" => 152, "mat" => 13),
            array("reg" => 153, "mat" => 1),
            array("reg" => 153, "mat" => 2),
            array("reg" => 154, "mat" => 6),
            array("reg" => 154, "mat" => 9),
            array("reg" => 154, "mat" => 10),
            array("reg" => 155, "mat" => 6),
            array("reg" => 155, "mat" => 9),
            array("reg" => 155, "mat" => 10),
            array("reg" => 156, "mat" => 3),
            array("reg" => 157, "mat" => 1),
            array("reg" => 157, "mat" => 2),
            array("reg" => 157, "mat" => 7),
            array("reg" => 158, "mat" => 1),
            array("reg" => 158, "mat" => 2),
            array("reg" => 159, "mat" => 1),
            array("reg" => 159, "mat" => 2),
            array("reg" => 159, "mat" => 10),
            array("reg" => 160, "mat" => 9),
            array("reg" => 160, "mat" => 10),
            array("reg" => 161, "mat" => 1),
            array("reg" => 161, "mat" => 6),
            array("reg" => 161, "mat" => 10),
            array("reg" => 162, "mat" => 1),
            array("reg" => 162, "mat" => 2),
            array("reg" => 163, "mat" => 3),
            array("reg" => 164, "mat" => 1),
            array("reg" => 164, "mat" => 2),
            array("reg" => 165, "mat" => 1),
            array("reg" => 165, "mat" => 2),
            array("reg" => 166, "mat" => 9),
            array("reg" => 167, "mat" => 9),
            array("reg" => 167, "mat" => 10),
            array("reg" => 168, "mat" => 1),
            array("reg" => 168, "mat" => 6),
            array("reg" => 169, "mat" => 1),
            array("reg" => 169, "mat" => 6),
            array("reg" => 170, "mat" => 3),
            array("reg" => 170, "mat" => 9),
            array("reg" => 170, "mat" => 11),
            array("reg" => 171, "mat" => 1),
            array("reg" => 171, "mat" => 2),
            array("reg" => 171, "mat" => 3),
            array("reg" => 171, "mat" => 9),
            array("reg" => 171, "mat" => 10),
            array("reg" => 172, "mat" => 9),
            array("reg" => 173, "mat" => 4),
            array("reg" => 173, "mat" => 7),
            array("reg" => 174, "mat" => 10),
            array("reg" => 175, "mat" => 3),
            array("reg" => 175, "mat" => 9),
            array("reg" => 175, "mat" => 10),
            array("reg" => 175, "mat" => 11),
            array("reg" => 175, "mat" => 12),
            array("reg" => 176, "mat" => 9),
            array("reg" => 176, "mat" => 10),
            array("reg" => 176, "mat" => 12),
            array("reg" => 177, "mat" => 1),
            array("reg" => 177, "mat" => 2),
            array("reg" => 177, "mat" => 6),
            array("reg" => 178, "mat" => 9),
            array("reg" => 178, "mat" => 10),
            array("reg" => 178, "mat" => 12),
            array("reg" => 179, "mat" => 1),
            array("reg" => 179, "mat" => 6),
            array("reg" => 179, "mat" => 9),
            array("reg" => 179, "mat" => 10),
            array("reg" => 180, "mat" => 3),
            array("reg" => 181, "mat" => 3),
            array("reg" => 181, "mat" => 6),
            array("reg" => 181, "mat" => 9),
            array("reg" => 181, "mat" => 10),
            array("reg" => 181, "mat" => 11),
            array("reg" => 181, "mat" => 12),
            array("reg" => 182, "mat" => 10),
            array("reg" => 183, "mat" => 1),
            array("reg" => 183, "mat" => 2),
            array("reg" => 183, "mat" => 7),
            array("reg" => 183, "mat" => 13),
            array("reg" => 184, "mat" => 3),
            array("reg" => 184, "mat" => 9),
            array("reg" => 184, "mat" => 10),
            array("reg" => 184, "mat" => 11),
            array("reg" => 184, "mat" => 12),
            array("reg" => 185, "mat" => 3),
            array("reg" => 185, "mat" => 10),
            array("reg" => 185, "mat" => 12),
            array("reg" => 186, "mat" => 1),
            array("reg" => 186, "mat" => 2),
            array("reg" => 186, "mat" => 13),
            array("reg" => 187, "mat" => 4),
            array("reg" => 188, "mat" => 1),
            array("reg" => 188, "mat" => 2),
            array("reg" => 188, "mat" => 6),
            array("reg" => 189, "mat" => 2),
            array("reg" => 189, "mat" => 7),
            array("reg" => 190, "mat" => 3),
            array("reg" => 190, "mat" => 9),
            array("reg" => 190, "mat" => 10),
            array("reg" => 191, "mat" => 6),
            array("reg" => 192, "mat" => 1),
            array("reg" => 192, "mat" => 2),
            array("reg" => 192, "mat" => 7),
            array("reg" => 193, "mat" => 1),
            array("reg" => 193, "mat" => 2),
            array("reg" => 193, "mat" => 7),
            array("reg" => 193, "mat" => 13),
            array("reg" => 194, "mat" => 1),
            array("reg" => 194, "mat" => 2),
            array("reg" => 195, "mat" => 10),
            array("reg" => 196, "mat" => 6),
            array("reg" => 197, "mat" => 3),
            array("reg" => 197, "mat" => 6),
            array("reg" => 197, "mat" => 9),
            array("reg" => 197, "mat" => 10),
            array("reg" => 197, "mat" => 11),
            array("reg" => 197, "mat" => 12),
            array("reg" => 198, "mat" => 1),
            array("reg" => 198, "mat" => 2),
            array("reg" => 198, "mat" => 13),
            array("reg" => 199, "mat" => 1),
            array("reg" => 199, "mat" => 2),
            array("reg" => 200, "mat" => 2),
            array("reg" => 200, "mat" => 10),
            array("reg" => 201, "mat" => 9),
            array("reg" => 202, "mat" => 1),
            array("reg" => 202, "mat" => 2),
            array("reg" => 202, "mat" => 13),
            array("reg" => 203, "mat" => 1),
            array("reg" => 203, "mat" => 2),
            array("reg" => 203, "mat" => 13),
            array("reg" => 204, "mat" => 6),
            array("reg" => 205, "mat" => 3),
            array("reg" => 205, "mat" => 9),
            array("reg" => 205, "mat" => 10),
            array("reg" => 205, "mat" => 12),
            array("reg" => 205, "mat" => 15),
            array("reg" => 206, "mat" => 3),
            array("reg" => 207, "mat" => 10),
            array("reg" => 208, "mat" => 1),
            array("reg" => 208, "mat" => 2),
            array("reg" => 208, "mat" => 7),
            array("reg" => 209, "mat" => 3),
            array("reg" => 209, "mat" => 6),
            array("reg" => 209, "mat" => 9),
            array("reg" => 209, "mat" => 10),
            array("reg" => 209, "mat" => 11),
            array("reg" => 209, "mat" => 12),
            array("reg" => 210, "mat" => 1),
            array("reg" => 210, "mat" => 2),
            array("reg" => 210, "mat" => 7),
            array("reg" => 210, "mat" => 13),
            array("reg" => 211, "mat" => 3),
            array("reg" => 212, "mat" => 2),
            array("reg" => 212, "mat" => 4),
            array("reg" => 213, "mat" => 11),
            array("reg" => 214, "mat" => 1),
            array("reg" => 214, "mat" => 2),
            array("reg" => 214, "mat" => 7),
            array("reg" => 215, "mat" => 1),
            array("reg" => 215, "mat" => 2),
            array("reg" => 215, "mat" => 4),
            array("reg" => 215, "mat" => 13),
            array("reg" => 216, "mat" => 3),
            array("reg" => 216, "mat" => 6),
            array("reg" => 216, "mat" => 9),
            array("reg" => 216, "mat" => 10),
            array("reg" => 216, "mat" => 11),
            array("reg" => 216, "mat" => 12),
            array("reg" => 217, "mat" => 3),
            array("reg" => 218, "mat" => 3),
            array("reg" => 219, "mat" => 1),
            array("reg" => 219, "mat" => 2),
            array("reg" => 219, "mat" => 7),
            array("reg" => 220, "mat" => 3),
            array("reg" => 221, "mat" => 3),
            array("reg" => 221, "mat" => 9),
            array("reg" => 221, "mat" => 10),
            array("reg" => 221, "mat" => 12),
            array("reg" => 221, "mat" => 15),
            array("reg" => 222, "mat" => 6),
            array("reg" => 223, "mat" => 1),
            array("reg" => 223, "mat" => 2),
            array("reg" => 223, "mat" => 7),
            array("reg" => 223, "mat" => 13),
            array("reg" => 224, "mat" => 1),
            array("reg" => 224, "mat" => 2),
            array("reg" => 224, "mat" => 7),
            array("reg" => 224, "mat" => 13),
            array("reg" => 225, "mat" => 6),
            array("reg" => 226, "mat" => 1),
            array("reg" => 226, "mat" => 3),
            array("reg" => 226, "mat" => 9),
            array("reg" => 226, "mat" => 10),
            array("reg" => 226, "mat" => 11),
            array("reg" => 227, "mat" => 10),
            array("reg" => 227, "mat" => 12),
            array("reg" => 228, "mat" => 1),
            array("reg" => 228, "mat" => 2),
            array("reg" => 228, "mat" => 7),
            array("reg" => 228, "mat" => 9),
            array("reg" => 228, "mat" => 13),
            array("reg" => 229, "mat" => 1),
            array("reg" => 229, "mat" => 2),
            array("reg" => 230, "mat" => 1),
            array("reg" => 231, "mat" => 1),
            array("reg" => 231, "mat" => 2),
            array("reg" => 232, "mat" => 1),
            array("reg" => 232, "mat" => 6),
            array("reg" => 233, "mat" => 3),
            array("reg" => 234, "mat" => 1),
            array("reg" => 234, "mat" => 2),
            array("reg" => 235, "mat" => 1),
            array("reg" => 235, "mat" => 2),
            array("reg" => 235, "mat" => 7),
            array("reg" => 235, "mat" => 13),
            array("reg" => 236, "mat" => 2),
            array("reg" => 236, "mat" => 7),
            array("reg" => 236, "mat" => 9),
            array("reg" => 236, "mat" => 10),
            array("reg" => 236, "mat" => 13),
            array("reg" => 237, "mat" => 3),
            array("reg" => 238, "mat" => 2),
            array("reg" => 238, "mat" => 13),
            array("reg" => 239, "mat" => 1),
            array("reg" => 240, "mat" => 3),
            array("reg" => 240, "mat" => 9),
            array("reg" => 240, "mat" => 10),
            array("reg" => 240, "mat" => 11),
            array("reg" => 240, "mat" => 12),
            array("reg" => 241, "mat" => 3),
            array("reg" => 241, "mat" => 6),
            array("reg" => 241, "mat" => 9),
            array("reg" => 241, "mat" => 10),
            array("reg" => 241, "mat" => 11),
            array("reg" => 241, "mat" => 12),
            array("reg" => 242, "mat" => 1),
            array("reg" => 242, "mat" => 2),
            array("reg" => 243, "mat" => 1),
            array("reg" => 243, "mat" => 2),
            array("reg" => 244, "mat" => 1),
            array("reg" => 245, "mat" => 10),
            array("reg" => 246, "mat" => 1),
            array("reg" => 246, "mat" => 2),
            array("reg" => 246, "mat" => 7),
            array("reg" => 246, "mat" => 13),
            array("reg" => 247, "mat" => 4),
            array("reg" => 248, "mat" => 6),
            array("reg" => 249, "mat" => 9),
            array("reg" => 249, "mat" => 10),
            array("reg" => 250, "mat" => 7),
            array("reg" => 250, "mat" => 8),
            array("reg" => 251, "mat" => 6),
            array("reg" => 252, "mat" => 1),
            array("reg" => 252, "mat" => 2),
            array("reg" => 252, "mat" => 7),
            array("reg" => 253, "mat" => 3),
            array("reg" => 254, "mat" => 1),
            array("reg" => 254, "mat" => 2),
            array("reg" => 254, "mat" => 7),
            array("reg" => 254, "mat" => 13),
            array("reg" => 255, "mat" => 3),
            array("reg" => 255, "mat" => 9),
            array("reg" => 255, "mat" => 10),
            array("reg" => 255, "mat" => 11),
            array("reg" => 255, "mat" => 12),
            array("reg" => 256, "mat" => 3),
            array("reg" => 256, "mat" => 10),
            array("reg" => 256, "mat" => 12),
            array("reg" => 257, "mat" => 3),
            array("reg" => 258, "mat" => 9),
            array("reg" => 258, "mat" => 10),
            array("reg" => 259, "mat" => 3),
            array("reg" => 259, "mat" => 6),
            array("reg" => 259, "mat" => 9),
            array("reg" => 259, "mat" => 10),
            array("reg" => 259, "mat" => 11),
            array("reg" => 259, "mat" => 12),
            array("reg" => 260, "mat" => 1),
            array("reg" => 260, "mat" => 3),
            array("reg" => 260, "mat" => 6),
            array("reg" => 260, "mat" => 10),
            array("reg" => 260, "mat" => 11),
            array("reg" => 261, "mat" => 3),
            array("reg" => 261, "mat" => 9),
            array("reg" => 261, "mat" => 10),
            array("reg" => 261, "mat" => 12),
            array("reg" => 261, "mat" => 15),
            array("reg" => 262, "mat" => 3),
            array("reg" => 262, "mat" => 6),
            array("reg" => 262, "mat" => 9),
            array("reg" => 262, "mat" => 10),
            array("reg" => 262, "mat" => 11),
            array("reg" => 262, "mat" => 12),
            array("reg" => 263, "mat" => 1),
            array("reg" => 263, "mat" => 2),
            array("reg" => 263, "mat" => 7),
            array("reg" => 264, "mat" => 9),
            array("reg" => 264, "mat" => 10),
            array("reg" => 265, "mat" => 1),
            array("reg" => 265, "mat" => 2),
            array("reg" => 265, "mat" => 4),
            array("reg" => 265, "mat" => 7),
            array("reg" => 265, "mat" => 13),
            array("reg" => 266, "mat" => 1),
            array("reg" => 266, "mat" => 2),
            array("reg" => 266, "mat" => 7),
            array("reg" => 266, "mat" => 9),
            array("reg" => 266, "mat" => 10),
            array("reg" => 266, "mat" => 11),
            array("reg" => 266, "mat" => 12),
            array("reg" => 267, "mat" => 1),
            array("reg" => 267, "mat" => 2),
            array("reg" => 267, "mat" => 7),
            array("reg" => 267, "mat" => 13),
            array("reg" => 268, "mat" => 1),
            array("reg" => 268, "mat" => 2),
            array("reg" => 268, "mat" => 4),
            array("reg" => 268, "mat" => 7),
            array("reg" => 268, "mat" => 13),
            array("reg" => 269, "mat" => 1),
            array("reg" => 270, "mat" => 3),
            array("reg" => 270, "mat" => 6),
            array("reg" => 270, "mat" => 9),
            array("reg" => 270, "mat" => 10),
            array("reg" => 270, "mat" => 11),
            array("reg" => 270, "mat" => 12),
            array("reg" => 271, "mat" => 1),
            array("reg" => 271, "mat" => 2),
            array("reg" => 271, "mat" => 4),
            array("reg" => 271, "mat" => 7),
            array("reg" => 271, "mat" => 13),
            array("reg" => 272, "mat" => 1),
            array("reg" => 272, "mat" => 3),
            array("reg" => 272, "mat" => 6),
            array("reg" => 272, "mat" => 10),
            array("reg" => 272, "mat" => 12),
            array("reg" => 272, "mat" => 15),
            array("reg" => 273, "mat" => 1),
            array("reg" => 273, "mat" => 3),
            array("reg" => 273, "mat" => 6),
            array("reg" => 273, "mat" => 10),
            array("reg" => 273, "mat" => 12),
            array("reg" => 273, "mat" => 15),
            array("reg" => 274, "mat" => 3),
            array("reg" => 274, "mat" => 6),
            array("reg" => 274, "mat" => 9),
            array("reg" => 274, "mat" => 10),
            array("reg" => 274, "mat" => 11),
            array("reg" => 274, "mat" => 12),
            array("reg" => 275, "mat" => 1),
            array("reg" => 275, "mat" => 2),
            array("reg" => 275, "mat" => 4),
            array("reg" => 275, "mat" => 7),
            array("reg" => 275, "mat" => 13),
            array("reg" => 276, "mat" => 6),
            array("reg" => 277, "mat" => 6),
            array("reg" => 278, "mat" => 3),
            array("reg" => 278, "mat" => 6),
            array("reg" => 278, "mat" => 9),
            array("reg" => 278, "mat" => 10),
            array("reg" => 278, "mat" => 11),
            array("reg" => 278, "mat" => 12),
            array("reg" => 279, "mat" => 1),
            array("reg" => 279, "mat" => 2),
            array("reg" => 279, "mat" => 4),
            array("reg" => 279, "mat" => 7),
            array("reg" => 279, "mat" => 13),
            array("reg" => 280, "mat" => 1),
            array("reg" => 280, "mat" => 2),
            array("reg" => 281, "mat" => 1),
            array("reg" => 281, "mat" => 2),
            array("reg" => 281, "mat" => 7),
            array("reg" => 281, "mat" => 13),
            array("reg" => 282, "mat" => 10),
            array("reg" => 283, "mat" => 1),
            array("reg" => 283, "mat" => 2),
            array("reg" => 283, "mat" => 4),
            array("reg" => 283, "mat" => 7),
            array("reg" => 283, "mat" => 13),
            array("reg" => 284, "mat" => 10),
            array("reg" => 285, "mat" => 3),
            array("reg" => 285, "mat" => 6),
            array("reg" => 285, "mat" => 9),
            array("reg" => 285, "mat" => 10),
            array("reg" => 285, "mat" => 11),
            array("reg" => 285, "mat" => 12),
            array("reg" => 286, "mat" => 6),
            array("reg" => 286, "mat" => 9),
            array("reg" => 286, "mat" => 10),
            array("reg" => 286, "mat" => 12),
            array("reg" => 287, "mat" => 3),
            array("reg" => 287, "mat" => 6),
            array("reg" => 287, "mat" => 9),
            array("reg" => 287, "mat" => 10),
            array("reg" => 287, "mat" => 11),
            array("reg" => 287, "mat" => 12),
            array("reg" => 288, "mat" => 3),
            array("reg" => 288, "mat" => 9),
            array("reg" => 288, "mat" => 10),
            array("reg" => 288, "mat" => 11),
            array("reg" => 288, "mat" => 12),
            array("reg" => 289, "mat" => 9),
            array("reg" => 289, "mat" => 11),
            array("reg" => 290, "mat" => 1),
            array("reg" => 290, "mat" => 2),
            array("reg" => 290, "mat" => 3),
            array("reg" => 290, "mat" => 6),
            array("reg" => 290, "mat" => 7),
            array("reg" => 291, "mat" => 3),
            array("reg" => 291, "mat" => 6),
            array("reg" => 291, "mat" => 9),
            array("reg" => 291, "mat" => 10),
            array("reg" => 291, "mat" => 11),
            array("reg" => 291, "mat" => 12),
            array("reg" => 292, "mat" => 3),
            array("reg" => 292, "mat" => 6),
            array("reg" => 292, "mat" => 9),
            array("reg" => 292, "mat" => 10),
            array("reg" => 292, "mat" => 11),
            array("reg" => 292, "mat" => 12),
            array("reg" => 293, "mat" => 1),
            array("reg" => 294, "mat" => 3),
            array("reg" => 294, "mat" => 6),
            array("reg" => 294, "mat" => 9),
            array("reg" => 294, "mat" => 10),
            array("reg" => 295, "mat" => 3),
            array("reg" => 295, "mat" => 6),
            array("reg" => 295, "mat" => 9),
            array("reg" => 295, "mat" => 10),
            array("reg" => 295, "mat" => 11),
            array("reg" => 295, "mat" => 12),
            array("reg" => 296, "mat" => 6),
            array("reg" => 297, "mat" => 3),
            array("reg" => 297, "mat" => 10),
            array("reg" => 298, "mat" => 1),
            array("reg" => 298, "mat" => 2),
            array("reg" => 298, "mat" => 9),
            array("reg" => 298, "mat" => 10),
            array("reg" => 299, "mat" => 3),
            array("reg" => 299, "mat" => 6),
            array("reg" => 299, "mat" => 9),
            array("reg" => 299, "mat" => 10),
            array("reg" => 299, "mat" => 11),
            array("reg" => 299, "mat" => 12),
            array("reg" => 300, "mat" => 1),
            array("reg" => 300, "mat" => 2),
            array("reg" => 300, "mat" => 4),
            array("reg" => 300, "mat" => 7),
            array("reg" => 300, "mat" => 13),
            array("reg" => 301, "mat" => 1),
            array("reg" => 301, "mat" => 2),
            array("reg" => 301, "mat" => 7),
            array("reg" => 301, "mat" => 13),
            array("reg" => 302, "mat" => 3),
            array("reg" => 302, "mat" => 6),
            array("reg" => 302, "mat" => 9),
            array("reg" => 302, "mat" => 10),
            array("reg" => 302, "mat" => 11),
            array("reg" => 302, "mat" => 12),
            array("reg" => 303, "mat" => 1),
            array("reg" => 303, "mat" => 3),
            array("reg" => 303, "mat" => 6),
            array("reg" => 303, "mat" => 10),
            array("reg" => 303, "mat" => 11),
            array("reg" => 304, "mat" => 1),
            array("reg" => 304, "mat" => 2),
            array("reg" => 304, "mat" => 4),
            array("reg" => 304, "mat" => 7),
            array("reg" => 304, "mat" => 13),
            array("reg" => 305, "mat" => 3),
            array("reg" => 305, "mat" => 6),
            array("reg" => 305, "mat" => 9),
            array("reg" => 305, "mat" => 10),
            array("reg" => 305, "mat" => 11),
            array("reg" => 305, "mat" => 12),
            array("reg" => 306, "mat" => 10),
            array("reg" => 307, "mat" => 1),
            array("reg" => 307, "mat" => 2),
            array("reg" => 307, "mat" => 4),
            array("reg" => 307, "mat" => 7),
            array("reg" => 307, "mat" => 13),
            array("reg" => 308, "mat" => 1),
            array("reg" => 308, "mat" => 2),
            array("reg" => 308, "mat" => 4),
            array("reg" => 308, "mat" => 7),
            array("reg" => 308, "mat" => 13),
            array("reg" => 309, "mat" => 3),
            array("reg" => 309, "mat" => 6),
            array("reg" => 309, "mat" => 9),
            array("reg" => 309, "mat" => 10),
            array("reg" => 309, "mat" => 11),
            array("reg" => 309, "mat" => 12),
            array("reg" => 310, "mat" => 1),
            array("reg" => 310, "mat" => 2),
            array("reg" => 310, "mat" => 4),
            array("reg" => 310, "mat" => 7),
            array("reg" => 310, "mat" => 13),
            array("reg" => 311, "mat" => 3),
            array("reg" => 311, "mat" => 6),
            array("reg" => 311, "mat" => 9),
            array("reg" => 311, "mat" => 10),
            array("reg" => 311, "mat" => 11),
            array("reg" => 311, "mat" => 12),
            array("reg" => 312, "mat" => 6),
            array("reg" => 313, "mat" => 1),
            array("reg" => 313, "mat" => 2),
            array("reg" => 313, "mat" => 4),
            array("reg" => 313, "mat" => 7),
            array("reg" => 313, "mat" => 13),
            array("reg" => 314, "mat" => 1),
            array("reg" => 314, "mat" => 2),
            array("reg" => 314, "mat" => 4),
            array("reg" => 314, "mat" => 7),
            array("reg" => 314, "mat" => 13),
            array("reg" => 315, "mat" => 3),
            array("reg" => 315, "mat" => 6),
            array("reg" => 315, "mat" => 9),
            array("reg" => 315, "mat" => 10),
            array("reg" => 315, "mat" => 11),
            array("reg" => 315, "mat" => 12),
            array("reg" => 316, "mat" => 3),
            array("reg" => 316, "mat" => 10),
            array("reg" => 316, "mat" => 12),
            array("reg" => 316, "mat" => 15),
            array("reg" => 317, "mat" => 1),
            array("reg" => 317, "mat" => 2),
            array("reg" => 317, "mat" => 4),
            array("reg" => 317, "mat" => 7),
            array("reg" => 317, "mat" => 13),
            array("reg" => 318, "mat" => 1),
            array("reg" => 318, "mat" => 2),
            array("reg" => 318, "mat" => 4),
            array("reg" => 318, "mat" => 7),
            array("reg" => 318, "mat" => 13),
            array("reg" => 319, "mat" => 1),
            array("reg" => 319, "mat" => 2),
            array("reg" => 319, "mat" => 4),
            array("reg" => 319, "mat" => 7),
            array("reg" => 319, "mat" => 13),
            array("reg" => 320, "mat" => 9),
            array("reg" => 320, "mat" => 10),
            array("reg" => 320, "mat" => 11),
            array("reg" => 320, "mat" => 12),
            array("reg" => 321, "mat" => 3),
            array("reg" => 321, "mat" => 6),
            array("reg" => 321, "mat" => 9),
            array("reg" => 321, "mat" => 10),
            array("reg" => 321, "mat" => 11),
            array("reg" => 321, "mat" => 12),
            array("reg" => 322, "mat" => 2),
            array("reg" => 322, "mat" => 3),
            array("reg" => 322, "mat" => 9),
            array("reg" => 322, "mat" => 10),
            array("reg" => 322, "mat" => 11),
            array("reg" => 322, "mat" => 13),
            array("reg" => 323, "mat" => 1),
            array("reg" => 323, "mat" => 2),
            array("reg" => 324, "mat" => 1),
            array("reg" => 324, "mat" => 2),
            array("reg" => 324, "mat" => 7),
            array("reg" => 325, "mat" => 1),
            array("reg" => 325, "mat" => 6),
            array("reg" => 325, "mat" => 9),
            array("reg" => 325, "mat" => 10),
            array("reg" => 326, "mat" => 2),
            array("reg" => 326, "mat" => 7),
            array("reg" => 326, "mat" => 13),
            array("reg" => 327, "mat" => 1),
            array("reg" => 327, "mat" => 2),
            array("reg" => 327, "mat" => 7),
            array("reg" => 327, "mat" => 13),
            array("reg" => 328, "mat" => 3),
            array("reg" => 328, "mat" => 6),
            array("reg" => 328, "mat" => 9),
            array("reg" => 328, "mat" => 10),
            array("reg" => 328, "mat" => 11),
            array("reg" => 328, "mat" => 12),
            array("reg" => 329, "mat" => 3),
            array("reg" => 329, "mat" => 6),
            array("reg" => 329, "mat" => 9),
            array("reg" => 329, "mat" => 10),
            array("reg" => 329, "mat" => 11),
            array("reg" => 329, "mat" => 12),
            array("reg" => 330, "mat" => 1),
            array("reg" => 330, "mat" => 2),
            array("reg" => 330, "mat" => 4),
            array("reg" => 330, "mat" => 7),
            array("reg" => 330, "mat" => 13),
            array("reg" => 331, "mat" => 1),
            array("reg" => 332, "mat" => 1),
            array("reg" => 332, "mat" => 3),
            array("reg" => 332, "mat" => 6),
            array("reg" => 332, "mat" => 10),
            array("reg" => 332, "mat" => 12),
            array("reg" => 332, "mat" => 15),
            array("reg" => 333, "mat" => 1),
            array("reg" => 333, "mat" => 6),
            array("reg" => 333, "mat" => 9),
            array("reg" => 333, "mat" => 10),
            array("reg" => 334, "mat" => 1),
            array("reg" => 334, "mat" => 2),
            array("reg" => 335, "mat" => 1),
            array("reg" => 335, "mat" => 2),
            array("reg" => 335, "mat" => 4),
            array("reg" => 335, "mat" => 7),
            array("reg" => 335, "mat" => 13),
            array("reg" => 336, "mat" => 1),
            array("reg" => 336, "mat" => 2),
            array("reg" => 337, "mat" => 9),
            array("reg" => 337, "mat" => 10),
            array("reg" => 337, "mat" => 11),
            array("reg" => 337, "mat" => 12),
            array("reg" => 338, "mat" => 3),
            array("reg" => 338, "mat" => 6),
            array("reg" => 338, "mat" => 9),
            array("reg" => 338, "mat" => 10),
            array("reg" => 338, "mat" => 11),
            array("reg" => 338, "mat" => 12),
            array("reg" => 339, "mat" => 1),
            array("reg" => 339, "mat" => 2),
            array("reg" => 339, "mat" => 4),
            array("reg" => 339, "mat" => 7),
            array("reg" => 339, "mat" => 13),
            array("reg" => 340, "mat" => 1),
            array("reg" => 340, "mat" => 2),
            array("reg" => 340, "mat" => 3),
            array("reg" => 340, "mat" => 7),
            array("reg" => 340, "mat" => 10),
            array("reg" => 340, "mat" => 11),
            array("reg" => 341, "mat" => 1),
            array("reg" => 341, "mat" => 2),
            array("reg" => 341, "mat" => 7),
            array("reg" => 342, "mat" => 1),
            array("reg" => 342, "mat" => 2),
            array("reg" => 342, "mat" => 7),
            array("reg" => 342, "mat" => 13),
            array("reg" => 343, "mat" => 1),
            array("reg" => 343, "mat" => 2),
            array("reg" => 343, "mat" => 4),
            array("reg" => 343, "mat" => 7),
            array("reg" => 343, "mat" => 13),
            array("reg" => 344, "mat" => 4),
            array("reg" => 345, "mat" => 1),
            array("reg" => 345, "mat" => 2),
            array("reg" => 345, "mat" => 4),
            array("reg" => 345, "mat" => 7),
            array("reg" => 345, "mat" => 13),
            array("reg" => 346, "mat" => 3),
            array("reg" => 346, "mat" => 6),
            array("reg" => 346, "mat" => 9),
            array("reg" => 346, "mat" => 10),
            array("reg" => 346, "mat" => 11),
            array("reg" => 346, "mat" => 12),
            array("reg" => 347, "mat" => 9),
            array("reg" => 348, "mat" => 1),
            array("reg" => 348, "mat" => 3),
            array("reg" => 348, "mat" => 10),
            array("reg" => 348, "mat" => 11),
            array("reg" => 348, "mat" => 12),
            array("reg" => 349, "mat" => 3),
            array("reg" => 349, "mat" => 6),
            array("reg" => 349, "mat" => 9),
            array("reg" => 349, "mat" => 10),
            array("reg" => 349, "mat" => 11),
            array("reg" => 349, "mat" => 12),
            array("reg" => 350, "mat" => 3),
            array("reg" => 351, "mat" => 3),
            array("reg" => 351, "mat" => 9),
            array("reg" => 351, "mat" => 10),
            array("reg" => 351, "mat" => 11),
            array("reg" => 351, "mat" => 12),
            array("reg" => 352, "mat" => 10),
            array("reg" => 353, "mat" => 1),
            array("reg" => 353, "mat" => 2),
            array("reg" => 353, "mat" => 6),
            array("reg" => 353, "mat" => 10),
            array("reg" => 354, "mat" => 1),
            array("reg" => 354, "mat" => 2),
            array("reg" => 354, "mat" => 7),
            array("reg" => 355, "mat" => 1),
            array("reg" => 355, "mat" => 2),
            array("reg" => 355, "mat" => 7),
            array("reg" => 356, "mat" => 3),
            array("reg" => 356, "mat" => 6),
            array("reg" => 356, "mat" => 9),
            array("reg" => 356, "mat" => 10),
            array("reg" => 356, "mat" => 11),
            array("reg" => 356, "mat" => 12),
            array("reg" => 357, "mat" => 1),
            array("reg" => 357, "mat" => 2),
            array("reg" => 357, "mat" => 4),
            array("reg" => 357, "mat" => 7),
            array("reg" => 357, "mat" => 13),
            array("reg" => 358, "mat" => 10),
            array("reg" => 359, "mat" => 3),
            array("reg" => 359, "mat" => 6),
            array("reg" => 359, "mat" => 9),
            array("reg" => 359, "mat" => 10),
            array("reg" => 359, "mat" => 11),
            array("reg" => 359, "mat" => 12),
            array("reg" => 360, "mat" => 1),
            array("reg" => 360, "mat" => 2),
            array("reg" => 360, "mat" => 7),
            array("reg" => 361, "mat" => 6),
            array("reg" => 362, "mat" => 3),
            array("reg" => 362, "mat" => 6),
            array("reg" => 362, "mat" => 9),
            array("reg" => 362, "mat" => 10),
            array("reg" => 362, "mat" => 11),
            array("reg" => 362, "mat" => 12),
            array("reg" => 363, "mat" => 1),
            array("reg" => 363, "mat" => 2),
            array("reg" => 363, "mat" => 4),
            array("reg" => 363, "mat" => 7),
            array("reg" => 363, "mat" => 13),
            array("reg" => 364, "mat" => 7),
            array("reg" => 364, "mat" => 9),
            array("reg" => 364, "mat" => 11),
            array("reg" => 364, "mat" => 13),
            array("reg" => 365, "mat" => 3),
            array("reg" => 365, "mat" => 6),
            array("reg" => 365, "mat" => 9),
            array("reg" => 365, "mat" => 10),
            array("reg" => 365, "mat" => 11),
            array("reg" => 365, "mat" => 12),
            array("reg" => 366, "mat" => 2),
            array("reg" => 366, "mat" => 13),
            array("reg" => 367, "mat" => 1),
            array("reg" => 367, "mat" => 2),
            array("reg" => 367, "mat" => 7),
            array("reg" => 368, "mat" => 1),
            array("reg" => 368, "mat" => 2),
            array("reg" => 368, "mat" => 7),
            array("reg" => 369, "mat" => 2),
            array("reg" => 369, "mat" => 10),
            array("reg" => 370, "mat" => 1),
            array("reg" => 370, "mat" => 2),
            array("reg" => 370, "mat" => 4),
            array("reg" => 370, "mat" => 7),
            array("reg" => 370, "mat" => 13),
            array("reg" => 371, "mat" => 1),
            array("reg" => 371, "mat" => 2),
            array("reg" => 371, "mat" => 13),
            array("reg" => 372, "mat" => 1),
            array("reg" => 372, "mat" => 2),
            array("reg" => 372, "mat" => 7),
            array("reg" => 373, "mat" => 3),
            array("reg" => 373, "mat" => 6),
            array("reg" => 373, "mat" => 9),
            array("reg" => 373, "mat" => 10),
            array("reg" => 373, "mat" => 11),
            array("reg" => 373, "mat" => 12),
            array("reg" => 374, "mat" => 3),
            array("reg" => 374, "mat" => 6),
            array("reg" => 374, "mat" => 9),
            array("reg" => 374, "mat" => 10),
            array("reg" => 374, "mat" => 11),
            array("reg" => 374, "mat" => 12),
            array("reg" => 375, "mat" => 3),
            array("reg" => 376, "mat" => 3),
            array("reg" => 376, "mat" => 6),
            array("reg" => 376, "mat" => 9),
            array("reg" => 376, "mat" => 10),
            array("reg" => 376, "mat" => 11),
            array("reg" => 376, "mat" => 12),
            array("reg" => 377, "mat" => 1),
            array("reg" => 377, "mat" => 2),
            array("reg" => 377, "mat" => 4),
            array("reg" => 377, "mat" => 7),
            array("reg" => 377, "mat" => 13),
            array("reg" => 378, "mat" => 1),
            array("reg" => 378, "mat" => 2),
            array("reg" => 378, "mat" => 4),
            array("reg" => 378, "mat" => 7),
            array("reg" => 378, "mat" => 13),
            array("reg" => 379, "mat" => 2),
            array("reg" => 379, "mat" => 13),
            array("reg" => 380, "mat" => 1),
            array("reg" => 380, "mat" => 2),
            array("reg" => 380, "mat" => 7),
            array("reg" => 381, "mat" => 1),
            array("reg" => 381, "mat" => 2),
            array("reg" => 381, "mat" => 7),
            array("reg" => 382, "mat" => 1),
            array("reg" => 382, "mat" => 2),
            array("reg" => 382, "mat" => 4),
            array("reg" => 382, "mat" => 7),
            array("reg" => 382, "mat" => 13),
            array("reg" => 383, "mat" => 1),
            array("reg" => 383, "mat" => 2),
            array("reg" => 383, "mat" => 9),
            array("reg" => 383, "mat" => 10),
            array("reg" => 383, "mat" => 11),
            array("reg" => 384, "mat" => 1),
            array("reg" => 384, "mat" => 2),
            array("reg" => 384, "mat" => 4),
            array("reg" => 384, "mat" => 7),
            array("reg" => 384, "mat" => 13),
            array("reg" => 385, "mat" => 3),
            array("reg" => 385, "mat" => 10),
            array("reg" => 386, "mat" => 1),
            array("reg" => 386, "mat" => 2),
            array("reg" => 386, "mat" => 4),
            array("reg" => 386, "mat" => 7),
            array("reg" => 386, "mat" => 13),
            array("reg" => 387, "mat" => 1),
            array("reg" => 387, "mat" => 2),
            array("reg" => 388, "mat" => 1),
            array("reg" => 388, "mat" => 2),
            array("reg" => 388, "mat" => 3),
            array("reg" => 388, "mat" => 7),
            array("reg" => 389, "mat" => 1),
            array("reg" => 390, "mat" => 1),
            array("reg" => 390, "mat" => 2),
            array("reg" => 390, "mat" => 4),
            array("reg" => 390, "mat" => 7),
            array("reg" => 390, "mat" => 13),
            array("reg" => 391, "mat" => 3),
            array("reg" => 391, "mat" => 6),
            array("reg" => 391, "mat" => 9),
            array("reg" => 391, "mat" => 10),
            array("reg" => 391, "mat" => 11),
            array("reg" => 391, "mat" => 12),
            array("reg" => 392, "mat" => 3),
            array("reg" => 392, "mat" => 6),
            array("reg" => 392, "mat" => 9),
            array("reg" => 392, "mat" => 10),
            array("reg" => 392, "mat" => 11),
            array("reg" => 392, "mat" => 12),
            array("reg" => 393, "mat" => 1),
            array("reg" => 393, "mat" => 2),
            array("reg" => 393, "mat" => 7),
            array("reg" => 394, "mat" => 1),
            array("reg" => 394, "mat" => 2),
            array("reg" => 394, "mat" => 4),
            array("reg" => 394, "mat" => 7),
            array("reg" => 394, "mat" => 13),
            array("reg" => 395, "mat" => 2),
            array("reg" => 396, "mat" => 3),
            array("reg" => 397, "mat" => 1),
            array("reg" => 397, "mat" => 2),
            array("reg" => 397, "mat" => 7),
            array("reg" => 397, "mat" => 13),
            array("reg" => 398, "mat" => 1),
            array("reg" => 398, "mat" => 2),
            array("reg" => 399, "mat" => 3),
            array("reg" => 400, "mat" => 1),
            array("reg" => 400, "mat" => 2),
            array("reg" => 400, "mat" => 11),
            array("reg" => 400, "mat" => 13),
            array("reg" => 401, "mat" => 10),
            array("reg" => 402, "mat" => 1),
            array("reg" => 402, "mat" => 2),
            array("reg" => 403, "mat" => 10),
            array("reg" => 403, "mat" => 12),
            array("reg" => 404, "mat" => 1),
            array("reg" => 404, "mat" => 2),
            array("reg" => 404, "mat" => 7),
            array("reg" => 404, "mat" => 13),
            array("reg" => 405, "mat" => 10),
            array("reg" => 406, "mat" => 3),
            array("reg" => 406, "mat" => 6),
            array("reg" => 406, "mat" => 9),
            array("reg" => 406, "mat" => 10),
            array("reg" => 406, "mat" => 11),
            array("reg" => 406, "mat" => 12),
            array("reg" => 407, "mat" => 3),
            array("reg" => 407, "mat" => 6),
            array("reg" => 407, "mat" => 9),
            array("reg" => 407, "mat" => 10),
            array("reg" => 407, "mat" => 11),
            array("reg" => 407, "mat" => 12),
            array("reg" => 408, "mat" => 4),
            array("reg" => 409, "mat" => 2),
            array("reg" => 409, "mat" => 13),
            array("reg" => 410, "mat" => 1),
            array("reg" => 410, "mat" => 2),
            array("reg" => 410, "mat" => 4),
            array("reg" => 410, "mat" => 7),
            array("reg" => 410, "mat" => 13),
            array("reg" => 411, "mat" => 1),
            array("reg" => 411, "mat" => 2),
            array("reg" => 411, "mat" => 3),
            array("reg" => 411, "mat" => 7),
            array("reg" => 411, "mat" => 11),
            array("reg" => 411, "mat" => 13),
            array("reg" => 412, "mat" => 3),
            array("reg" => 413, "mat" => 8),
            array("reg" => 414, "mat" => 6),
            array("reg" => 415, "mat" => 1),
            array("reg" => 415, "mat" => 2),
            array("reg" => 415, "mat" => 4),
            array("reg" => 415, "mat" => 7),
            array("reg" => 415, "mat" => 13),
            array("reg" => 416, "mat" => 3),
            array("reg" => 416, "mat" => 6),
            array("reg" => 416, "mat" => 9),
            array("reg" => 416, "mat" => 10),
            array("reg" => 416, "mat" => 11),
            array("reg" => 416, "mat" => 12),
            array("reg" => 417, "mat" => 3),
            array("reg" => 417, "mat" => 6),
            array("reg" => 417, "mat" => 9),
            array("reg" => 417, "mat" => 10),
            array("reg" => 417, "mat" => 11),
            array("reg" => 417, "mat" => 12),
            array("reg" => 418, "mat" => 9),
            array("reg" => 418, "mat" => 10),
            array("reg" => 419, "mat" => 1),
            array("reg" => 419, "mat" => 2),
            array("reg" => 419, "mat" => 3),
            array("reg" => 419, "mat" => 4),
            array("reg" => 419, "mat" => 7),
            array("reg" => 419, "mat" => 9),
            array("reg" => 419, "mat" => 10),
            array("reg" => 419, "mat" => 11),
            array("reg" => 419, "mat" => 12),
            array("reg" => 419, "mat" => 13),
            array("reg" => 420, "mat" => 3),
            array("reg" => 420, "mat" => 6),
            array("reg" => 420, "mat" => 9),
            array("reg" => 420, "mat" => 10),
            array("reg" => 420, "mat" => 11),
            array("reg" => 420, "mat" => 12),
            array("reg" => 421, "mat" => 1),
            array("reg" => 421, "mat" => 2),
            array("reg" => 421, "mat" => 4),
            array("reg" => 421, "mat" => 7),
            array("reg" => 421, "mat" => 13),
            array("reg" => 422, "mat" => 3),
            array("reg" => 423, "mat" => 1),
            array("reg" => 423, "mat" => 2),
            array("reg" => 423, "mat" => 4),
            array("reg" => 423, "mat" => 7),
            array("reg" => 423, "mat" => 13),
            array("reg" => 424, "mat" => 1),
            array("reg" => 424, "mat" => 2),
            array("reg" => 424, "mat" => 7),
            array("reg" => 425, "mat" => 6),
            array("reg" => 426, "mat" => 6),
            array("reg" => 427, "mat" => 3),
            array("reg" => 427, "mat" => 6),
            array("reg" => 427, "mat" => 9),
            array("reg" => 427, "mat" => 10),
            array("reg" => 427, "mat" => 11),
            array("reg" => 427, "mat" => 12),
            array("reg" => 428, "mat" => 9),
            array("reg" => 428, "mat" => 10),
            array("reg" => 428, "mat" => 11),
            array("reg" => 428, "mat" => 12),
            array("reg" => 429, "mat" => 1),
            array("reg" => 429, "mat" => 2),
            array("reg" => 429, "mat" => 4),
            array("reg" => 429, "mat" => 7),
            array("reg" => 429, "mat" => 13),
            array("reg" => 430, "mat" => 3),
            array("reg" => 430, "mat" => 6),
            array("reg" => 430, "mat" => 9),
            array("reg" => 430, "mat" => 10),
            array("reg" => 430, "mat" => 11),
            array("reg" => 430, "mat" => 12),
            array("reg" => 431, "mat" => 4),
            array("reg" => 432, "mat" => 3),
            array("reg" => 432, "mat" => 6),
            array("reg" => 432, "mat" => 9),
            array("reg" => 432, "mat" => 10),
            array("reg" => 432, "mat" => 11),
            array("reg" => 432, "mat" => 12),
            array("reg" => 433, "mat" => 3),
            array("reg" => 433, "mat" => 6),
            array("reg" => 433, "mat" => 9),
            array("reg" => 433, "mat" => 10),
            array("reg" => 433, "mat" => 11),
            array("reg" => 433, "mat" => 12),
            array("reg" => 434, "mat" => 1),
            array("reg" => 434, "mat" => 2),
            array("reg" => 434, "mat" => 4),
            array("reg" => 434, "mat" => 7),
            array("reg" => 434, "mat" => 13),
            array("reg" => 435, "mat" => 9),
            array("reg" => 435, "mat" => 10),
            array("reg" => 436, "mat" => 8),
            array("reg" => 437, "mat" => 1),
            array("reg" => 437, "mat" => 2),
            array("reg" => 437, "mat" => 4),
            array("reg" => 437, "mat" => 7),
            array("reg" => 437, "mat" => 13),
            array("reg" => 438, "mat" => 3),
            array("reg" => 438, "mat" => 6),
            array("reg" => 438, "mat" => 9),
            array("reg" => 438, "mat" => 10),
            array("reg" => 438, "mat" => 11),
            array("reg" => 438, "mat" => 12),
            array("reg" => 439, "mat" => 1),
            array("reg" => 439, "mat" => 2),
            array("reg" => 439, "mat" => 4),
            array("reg" => 439, "mat" => 7),
            array("reg" => 439, "mat" => 13),
            array("reg" => 440, "mat" => 1),
            array("reg" => 440, "mat" => 2),
            array("reg" => 440, "mat" => 4),
            array("reg" => 440, "mat" => 7),
            array("reg" => 440, "mat" => 13),
            array("reg" => 441, "mat" => 2),
            array("reg" => 441, "mat" => 13),
            array("reg" => 442, "mat" => 3),
            array("reg" => 442, "mat" => 6),
            array("reg" => 443, "mat" => 3),
            array("reg" => 443, "mat" => 6),
            array("reg" => 443, "mat" => 9),
            array("reg" => 443, "mat" => 10),
            array("reg" => 443, "mat" => 11),
            array("reg" => 443, "mat" => 12),
            array("reg" => 444, "mat" => 1),
            array("reg" => 444, "mat" => 2),
            array("reg" => 444, "mat" => 4),
            array("reg" => 444, "mat" => 7),
            array("reg" => 444, "mat" => 13),
            array("reg" => 445, "mat" => 1),
            array("reg" => 445, "mat" => 2),
            array("reg" => 445, "mat" => 4),
            array("reg" => 445, "mat" => 7),
            array("reg" => 445, "mat" => 13),
            array("reg" => 446, "mat" => 6),
            array("reg" => 446, "mat" => 10),
            array("reg" => 447, "mat" => 2),
            array("reg" => 447, "mat" => 7),
            array("reg" => 448, "mat" => 1),
            array("reg" => 448, "mat" => 2),
            array("reg" => 448, "mat" => 4),
            array("reg" => 448, "mat" => 7),
            array("reg" => 448, "mat" => 13),
            array("reg" => 449, "mat" => 2),
            array("reg" => 449, "mat" => 13),
            array("reg" => 450, "mat" => 1),
            array("reg" => 450, "mat" => 2),
            array("reg" => 450, "mat" => 4),
            array("reg" => 450, "mat" => 7),
            array("reg" => 450, "mat" => 13),
            array("reg" => 451, "mat" => 2),
            array("reg" => 451, "mat" => 13),
            array("reg" => 452, "mat" => 1),
            array("reg" => 452, "mat" => 3),
            array("reg" => 452, "mat" => 7),
            array("reg" => 452, "mat" => 10),
            array("reg" => 452, "mat" => 13),
            array("reg" => 453, "mat" => 1),
            array("reg" => 453, "mat" => 2),
            array("reg" => 453, "mat" => 13),
            array("reg" => 454, "mat" => 1),
            array("reg" => 454, "mat" => 2),
            array("reg" => 454, "mat" => 4),
            array("reg" => 454, "mat" => 6),
            array("reg" => 454, "mat" => 7),
            array("reg" => 454, "mat" => 13),
            array("reg" => 455, "mat" => 1),
            array("reg" => 455, "mat" => 2),
            array("reg" => 456, "mat" => 2),
            array("reg" => 456, "mat" => 13),
            array("reg" => 457, "mat" => 1),
            array("reg" => 457, "mat" => 2),
            array("reg" => 457, "mat" => 13),
            array("reg" => 458, "mat" => 1),
            array("reg" => 458, "mat" => 2),
            array("reg" => 458, "mat" => 4),
            array("reg" => 458, "mat" => 7),
            array("reg" => 458, "mat" => 13),
            array("reg" => 459, "mat" => 1),
            array("reg" => 459, "mat" => 2),
            array("reg" => 459, "mat" => 4),
            array("reg" => 459, "mat" => 7),
            array("reg" => 459, "mat" => 13),
            array("reg" => 460, "mat" => 3),
            array("reg" => 460, "mat" => 6),
            array("reg" => 460, "mat" => 9),
            array("reg" => 460, "mat" => 10),
            array("reg" => 460, "mat" => 11),
            array("reg" => 460, "mat" => 12),
            array("reg" => 461, "mat" => 1),
            array("reg" => 461, "mat" => 2),
            array("reg" => 461, "mat" => 4),
            array("reg" => 461, "mat" => 7),
            array("reg" => 461, "mat" => 13),
            array("reg" => 462, "mat" => 1),
            array("reg" => 462, "mat" => 2),
            array("reg" => 462, "mat" => 4),
            array("reg" => 462, "mat" => 7),
            array("reg" => 462, "mat" => 13),
            array("reg" => 463, "mat" => 1),
            array("reg" => 463, "mat" => 2),
            array("reg" => 463, "mat" => 4),
            array("reg" => 463, "mat" => 7),
            array("reg" => 463, "mat" => 13),
            array("reg" => 464, "mat" => 3),
            array("reg" => 464, "mat" => 6),
            array("reg" => 464, "mat" => 9),
            array("reg" => 464, "mat" => 10),
            array("reg" => 464, "mat" => 11),
            array("reg" => 464, "mat" => 12),
            array("reg" => 465, "mat" => 3),
            array("reg" => 465, "mat" => 6),
            array("reg" => 465, "mat" => 9),
            array("reg" => 465, "mat" => 10),
            array("reg" => 465, "mat" => 11),
            array("reg" => 465, "mat" => 12),
            array("reg" => 466, "mat" => 3),
            array("reg" => 466, "mat" => 6),
            array("reg" => 466, "mat" => 9),
            array("reg" => 466, "mat" => 10),
            array("reg" => 466, "mat" => 11),
            array("reg" => 466, "mat" => 12),
            array("reg" => 467, "mat" => 3),
            array("reg" => 467, "mat" => 6),
            array("reg" => 467, "mat" => 9),
            array("reg" => 467, "mat" => 10),
            array("reg" => 467, "mat" => 11),
            array("reg" => 467, "mat" => 12),
            array("reg" => 468, "mat" => 9),
            array("reg" => 468, "mat" => 10),
            array("reg" => 468, "mat" => 11),
            array("reg" => 469, "mat" => 1),
            array("reg" => 469, "mat" => 2),
            array("reg" => 469, "mat" => 4),
            array("reg" => 469, "mat" => 7),
            array("reg" => 469, "mat" => 13),
            array("reg" => 470, "mat" => 3),
            array("reg" => 470, "mat" => 6),
            array("reg" => 470, "mat" => 9),
            array("reg" => 470, "mat" => 10),
            array("reg" => 470, "mat" => 11),
            array("reg" => 470, "mat" => 12),
            array("reg" => 471, "mat" => 3),
            array("reg" => 471, "mat" => 6),
            array("reg" => 471, "mat" => 9),
            array("reg" => 471, "mat" => 10),
            array("reg" => 471, "mat" => 11),
            array("reg" => 471, "mat" => 12),
            array("reg" => 472, "mat" => 1),
            array("reg" => 472, "mat" => 2),
            array("reg" => 472, "mat" => 4),
            array("reg" => 472, "mat" => 7),
            array("reg" => 472, "mat" => 13),
            array("reg" => 473, "mat" => 1),
            array("reg" => 473, "mat" => 2),
            array("reg" => 474, "mat" => 3),
            array("reg" => 474, "mat" => 6),
            array("reg" => 474, "mat" => 9),
            array("reg" => 474, "mat" => 10),
            array("reg" => 474, "mat" => 11),
            array("reg" => 474, "mat" => 12),
            array("reg" => 475, "mat" => 3),
            array("reg" => 475, "mat" => 6),
            array("reg" => 475, "mat" => 9),
            array("reg" => 475, "mat" => 10),
            array("reg" => 475, "mat" => 11),
            array("reg" => 475, "mat" => 12),
            array("reg" => 476, "mat" => 1),
            array("reg" => 476, "mat" => 6),
            array("reg" => 477, "mat" => 3),
            array("reg" => 477, "mat" => 6),
            array("reg" => 477, "mat" => 9),
            array("reg" => 477, "mat" => 10),
            array("reg" => 477, "mat" => 11),
            array("reg" => 477, "mat" => 12),
            array("reg" => 478, "mat" => 10),
            array("reg" => 479, "mat" => 3),
            array("reg" => 479, "mat" => 10),
            array("reg" => 480, "mat" => 3),
            array("reg" => 480, "mat" => 10),
            array("reg" => 480, "mat" => 12),
            array("reg" => 481, "mat" => 3),
            array("reg" => 481, "mat" => 6),
            array("reg" => 481, "mat" => 9),
            array("reg" => 481, "mat" => 10),
            array("reg" => 481, "mat" => 11),
            array("reg" => 481, "mat" => 12),
            array("reg" => 482, "mat" => 3),
            array("reg" => 482, "mat" => 6),
            array("reg" => 482, "mat" => 9),
            array("reg" => 482, "mat" => 10),
            array("reg" => 482, "mat" => 11),
            array("reg" => 482, "mat" => 12),
            array("reg" => 483, "mat" => 3),
            array("reg" => 483, "mat" => 10),
            array("reg" => 484, "mat" => 2),
            array("reg" => 484, "mat" => 13),
            array("reg" => 485, "mat" => 11),
            array("reg" => 486, "mat" => 1),
            array("reg" => 486, "mat" => 2),
            array("reg" => 486, "mat" => 4),
            array("reg" => 486, "mat" => 7),
            array("reg" => 486, "mat" => 13),
            array("reg" => 487, "mat" => 3),
            array("reg" => 487, "mat" => 6),
            array("reg" => 487, "mat" => 9),
            array("reg" => 487, "mat" => 10),
            array("reg" => 487, "mat" => 11),
            array("reg" => 487, "mat" => 12),
            array("reg" => 488, "mat" => 1),
            array("reg" => 488, "mat" => 2),
            array("reg" => 488, "mat" => 4),
            array("reg" => 488, "mat" => 7),
            array("reg" => 488, "mat" => 13),
            array("reg" => 489, "mat" => 1),
            array("reg" => 489, "mat" => 2),
            array("reg" => 489, "mat" => 4),
            array("reg" => 489, "mat" => 7),
            array("reg" => 489, "mat" => 13),
            array("reg" => 490, "mat" => 1),
            array("reg" => 490, "mat" => 2),
            array("reg" => 490, "mat" => 4),
            array("reg" => 490, "mat" => 7),
            array("reg" => 490, "mat" => 13),
            array("reg" => 491, "mat" => 1),
            array("reg" => 491, "mat" => 2),
            array("reg" => 491, "mat" => 4),
            array("reg" => 491, "mat" => 7),
            array("reg" => 491, "mat" => 13),
            array("reg" => 492, "mat" => 3),
            array("reg" => 492, "mat" => 6),
            array("reg" => 492, "mat" => 9),
            array("reg" => 492, "mat" => 10),
            array("reg" => 492, "mat" => 11),
            array("reg" => 492, "mat" => 12),
            array("reg" => 493, "mat" => 3),
            array("reg" => 493, "mat" => 10),
            array("reg" => 494, "mat" => 3),
            array("reg" => 494, "mat" => 6),
            array("reg" => 494, "mat" => 9),
            array("reg" => 494, "mat" => 10),
            array("reg" => 494, "mat" => 11),
            array("reg" => 494, "mat" => 12),
            array("reg" => 495, "mat" => 1),
            array("reg" => 495, "mat" => 2),
            array("reg" => 495, "mat" => 4),
            array("reg" => 495, "mat" => 7),
            array("reg" => 495, "mat" => 13),
            array("reg" => 496, "mat" => 1),
            array("reg" => 496, "mat" => 3),
            array("reg" => 496, "mat" => 9),
            array("reg" => 496, "mat" => 10),
            array("reg" => 496, "mat" => 11),
            array("reg" => 496, "mat" => 12),
            array("reg" => 497, "mat" => 1),
            array("reg" => 497, "mat" => 2),
            array("reg" => 497, "mat" => 4),
            array("reg" => 497, "mat" => 7),
            array("reg" => 497, "mat" => 13),
            array("reg" => 498, "mat" => 1),
            array("reg" => 498, "mat" => 2),
            array("reg" => 498, "mat" => 4),
            array("reg" => 498, "mat" => 7),
            array("reg" => 498, "mat" => 13),
            array("reg" => 499, "mat" => 1),
            array("reg" => 499, "mat" => 2),
            array("reg" => 499, "mat" => 4),
            array("reg" => 499, "mat" => 7),
            array("reg" => 499, "mat" => 13),
            array("reg" => 500, "mat" => 6),
            array("reg" => 501, "mat" => 4),
            array("reg" => 502, "mat" => 1),
            array("reg" => 502, "mat" => 2),
            array("reg" => 502, "mat" => 4),
            array("reg" => 502, "mat" => 7),
            array("reg" => 502, "mat" => 13),
            array("reg" => 503, "mat" => 1),
            array("reg" => 503, "mat" => 2),
            array("reg" => 503, "mat" => 4),
            array("reg" => 503, "mat" => 7),
            array("reg" => 503, "mat" => 13),
            array("reg" => 504, "mat" => 3),
            array("reg" => 504, "mat" => 6),
            array("reg" => 504, "mat" => 9),
            array("reg" => 504, "mat" => 10),
            array("reg" => 504, "mat" => 11),
            array("reg" => 504, "mat" => 12),
            array("reg" => 505, "mat" => 1),
            array("reg" => 505, "mat" => 2),
            array("reg" => 505, "mat" => 4),
            array("reg" => 505, "mat" => 7),
            array("reg" => 505, "mat" => 13),
            array("reg" => 506, "mat" => 3),
            array("reg" => 506, "mat" => 6),
            array("reg" => 506, "mat" => 9),
            array("reg" => 506, "mat" => 10),
            array("reg" => 506, "mat" => 11),
            array("reg" => 506, "mat" => 12),
            array("reg" => 507, "mat" => 3),
            array("reg" => 507, "mat" => 6),
            array("reg" => 507, "mat" => 9),
            array("reg" => 507, "mat" => 10),
            array("reg" => 507, "mat" => 11),
            array("reg" => 507, "mat" => 12),
            array("reg" => 508, "mat" => 7),
            array("reg" => 508, "mat" => 13),
            array("reg" => 509, "mat" => 1),
            array("reg" => 509, "mat" => 2),
            array("reg" => 509, "mat" => 7),
            array("reg" => 509, "mat" => 13),
            array("reg" => 510, "mat" => 1),
            array("reg" => 510, "mat" => 2),
            array("reg" => 510, "mat" => 13),
            array("reg" => 511, "mat" => 1),
            array("reg" => 511, "mat" => 2),
            array("reg" => 511, "mat" => 4),
            array("reg" => 511, "mat" => 7),
            array("reg" => 511, "mat" => 13),
            array("reg" => 512, "mat" => 3),
            array("reg" => 512, "mat" => 6),
            array("reg" => 512, "mat" => 9),
            array("reg" => 512, "mat" => 10),
            array("reg" => 512, "mat" => 11),
            array("reg" => 512, "mat" => 12),
            array("reg" => 513, "mat" => 1),
            array("reg" => 513, "mat" => 2),
            array("reg" => 513, "mat" => 4),
            array("reg" => 513, "mat" => 7),
            array("reg" => 513, "mat" => 13),
            array("reg" => 514, "mat" => 1),
            array("reg" => 514, "mat" => 2),
            array("reg" => 514, "mat" => 4),
            array("reg" => 514, "mat" => 7),
            array("reg" => 514, "mat" => 13),
            array("reg" => 515, "mat" => 1),
            array("reg" => 515, "mat" => 2),
            array("reg" => 515, "mat" => 4),
            array("reg" => 515, "mat" => 7),
            array("reg" => 515, "mat" => 13),
            array("reg" => 516, "mat" => 1),
            array("reg" => 516, "mat" => 2),
            array("reg" => 516, "mat" => 4),
            array("reg" => 516, "mat" => 7),
            array("reg" => 516, "mat" => 13),
            array("reg" => 517, "mat" => 3),
            array("reg" => 517, "mat" => 6),
            array("reg" => 517, "mat" => 9),
            array("reg" => 517, "mat" => 10),
            array("reg" => 517, "mat" => 11),
            array("reg" => 517, "mat" => 12),
            array("reg" => 518, "mat" => 1),
            array("reg" => 518, "mat" => 2),
            array("reg" => 518, "mat" => 4),
            array("reg" => 518, "mat" => 7),
            array("reg" => 518, "mat" => 13),
            array("reg" => 519, "mat" => 1),
            array("reg" => 519, "mat" => 2),
            array("reg" => 519, "mat" => 4),
            array("reg" => 519, "mat" => 7),
            array("reg" => 519, "mat" => 13),
            array("reg" => 520, "mat" => 3),
            array("reg" => 520, "mat" => 6),
            array("reg" => 520, "mat" => 9),
            array("reg" => 520, "mat" => 10),
            array("reg" => 520, "mat" => 11),
            array("reg" => 520, "mat" => 12),
            array("reg" => 521, "mat" => 4),
            array("reg" => 522, "mat" => 4),
            array("reg" => 523, "mat" => 1),
            array("reg" => 523, "mat" => 2),
            array("reg" => 523, "mat" => 4),
            array("reg" => 523, "mat" => 7),
            array("reg" => 523, "mat" => 13),
            array("reg" => 524, "mat" => 3),
            array("reg" => 524, "mat" => 6),
            array("reg" => 524, "mat" => 9),
            array("reg" => 524, "mat" => 10),
            array("reg" => 524, "mat" => 11),
            array("reg" => 524, "mat" => 12),
            array("reg" => 525, "mat" => 3),
            array("reg" => 525, "mat" => 6),
            array("reg" => 525, "mat" => 9),
            array("reg" => 525, "mat" => 10),
            array("reg" => 525, "mat" => 11),
            array("reg" => 525, "mat" => 12),
            array("reg" => 526, "mat" => 1),
            array("reg" => 526, "mat" => 2),
            array("reg" => 526, "mat" => 4),
            array("reg" => 526, "mat" => 7),
            array("reg" => 526, "mat" => 13),
            array("reg" => 527, "mat" => 1),
            array("reg" => 527, "mat" => 2),
            array("reg" => 527, "mat" => 4),
            array("reg" => 527, "mat" => 7),
            array("reg" => 527, "mat" => 13),
            array("reg" => 528, "mat" => 2),
            array("reg" => 529, "mat" => 1),
            array("reg" => 529, "mat" => 2),
            array("reg" => 529, "mat" => 4),
            array("reg" => 529, "mat" => 7),
            array("reg" => 529, "mat" => 13),
            array("reg" => 530, "mat" => 3),
            array("reg" => 530, "mat" => 6),
            array("reg" => 530, "mat" => 9),
            array("reg" => 530, "mat" => 10),
            array("reg" => 530, "mat" => 11),
            array("reg" => 530, "mat" => 12),
            array("reg" => 531, "mat" => 1),
            array("reg" => 531, "mat" => 2),
            array("reg" => 531, "mat" => 4),
            array("reg" => 531, "mat" => 7),
            array("reg" => 531, "mat" => 13),
            array("reg" => 532, "mat" => 3),
            array("reg" => 532, "mat" => 6),
            array("reg" => 532, "mat" => 9),
            array("reg" => 532, "mat" => 10),
            array("reg" => 532, "mat" => 11),
            array("reg" => 532, "mat" => 12),
            array("reg" => 533, "mat" => 3),
            array("reg" => 533, "mat" => 6),
            array("reg" => 533, "mat" => 9),
            array("reg" => 533, "mat" => 10),
            array("reg" => 533, "mat" => 11),
            array("reg" => 533, "mat" => 12),
            array("reg" => 534, "mat" => 1),
            array("reg" => 534, "mat" => 2),
            array("reg" => 534, "mat" => 4),
            array("reg" => 534, "mat" => 7),
            array("reg" => 534, "mat" => 13),
            array("reg" => 535, "mat" => 1),
            array("reg" => 535, "mat" => 2),
            array("reg" => 535, "mat" => 4),
            array("reg" => 535, "mat" => 7),
            array("reg" => 535, "mat" => 13),
            array("reg" => 536, "mat" => 1),
            array("reg" => 536, "mat" => 2),
            array("reg" => 536, "mat" => 4),
            array("reg" => 536, "mat" => 7),
            array("reg" => 536, "mat" => 13),
            array("reg" => 537, "mat" => 1),
            array("reg" => 537, "mat" => 2),
            array("reg" => 537, "mat" => 4),
            array("reg" => 537, "mat" => 7),
            array("reg" => 537, "mat" => 13),
            array("reg" => 538, "mat" => 2),
            array("reg" => 538, "mat" => 7),
            array("reg" => 539, "mat" => 1),
            array("reg" => 539, "mat" => 2),
            array("reg" => 539, "mat" => 4),
            array("reg" => 539, "mat" => 7),
            array("reg" => 539, "mat" => 13),
            array("reg" => 540, "mat" => 1),
            array("reg" => 540, "mat" => 2),
            array("reg" => 540, "mat" => 4),
            array("reg" => 540, "mat" => 7),
            array("reg" => 540, "mat" => 13),
            array("reg" => 541, "mat" => 1),
            array("reg" => 541, "mat" => 2),
            array("reg" => 541, "mat" => 4),
            array("reg" => 541, "mat" => 7),
            array("reg" => 541, "mat" => 13),
            array("reg" => 542, "mat" => 3),
            array("reg" => 542, "mat" => 6),
            array("reg" => 542, "mat" => 9),
            array("reg" => 542, "mat" => 10),
            array("reg" => 542, "mat" => 11),
            array("reg" => 542, "mat" => 12),
            array("reg" => 543, "mat" => 3),
            array("reg" => 543, "mat" => 6),
            array("reg" => 543, "mat" => 9),
            array("reg" => 543, "mat" => 10),
            array("reg" => 543, "mat" => 11),
            array("reg" => 543, "mat" => 12),
            array("reg" => 544, "mat" => 1),
            array("reg" => 544, "mat" => 2),
            array("reg" => 544, "mat" => 4),
            array("reg" => 544, "mat" => 7),
            array("reg" => 544, "mat" => 13),
            array("reg" => 545, "mat" => 1),
            array("reg" => 545, "mat" => 2),
            array("reg" => 545, "mat" => 4),
            array("reg" => 545, "mat" => 7),
            array("reg" => 545, "mat" => 13),
            array("reg" => 546, "mat" => 3),
            array("reg" => 546, "mat" => 6),
            array("reg" => 546, "mat" => 9),
            array("reg" => 546, "mat" => 10),
            array("reg" => 546, "mat" => 11),
            array("reg" => 546, "mat" => 12),
            array("reg" => 547, "mat" => 1),
            array("reg" => 547, "mat" => 2),
            array("reg" => 547, "mat" => 4),
            array("reg" => 547, "mat" => 7),
            array("reg" => 547, "mat" => 13),
            array("reg" => 548, "mat" => 1),
            array("reg" => 548, "mat" => 2),
            array("reg" => 548, "mat" => 4),
            array("reg" => 548, "mat" => 7),
            array("reg" => 548, "mat" => 13),
            array("reg" => 549, "mat" => 3),
            array("reg" => 549, "mat" => 6),
            array("reg" => 549, "mat" => 9),
            array("reg" => 549, "mat" => 10),
            array("reg" => 549, "mat" => 11),
            array("reg" => 549, "mat" => 12),
            array("reg" => 550, "mat" => 3),
            array("reg" => 550, "mat" => 6),
            array("reg" => 550, "mat" => 9),
            array("reg" => 550, "mat" => 10),
            array("reg" => 550, "mat" => 11),
            array("reg" => 550, "mat" => 12),
            array("reg" => 551, "mat" => 3),
            array("reg" => 551, "mat" => 6),
            array("reg" => 551, "mat" => 9),
            array("reg" => 551, "mat" => 10),
            array("reg" => 551, "mat" => 11),
            array("reg" => 551, "mat" => 12),
            array("reg" => 552, "mat" => 1),
            array("reg" => 552, "mat" => 2),
            array("reg" => 552, "mat" => 4),
            array("reg" => 552, "mat" => 7),
            array("reg" => 552, "mat" => 13),
            array("reg" => 553, "mat" => 1),
            array("reg" => 553, "mat" => 2),
            array("reg" => 553, "mat" => 4),
            array("reg" => 553, "mat" => 7),
            array("reg" => 553, "mat" => 13),
            array("reg" => 554, "mat" => 3),
            array("reg" => 554, "mat" => 6),
            array("reg" => 554, "mat" => 9),
            array("reg" => 554, "mat" => 10),
            array("reg" => 554, "mat" => 11),
            array("reg" => 554, "mat" => 12),
            array("reg" => 555, "mat" => 3),
            array("reg" => 555, "mat" => 6),
            array("reg" => 555, "mat" => 9),
            array("reg" => 555, "mat" => 10),
            array("reg" => 555, "mat" => 11),
            array("reg" => 555, "mat" => 12),
            array("reg" => 556, "mat" => 1),
            array("reg" => 556, "mat" => 2),
            array("reg" => 556, "mat" => 4),
            array("reg" => 556, "mat" => 7),
            array("reg" => 556, "mat" => 13),
            array("reg" => 557, "mat" => 1),
            array("reg" => 557, "mat" => 2),
            array("reg" => 557, "mat" => 4),
            array("reg" => 557, "mat" => 7),
            array("reg" => 557, "mat" => 13),
            array("reg" => 558, "mat" => 1),
            array("reg" => 558, "mat" => 3),
            array("reg" => 558, "mat" => 6),
            array("reg" => 558, "mat" => 9),
            array("reg" => 558, "mat" => 10),
            array("reg" => 558, "mat" => 11),
            array("reg" => 558, "mat" => 12),
            array("reg" => 559, "mat" => 1),
            array("reg" => 559, "mat" => 3),
            array("reg" => 559, "mat" => 6),
            array("reg" => 559, "mat" => 9),
            array("reg" => 559, "mat" => 10),
            array("reg" => 559, "mat" => 11),
            array("reg" => 559, "mat" => 12),
            array("reg" => 560, "mat" => 1),
            array("reg" => 560, "mat" => 2),
            array("reg" => 560, "mat" => 4),
            array("reg" => 560, "mat" => 7),
            array("reg" => 560, "mat" => 13),
            array("reg" => 561, "mat" => 1),
            array("reg" => 561, "mat" => 2),
            array("reg" => 561, "mat" => 4),
            array("reg" => 561, "mat" => 7),
            array("reg" => 561, "mat" => 13),
            array("reg" => 562, "mat" => 1),
            array("reg" => 562, "mat" => 2),
            array("reg" => 562, "mat" => 4),
            array("reg" => 562, "mat" => 7),
            array("reg" => 562, "mat" => 13),
            array("reg" => 563, "mat" => 3),
            array("reg" => 563, "mat" => 6),
            array("reg" => 563, "mat" => 9),
            array("reg" => 563, "mat" => 10),
            array("reg" => 563, "mat" => 11),
            array("reg" => 563, "mat" => 12),
            array("reg" => 564, "mat" => 1),
            array("reg" => 564, "mat" => 2),
            array("reg" => 564, "mat" => 4),
            array("reg" => 564, "mat" => 7),
            array("reg" => 564, "mat" => 13),
            array("reg" => 565, "mat" => 3),
            array("reg" => 565, "mat" => 6),
            array("reg" => 565, "mat" => 9),
            array("reg" => 565, "mat" => 10),
            array("reg" => 565, "mat" => 11),
            array("reg" => 565, "mat" => 12),
            array("reg" => 566, "mat" => 3),
            array("reg" => 566, "mat" => 6),
            array("reg" => 566, "mat" => 9),
            array("reg" => 566, "mat" => 10),
            array("reg" => 566, "mat" => 11),
            array("reg" => 566, "mat" => 12),
            array("reg" => 567, "mat" => 1),
            array("reg" => 567, "mat" => 2),
            array("reg" => 567, "mat" => 4),
            array("reg" => 567, "mat" => 7),
            array("reg" => 567, "mat" => 13),
            array("reg" => 568, "mat" => 3),
            array("reg" => 568, "mat" => 6),
            array("reg" => 568, "mat" => 9),
            array("reg" => 568, "mat" => 10),
            array("reg" => 568, "mat" => 11),
            array("reg" => 568, "mat" => 12),
            array("reg" => 569, "mat" => 3),
            array("reg" => 569, "mat" => 6),
            array("reg" => 569, "mat" => 9),
            array("reg" => 569, "mat" => 10),
            array("reg" => 569, "mat" => 11),
            array("reg" => 569, "mat" => 12),
            array("reg" => 570, "mat" => 1),
            array("reg" => 570, "mat" => 2),
            array("reg" => 570, "mat" => 4),
            array("reg" => 570, "mat" => 7),
            array("reg" => 570, "mat" => 13),
            array("reg" => 571, "mat" => 3),
            array("reg" => 571, "mat" => 6),
            array("reg" => 571, "mat" => 9),
            array("reg" => 571, "mat" => 10),
            array("reg" => 571, "mat" => 11),
            array("reg" => 571, "mat" => 12),
            array("reg" => 572, "mat" => 1),
            array("reg" => 572, "mat" => 2),
            array("reg" => 572, "mat" => 4),
            array("reg" => 572, "mat" => 7),
            array("reg" => 572, "mat" => 13),
            array("reg" => 573, "mat" => 1),
            array("reg" => 573, "mat" => 2),
            array("reg" => 573, "mat" => 4),
            array("reg" => 573, "mat" => 7),
            array("reg" => 573, "mat" => 13),
            array("reg" => 574, "mat" => 1),
            array("reg" => 574, "mat" => 2),
            array("reg" => 574, "mat" => 4),
            array("reg" => 574, "mat" => 7),
            array("reg" => 574, "mat" => 13),
            array("reg" => 575, "mat" => 1),
            array("reg" => 575, "mat" => 2),
            array("reg" => 575, "mat" => 13),
            array("reg" => 576, "mat" => 1),
            array("reg" => 577, "mat" => 1),
            array("reg" => 577, "mat" => 2),
            array("reg" => 577, "mat" => 7),
            array("reg" => 578, "mat" => 1),
            array("reg" => 578, "mat" => 2),
            array("reg" => 579, "mat" => 1),
            array("reg" => 580, "mat" => 1),
            array("reg" => 580, "mat" => 7),
            array("reg" => 581, "mat" => 1),
            array("reg" => 581, "mat" => 2),
            array("reg" => 581, "mat" => 7),
            array("reg" => 582, "mat" => 1),
            array("reg" => 582, "mat" => 2),
            array("reg" => 582, "mat" => 4),
            array("reg" => 582, "mat" => 7),
            array("reg" => 582, "mat" => 13),
            array("reg" => 583, "mat" => 3),
            array("reg" => 583, "mat" => 6),
            array("reg" => 583, "mat" => 9),
            array("reg" => 583, "mat" => 10),
            array("reg" => 583, "mat" => 11),
            array("reg" => 583, "mat" => 12),
            array("reg" => 584, "mat" => 3),
            array("reg" => 584, "mat" => 6),
            array("reg" => 584, "mat" => 9),
            array("reg" => 584, "mat" => 10),
            array("reg" => 584, "mat" => 11),
            array("reg" => 584, "mat" => 12),
            array("reg" => 585, "mat" => 1),
            array("reg" => 585, "mat" => 2),
            array("reg" => 585, "mat" => 4),
            array("reg" => 585, "mat" => 7),
            array("reg" => 585, "mat" => 13),
            array("reg" => 586, "mat" => 1),
            array("reg" => 586, "mat" => 2),
            array("reg" => 586, "mat" => 4),
            array("reg" => 586, "mat" => 7),
            array("reg" => 586, "mat" => 13),
            array("reg" => 587, "mat" => 1),
            array("reg" => 587, "mat" => 2),
            array("reg" => 587, "mat" => 4),
            array("reg" => 587, "mat" => 7),
            array("reg" => 587, "mat" => 13),
            array("reg" => 588, "mat" => 3),
            array("reg" => 588, "mat" => 6),
            array("reg" => 588, "mat" => 9),
            array("reg" => 588, "mat" => 10),
            array("reg" => 588, "mat" => 11),
            array("reg" => 588, "mat" => 12),
            array("reg" => 589, "mat" => 1),
            array("reg" => 589, "mat" => 2),
            array("reg" => 589, "mat" => 4),
            array("reg" => 589, "mat" => 7),
            array("reg" => 589, "mat" => 13),
            array("reg" => 590, "mat" => 3),
            array("reg" => 590, "mat" => 6),
            array("reg" => 590, "mat" => 9),
            array("reg" => 590, "mat" => 10),
            array("reg" => 590, "mat" => 11),
            array("reg" => 590, "mat" => 12),
            array("reg" => 591, "mat" => 1),
            array("reg" => 591, "mat" => 2),
            array("reg" => 591, "mat" => 4),
            array("reg" => 591, "mat" => 7),
            array("reg" => 591, "mat" => 13),
            array("reg" => 592, "mat" => 1),
            array("reg" => 592, "mat" => 2),
            array("reg" => 592, "mat" => 4),
            array("reg" => 592, "mat" => 7),
            array("reg" => 592, "mat" => 13),
            array("reg" => 593, "mat" => 3),
            array("reg" => 593, "mat" => 6),
            array("reg" => 593, "mat" => 9),
            array("reg" => 593, "mat" => 10),
            array("reg" => 593, "mat" => 11),
            array("reg" => 593, "mat" => 12),
            array("reg" => 594, "mat" => 1),
            array("reg" => 594, "mat" => 2),
            array("reg" => 594, "mat" => 4),
            array("reg" => 594, "mat" => 7),
            array("reg" => 594, "mat" => 13),
            array("reg" => 595, "mat" => 3),
            array("reg" => 595, "mat" => 6),
            array("reg" => 595, "mat" => 9),
            array("reg" => 595, "mat" => 10),
            array("reg" => 595, "mat" => 11),
            array("reg" => 595, "mat" => 12),
            array("reg" => 596, "mat" => 4),
            array("reg" => 597, "mat" => 4),
            array("reg" => 598, "mat" => 3),
            array("reg" => 598, "mat" => 6),
            array("reg" => 598, "mat" => 9),
            array("reg" => 598, "mat" => 10),
            array("reg" => 598, "mat" => 11),
            array("reg" => 598, "mat" => 12),
            array("reg" => 599, "mat" => 1),
            array("reg" => 599, "mat" => 2),
            array("reg" => 599, "mat" => 4),
            array("reg" => 599, "mat" => 7),
            array("reg" => 599, "mat" => 13),
            array("reg" => 600, "mat" => 3),
            array("reg" => 600, "mat" => 6),
            array("reg" => 600, "mat" => 9),
            array("reg" => 600, "mat" => 10),
            array("reg" => 600, "mat" => 11),
            array("reg" => 600, "mat" => 12),
            array("reg" => 601, "mat" => 1),
            array("reg" => 601, "mat" => 2),
            array("reg" => 601, "mat" => 4),
            array("reg" => 601, "mat" => 7),
            array("reg" => 601, "mat" => 13),
            array("reg" => 602, "mat" => 3),
            array("reg" => 602, "mat" => 6),
            array("reg" => 602, "mat" => 9),
            array("reg" => 602, "mat" => 10),
            array("reg" => 602, "mat" => 11),
            array("reg" => 602, "mat" => 12),
            array("reg" => 603, "mat" => 1),
            array("reg" => 603, "mat" => 2),
            array("reg" => 603, "mat" => 4),
            array("reg" => 603, "mat" => 7),
            array("reg" => 603, "mat" => 13),
            array("reg" => 604, "mat" => 1),
            array("reg" => 604, "mat" => 2),
            array("reg" => 604, "mat" => 4),
            array("reg" => 604, "mat" => 7),
            array("reg" => 604, "mat" => 13),
            array("reg" => 605, "mat" => 3),
            array("reg" => 605, "mat" => 6),
            array("reg" => 605, "mat" => 9),
            array("reg" => 605, "mat" => 10),
            array("reg" => 605, "mat" => 11),
            array("reg" => 605, "mat" => 12),
            array("reg" => 606, "mat" => 1),
            array("reg" => 606, "mat" => 2),
            array("reg" => 606, "mat" => 4),
            array("reg" => 606, "mat" => 7),
            array("reg" => 606, "mat" => 13),
            array("reg" => 607, "mat" => 1),
            array("reg" => 607, "mat" => 2),
            array("reg" => 607, "mat" => 4),
            array("reg" => 607, "mat" => 7),
            array("reg" => 607, "mat" => 13),
            array("reg" => 608, "mat" => 3),
            array("reg" => 608, "mat" => 6),
            array("reg" => 608, "mat" => 9),
            array("reg" => 608, "mat" => 10),
            array("reg" => 608, "mat" => 11),
            array("reg" => 608, "mat" => 12),
            array("reg" => 609, "mat" => 1),
            array("reg" => 609, "mat" => 2),
            array("reg" => 609, "mat" => 4),
            array("reg" => 609, "mat" => 7),
            array("reg" => 609, "mat" => 13),
            array("reg" => 610, "mat" => 1),
            array("reg" => 610, "mat" => 2),
            array("reg" => 610, "mat" => 4),
            array("reg" => 610, "mat" => 7),
            array("reg" => 610, "mat" => 13),
            array("reg" => 611, "mat" => 1),
            array("reg" => 611, "mat" => 2),
            array("reg" => 611, "mat" => 4),
            array("reg" => 611, "mat" => 7),
            array("reg" => 611, "mat" => 13),
            array("reg" => 612, "mat" => 1),
            array("reg" => 612, "mat" => 2),
            array("reg" => 612, "mat" => 4),
            array("reg" => 612, "mat" => 7),
            array("reg" => 612, "mat" => 13),
            array("reg" => 613, "mat" => 3),
            array("reg" => 613, "mat" => 6),
            array("reg" => 613, "mat" => 9),
            array("reg" => 613, "mat" => 10),
            array("reg" => 613, "mat" => 11),
            array("reg" => 613, "mat" => 12),
            array("reg" => 614, "mat" => 1),
            array("reg" => 614, "mat" => 2),
            array("reg" => 614, "mat" => 4),
            array("reg" => 614, "mat" => 7),
            array("reg" => 614, "mat" => 13),
            array("reg" => 615, "mat" => 3),
            array("reg" => 615, "mat" => 6),
            array("reg" => 615, "mat" => 9),
            array("reg" => 615, "mat" => 10),
            array("reg" => 615, "mat" => 11),
            array("reg" => 615, "mat" => 12),
            array("reg" => 616, "mat" => 1),
            array("reg" => 616, "mat" => 2),
            array("reg" => 616, "mat" => 4),
            array("reg" => 616, "mat" => 7),
            array("reg" => 616, "mat" => 13),
            array("reg" => 617, "mat" => 1),
            array("reg" => 617, "mat" => 2),
            array("reg" => 617, "mat" => 4),
            array("reg" => 617, "mat" => 7),
            array("reg" => 617, "mat" => 13),
            array("reg" => 618, "mat" => 1),
            array("reg" => 618, "mat" => 2),
            array("reg" => 618, "mat" => 4),
            array("reg" => 618, "mat" => 7),
            array("reg" => 618, "mat" => 13),
            array("reg" => 619, "mat" => 1),
            array("reg" => 619, "mat" => 2),
            array("reg" => 619, "mat" => 4),
            array("reg" => 619, "mat" => 7),
            array("reg" => 619, "mat" => 13),
            array("reg" => 620, "mat" => 1),
            array("reg" => 620, "mat" => 2),
            array("reg" => 620, "mat" => 4),
            array("reg" => 620, "mat" => 7),
            array("reg" => 620, "mat" => 13),
            array("reg" => 621, "mat" => 1),
            array("reg" => 621, "mat" => 2),
            array("reg" => 621, "mat" => 4),
            array("reg" => 621, "mat" => 7),
            array("reg" => 621, "mat" => 13),
            array("reg" => 622, "mat" => 3),
            array("reg" => 622, "mat" => 6),
            array("reg" => 622, "mat" => 9),
            array("reg" => 622, "mat" => 10),
            array("reg" => 622, "mat" => 11),
            array("reg" => 622, "mat" => 12),
            array("reg" => 623, "mat" => 3),
            array("reg" => 623, "mat" => 6),
            array("reg" => 623, "mat" => 9),
            array("reg" => 623, "mat" => 10),
            array("reg" => 623, "mat" => 11),
            array("reg" => 623, "mat" => 12),
            array("reg" => 624, "mat" => 1),
            array("reg" => 624, "mat" => 2),
            array("reg" => 624, "mat" => 4),
            array("reg" => 624, "mat" => 7),
            array("reg" => 624, "mat" => 13),
            array("reg" => 625, "mat" => 3),
            array("reg" => 625, "mat" => 6),
            array("reg" => 625, "mat" => 9),
            array("reg" => 625, "mat" => 10),
            array("reg" => 625, "mat" => 11),
            array("reg" => 625, "mat" => 12),
            array("reg" => 626, "mat" => 1),
            array("reg" => 626, "mat" => 3),
            array("reg" => 626, "mat" => 6),
            array("reg" => 626, "mat" => 10),
            array("reg" => 627, "mat" => 4),
            array("reg" => 628, "mat" => 3),
            array("reg" => 628, "mat" => 7),
            array("reg" => 628, "mat" => 9),
            array("reg" => 628, "mat" => 10),
            array("reg" => 628, "mat" => 11),
            array("reg" => 629, "mat" => 3),
            array("reg" => 629, "mat" => 6),
            array("reg" => 629, "mat" => 9),
            array("reg" => 629, "mat" => 10),
            array("reg" => 629, "mat" => 11),
            array("reg" => 629, "mat" => 12),
            array("reg" => 630, "mat" => 3),
            array("reg" => 630, "mat" => 6),
            array("reg" => 630, "mat" => 9),
            array("reg" => 630, "mat" => 10),
            array("reg" => 630, "mat" => 11),
            array("reg" => 630, "mat" => 12),
            array("reg" => 631, "mat" => 3),
            array("reg" => 631, "mat" => 6),
            array("reg" => 631, "mat" => 9),
            array("reg" => 631, "mat" => 10),
            array("reg" => 631, "mat" => 11),
            array("reg" => 631, "mat" => 12),
            array("reg" => 632, "mat" => 1),
            array("reg" => 632, "mat" => 2),
            array("reg" => 632, "mat" => 4),
            array("reg" => 632, "mat" => 7),
            array("reg" => 632, "mat" => 13),
            array("reg" => 633, "mat" => 1),
            array("reg" => 633, "mat" => 2),
            array("reg" => 633, "mat" => 4),
            array("reg" => 633, "mat" => 7),
            array("reg" => 633, "mat" => 13),
            array("reg" => 634, "mat" => 1),
            array("reg" => 634, "mat" => 2),
            array("reg" => 634, "mat" => 4),
            array("reg" => 634, "mat" => 7),
            array("reg" => 634, "mat" => 13),
            array("reg" => 635, "mat" => 1),
            array("reg" => 635, "mat" => 2),
            array("reg" => 635, "mat" => 7),
            array("reg" => 635, "mat" => 10),
            array("reg" => 636, "mat" => 1),
            array("reg" => 636, "mat" => 2),
            array("reg" => 637, "mat" => 1),
            array("reg" => 637, "mat" => 2),
            array("reg" => 637, "mat" => 4),
            array("reg" => 637, "mat" => 7),
            array("reg" => 637, "mat" => 13),
            array("reg" => 638, "mat" => 3),
            array("reg" => 638, "mat" => 6),
            array("reg" => 638, "mat" => 9),
            array("reg" => 638, "mat" => 10),
            array("reg" => 638, "mat" => 11),
            array("reg" => 638, "mat" => 12),
            array("reg" => 639, "mat" => 3),
            array("reg" => 639, "mat" => 6),
            array("reg" => 639, "mat" => 9),
            array("reg" => 639, "mat" => 10),
            array("reg" => 639, "mat" => 11),
            array("reg" => 639, "mat" => 12),
            array("reg" => 640, "mat" => 3),
            array("reg" => 640, "mat" => 6),
            array("reg" => 640, "mat" => 9),
            array("reg" => 640, "mat" => 10),
            array("reg" => 640, "mat" => 11),
            array("reg" => 640, "mat" => 12),
            array("reg" => 641, "mat" => 3),
            array("reg" => 641, "mat" => 6),
            array("reg" => 641, "mat" => 9),
            array("reg" => 641, "mat" => 10),
            array("reg" => 641, "mat" => 11),
            array("reg" => 641, "mat" => 12),
            array("reg" => 642, "mat" => 3),
            array("reg" => 642, "mat" => 6),
            array("reg" => 642, "mat" => 9),
            array("reg" => 642, "mat" => 10),
            array("reg" => 642, "mat" => 11),
            array("reg" => 642, "mat" => 12),
            array("reg" => 643, "mat" => 3),
            array("reg" => 643, "mat" => 6),
            array("reg" => 643, "mat" => 9),
            array("reg" => 643, "mat" => 10),
            array("reg" => 643, "mat" => 11),
            array("reg" => 643, "mat" => 12),
            array("reg" => 644, "mat" => 3),
            array("reg" => 644, "mat" => 6),
            array("reg" => 644, "mat" => 9),
            array("reg" => 644, "mat" => 10),
            array("reg" => 644, "mat" => 11),
            array("reg" => 644, "mat" => 12),
            array("reg" => 645, "mat" => 1),
            array("reg" => 645, "mat" => 2),
            array("reg" => 645, "mat" => 4),
            array("reg" => 645, "mat" => 7),
            array("reg" => 645, "mat" => 13),
            array("reg" => 646, "mat" => 1),
            array("reg" => 646, "mat" => 2),
            array("reg" => 646, "mat" => 4),
            array("reg" => 646, "mat" => 7),
            array("reg" => 646, "mat" => 13),
            array("reg" => 647, "mat" => 3),
            array("reg" => 647, "mat" => 6),
            array("reg" => 647, "mat" => 9),
            array("reg" => 647, "mat" => 10),
            array("reg" => 647, "mat" => 11),
            array("reg" => 647, "mat" => 12),
            array("reg" => 648, "mat" => 3),
            array("reg" => 648, "mat" => 6),
            array("reg" => 648, "mat" => 9),
            array("reg" => 648, "mat" => 10),
            array("reg" => 648, "mat" => 11),
            array("reg" => 648, "mat" => 12),
            array("reg" => 649, "mat" => 1),
            array("reg" => 649, "mat" => 2),
            array("reg" => 649, "mat" => 4),
            array("reg" => 649, "mat" => 7),
            array("reg" => 649, "mat" => 13),
            array("reg" => 650, "mat" => 1),
            array("reg" => 650, "mat" => 2),
            array("reg" => 650, "mat" => 4),
            array("reg" => 650, "mat" => 7),
            array("reg" => 650, "mat" => 13),
            array("reg" => 651, "mat" => 1),
            array("reg" => 651, "mat" => 2),
            array("reg" => 651, "mat" => 4),
            array("reg" => 651, "mat" => 7),
            array("reg" => 651, "mat" => 13),
            array("reg" => 652, "mat" => 1),
            array("reg" => 652, "mat" => 2),
            array("reg" => 652, "mat" => 4),
            array("reg" => 652, "mat" => 7),
            array("reg" => 652, "mat" => 13),
            array("reg" => 653, "mat" => 2),
            array("reg" => 653, "mat" => 13),
            array("reg" => 654, "mat" => 1),
            array("reg" => 654, "mat" => 2),
            array("reg" => 654, "mat" => 4),
            array("reg" => 654, "mat" => 7),
            array("reg" => 654, "mat" => 13),
            array("reg" => 655, "mat" => 1),
            array("reg" => 655, "mat" => 2),
            array("reg" => 655, "mat" => 4),
            array("reg" => 655, "mat" => 7),
            array("reg" => 655, "mat" => 13),
            array("reg" => 656, "mat" => 1),
            array("reg" => 656, "mat" => 2),
            array("reg" => 656, "mat" => 4),
            array("reg" => 656, "mat" => 7),
            array("reg" => 656, "mat" => 13),
            array("reg" => 657, "mat" => 3),
            array("reg" => 657, "mat" => 6),
            array("reg" => 657, "mat" => 9),
            array("reg" => 657, "mat" => 10),
            array("reg" => 657, "mat" => 11),
            array("reg" => 657, "mat" => 12),
            array("reg" => 658, "mat" => 1),
            array("reg" => 658, "mat" => 2),
            array("reg" => 658, "mat" => 4),
            array("reg" => 658, "mat" => 7),
            array("reg" => 658, "mat" => 13),
            array("reg" => 659, "mat" => 1),
            array("reg" => 659, "mat" => 2),
            array("reg" => 659, "mat" => 4),
            array("reg" => 659, "mat" => 7),
            array("reg" => 659, "mat" => 13),
            array("reg" => 660, "mat" => 3),
            array("reg" => 660, "mat" => 6),
            array("reg" => 660, "mat" => 9),
            array("reg" => 660, "mat" => 10),
            array("reg" => 660, "mat" => 11),
            array("reg" => 660, "mat" => 12),
            array("reg" => 661, "mat" => 3),
            array("reg" => 661, "mat" => 6),
            array("reg" => 661, "mat" => 9),
            array("reg" => 661, "mat" => 10),
            array("reg" => 661, "mat" => 11),
            array("reg" => 661, "mat" => 12),
            array("reg" => 662, "mat" => 1),
            array("reg" => 662, "mat" => 2),
            array("reg" => 662, "mat" => 4),
            array("reg" => 662, "mat" => 7),
            array("reg" => 662, "mat" => 13),
            array("reg" => 663, "mat" => 1),
            array("reg" => 663, "mat" => 2),
            array("reg" => 663, "mat" => 4),
            array("reg" => 663, "mat" => 7),
            array("reg" => 663, "mat" => 13),
            array("reg" => 664, "mat" => 1),
            array("reg" => 664, "mat" => 2),
            array("reg" => 664, "mat" => 4),
            array("reg" => 664, "mat" => 7),
            array("reg" => 664, "mat" => 13),
            array("reg" => 665, "mat" => 1),
            array("reg" => 665, "mat" => 2),
            array("reg" => 665, "mat" => 4),
            array("reg" => 665, "mat" => 7),
            array("reg" => 665, "mat" => 13),
            array("reg" => 666, "mat" => 1),
            array("reg" => 666, "mat" => 2),
            array("reg" => 666, "mat" => 4),
            array("reg" => 666, "mat" => 7),
            array("reg" => 666, "mat" => 13),
            array("reg" => 667, "mat" => 1),
            array("reg" => 667, "mat" => 2),
            array("reg" => 667, "mat" => 4),
            array("reg" => 667, "mat" => 7),
            array("reg" => 667, "mat" => 13),
            array("reg" => 668, "mat" => 1),
            array("reg" => 668, "mat" => 2),
            array("reg" => 668, "mat" => 4),
            array("reg" => 668, "mat" => 7),
            array("reg" => 668, "mat" => 13),
            array("reg" => 669, "mat" => 3),
            array("reg" => 669, "mat" => 6),
            array("reg" => 669, "mat" => 9),
            array("reg" => 669, "mat" => 10),
            array("reg" => 669, "mat" => 11),
            array("reg" => 669, "mat" => 12),
            array("reg" => 670, "mat" => 3),
            array("reg" => 670, "mat" => 6),
            array("reg" => 670, "mat" => 9),
            array("reg" => 670, "mat" => 10),
            array("reg" => 670, "mat" => 11),
            array("reg" => 670, "mat" => 12),
            array("reg" => 671, "mat" => 3),
            array("reg" => 671, "mat" => 6),
            array("reg" => 671, "mat" => 9),
            array("reg" => 671, "mat" => 10),
            array("reg" => 671, "mat" => 11),
            array("reg" => 671, "mat" => 12),
            array("reg" => 672, "mat" => 1),
            array("reg" => 672, "mat" => 2),
            array("reg" => 672, "mat" => 4),
            array("reg" => 672, "mat" => 7),
            array("reg" => 672, "mat" => 13),
            array("reg" => 673, "mat" => 8),
            array("reg" => 674, "mat" => 1),
            array("reg" => 675, "mat" => 1),
            array("reg" => 675, "mat" => 2),
            array("reg" => 675, "mat" => 4),
            array("reg" => 675, "mat" => 7),
            array("reg" => 675, "mat" => 13),
            array("reg" => 676, "mat" => 1),
            array("reg" => 676, "mat" => 2),
            array("reg" => 676, "mat" => 4),
            array("reg" => 676, "mat" => 7),
            array("reg" => 676, "mat" => 13),
            array("reg" => 677, "mat" => 3),
            array("reg" => 677, "mat" => 6),
            array("reg" => 677, "mat" => 9),
            array("reg" => 677, "mat" => 10),
            array("reg" => 677, "mat" => 11),
            array("reg" => 677, "mat" => 12),
            array("reg" => 678, "mat" => 3),
            array("reg" => 678, "mat" => 6),
            array("reg" => 678, "mat" => 9),
            array("reg" => 678, "mat" => 10),
            array("reg" => 678, "mat" => 11),
            array("reg" => 678, "mat" => 12),
            array("reg" => 679, "mat" => 3),
            array("reg" => 679, "mat" => 7),
            array("reg" => 679, "mat" => 9),
            array("reg" => 680, "mat" => 1),
            array("reg" => 680, "mat" => 2),
            array("reg" => 680, "mat" => 4),
            array("reg" => 680, "mat" => 7),
            array("reg" => 680, "mat" => 13),
            array("reg" => 681, "mat" => 1),
            array("reg" => 681, "mat" => 2),
            array("reg" => 681, "mat" => 4),
            array("reg" => 681, "mat" => 7),
            array("reg" => 681, "mat" => 13),
            array("reg" => 682, "mat" => 1),
            array("reg" => 682, "mat" => 2),
            array("reg" => 682, "mat" => 4),
            array("reg" => 682, "mat" => 7),
            array("reg" => 682, "mat" => 13),
            array("reg" => 683, "mat" => 1),
            array("reg" => 683, "mat" => 2),
            array("reg" => 683, "mat" => 4),
            array("reg" => 683, "mat" => 7),
            array("reg" => 683, "mat" => 13),
            array("reg" => 684, "mat" => 3),
            array("reg" => 684, "mat" => 6),
            array("reg" => 684, "mat" => 9),
            array("reg" => 684, "mat" => 10),
            array("reg" => 684, "mat" => 11),
            array("reg" => 684, "mat" => 12),
            array("reg" => 685, "mat" => 3),
            array("reg" => 685, "mat" => 6),
            array("reg" => 685, "mat" => 9),
            array("reg" => 685, "mat" => 10),
            array("reg" => 685, "mat" => 11),
            array("reg" => 685, "mat" => 12),
            array("reg" => 686, "mat" => 1),
            array("reg" => 686, "mat" => 2),
            array("reg" => 686, "mat" => 4),
            array("reg" => 686, "mat" => 7),
            array("reg" => 686, "mat" => 13),
            array("reg" => 687, "mat" => 1),
            array("reg" => 687, "mat" => 2),
            array("reg" => 687, "mat" => 4),
            array("reg" => 687, "mat" => 7),
            array("reg" => 687, "mat" => 13),
            array("reg" => 688, "mat" => 3),
            array("reg" => 688, "mat" => 6),
            array("reg" => 688, "mat" => 9),
            array("reg" => 688, "mat" => 10),
            array("reg" => 688, "mat" => 11),
            array("reg" => 688, "mat" => 12),
            array("reg" => 689, "mat" => 3),
            array("reg" => 689, "mat" => 6),
            array("reg" => 689, "mat" => 9),
            array("reg" => 689, "mat" => 10),
            array("reg" => 689, "mat" => 11),
            array("reg" => 689, "mat" => 12),
            array("reg" => 690, "mat" => 1),
            array("reg" => 690, "mat" => 2),
            array("reg" => 690, "mat" => 4),
            array("reg" => 690, "mat" => 7),
            array("reg" => 690, "mat" => 13),
            array("reg" => 691, "mat" => 3),
            array("reg" => 691, "mat" => 6),
            array("reg" => 691, "mat" => 9),
            array("reg" => 691, "mat" => 10),
            array("reg" => 691, "mat" => 11),
            array("reg" => 691, "mat" => 12),
            array("reg" => 692, "mat" => 1),
            array("reg" => 692, "mat" => 2),
            array("reg" => 692, "mat" => 4),
            array("reg" => 692, "mat" => 7),
            array("reg" => 692, "mat" => 13),
            array("reg" => 693, "mat" => 3),
            array("reg" => 693, "mat" => 6),
            array("reg" => 693, "mat" => 9),
            array("reg" => 693, "mat" => 10),
            array("reg" => 693, "mat" => 11),
            array("reg" => 693, "mat" => 12),
            array("reg" => 694, "mat" => 1),
            array("reg" => 694, "mat" => 2),
            array("reg" => 694, "mat" => 4),
            array("reg" => 694, "mat" => 7),
            array("reg" => 694, "mat" => 13),
            array("reg" => 695, "mat" => 3),
            array("reg" => 695, "mat" => 6),
            array("reg" => 695, "mat" => 9),
            array("reg" => 695, "mat" => 10),
            array("reg" => 695, "mat" => 11),
            array("reg" => 695, "mat" => 12),
            array("reg" => 696, "mat" => 3),
            array("reg" => 696, "mat" => 6),
            array("reg" => 696, "mat" => 9),
            array("reg" => 696, "mat" => 10),
            array("reg" => 696, "mat" => 11),
            array("reg" => 696, "mat" => 12),
            array("reg" => 697, "mat" => 1),
            array("reg" => 697, "mat" => 2),
            array("reg" => 697, "mat" => 4),
            array("reg" => 697, "mat" => 7),
            array("reg" => 697, "mat" => 13),
            array("reg" => 698, "mat" => 1),
            array("reg" => 698, "mat" => 2),
            array("reg" => 698, "mat" => 4),
            array("reg" => 698, "mat" => 7),
            array("reg" => 698, "mat" => 13),
            array("reg" => 699, "mat" => 3),
            array("reg" => 699, "mat" => 6),
            array("reg" => 699, "mat" => 9),
            array("reg" => 699, "mat" => 10),
            array("reg" => 699, "mat" => 11),
            array("reg" => 699, "mat" => 12),
            array("reg" => 700, "mat" => 1),
            array("reg" => 700, "mat" => 2),
            array("reg" => 700, "mat" => 4),
            array("reg" => 700, "mat" => 7),
            array("reg" => 700, "mat" => 13),
            array("reg" => 701, "mat" => 3),
            array("reg" => 701, "mat" => 6),
            array("reg" => 701, "mat" => 9),
            array("reg" => 701, "mat" => 10),
            array("reg" => 701, "mat" => 11),
            array("reg" => 701, "mat" => 12),
            array("reg" => 702, "mat" => 1),
            array("reg" => 702, "mat" => 2),
            array("reg" => 702, "mat" => 4),
            array("reg" => 702, "mat" => 7),
            array("reg" => 702, "mat" => 13),
            array("reg" => 703, "mat" => 1),
            array("reg" => 703, "mat" => 2),
            array("reg" => 703, "mat" => 4),
            array("reg" => 703, "mat" => 7),
            array("reg" => 703, "mat" => 13),
            array("reg" => 704, "mat" => 3),
            array("reg" => 704, "mat" => 6),
            array("reg" => 704, "mat" => 9),
            array("reg" => 704, "mat" => 10),
            array("reg" => 704, "mat" => 11),
            array("reg" => 704, "mat" => 12),
            array("reg" => 705, "mat" => 3),
            array("reg" => 705, "mat" => 6),
            array("reg" => 705, "mat" => 9),
            array("reg" => 705, "mat" => 10),
            array("reg" => 705, "mat" => 11),
            array("reg" => 705, "mat" => 12),
            array("reg" => 706, "mat" => 1),
            array("reg" => 706, "mat" => 2),
            array("reg" => 706, "mat" => 13),
            array("reg" => 707, "mat" => 3),
            array("reg" => 707, "mat" => 9),
            array("reg" => 707, "mat" => 10),
            array("reg" => 707, "mat" => 11),
            array("reg" => 707, "mat" => 12),
            array("reg" => 708, "mat" => 3),
            array("reg" => 709, "mat" => 3),
            array("reg" => 710, "mat" => 1),
            array("reg" => 710, "mat" => 2),
            array("reg" => 710, "mat" => 4),
            array("reg" => 710, "mat" => 7),
            array("reg" => 710, "mat" => 13),
            array("reg" => 711, "mat" => 3),
            array("reg" => 711, "mat" => 6),
            array("reg" => 711, "mat" => 9),
            array("reg" => 711, "mat" => 10),
            array("reg" => 711, "mat" => 11),
            array("reg" => 711, "mat" => 12),
            array("reg" => 712, "mat" => 1),
            array("reg" => 712, "mat" => 2),
            array("reg" => 712, "mat" => 4),
            array("reg" => 712, "mat" => 7),
            array("reg" => 712, "mat" => 13),
            array("reg" => 713, "mat" => 1),
            array("reg" => 713, "mat" => 2),
            array("reg" => 713, "mat" => 4),
            array("reg" => 713, "mat" => 7),
            array("reg" => 713, "mat" => 13),
            array("reg" => 714, "mat" => 1),
            array("reg" => 714, "mat" => 2),
            array("reg" => 714, "mat" => 4),
            array("reg" => 714, "mat" => 7),
            array("reg" => 714, "mat" => 13),
            array("reg" => 715, "mat" => 1),
            array("reg" => 715, "mat" => 2),
            array("reg" => 715, "mat" => 4),
            array("reg" => 715, "mat" => 7),
            array("reg" => 715, "mat" => 13),
            array("reg" => 716, "mat" => 1),
            array("reg" => 716, "mat" => 2),
            array("reg" => 716, "mat" => 4),
            array("reg" => 716, "mat" => 7),
            array("reg" => 716, "mat" => 13),
            array("reg" => 717, "mat" => 1),
            array("reg" => 717, "mat" => 2),
            array("reg" => 717, "mat" => 4),
            array("reg" => 717, "mat" => 7),
            array("reg" => 717, "mat" => 13),
            array("reg" => 718, "mat" => 1),
            array("reg" => 718, "mat" => 2),
            array("reg" => 718, "mat" => 4),
            array("reg" => 718, "mat" => 7),
            array("reg" => 718, "mat" => 13),
            array("reg" => 719, "mat" => 3),
            array("reg" => 719, "mat" => 6),
            array("reg" => 719, "mat" => 9),
            array("reg" => 719, "mat" => 10),
            array("reg" => 719, "mat" => 11),
            array("reg" => 719, "mat" => 12),
            array("reg" => 720, "mat" => 3),
            array("reg" => 720, "mat" => 6),
            array("reg" => 720, "mat" => 9),
            array("reg" => 720, "mat" => 10),
            array("reg" => 720, "mat" => 11),
            array("reg" => 720, "mat" => 12),
            array("reg" => 721, "mat" => 1),
            array("reg" => 721, "mat" => 2),
            array("reg" => 721, "mat" => 4),
            array("reg" => 721, "mat" => 7),
            array("reg" => 721, "mat" => 13),
            array("reg" => 722, "mat" => 3),
            array("reg" => 722, "mat" => 6),
            array("reg" => 722, "mat" => 9),
            array("reg" => 722, "mat" => 10),
            array("reg" => 722, "mat" => 11),
            array("reg" => 722, "mat" => 12),
            array("reg" => 723, "mat" => 3),
            array("reg" => 723, "mat" => 6),
            array("reg" => 723, "mat" => 9),
            array("reg" => 723, "mat" => 10),
            array("reg" => 723, "mat" => 11),
            array("reg" => 723, "mat" => 12),
            array("reg" => 724, "mat" => 1),
            array("reg" => 724, "mat" => 2),
            array("reg" => 724, "mat" => 4),
            array("reg" => 724, "mat" => 7),
            array("reg" => 724, "mat" => 13),
            array("reg" => 725, "mat" => 3),
            array("reg" => 725, "mat" => 6),
            array("reg" => 725, "mat" => 9),
            array("reg" => 725, "mat" => 10),
            array("reg" => 725, "mat" => 11),
            array("reg" => 725, "mat" => 12),
            array("reg" => 726, "mat" => 3),
            array("reg" => 726, "mat" => 6),
            array("reg" => 726, "mat" => 9),
            array("reg" => 726, "mat" => 10),
            array("reg" => 726, "mat" => 11),
            array("reg" => 726, "mat" => 12),
            array("reg" => 727, "mat" => 3),
            array("reg" => 727, "mat" => 6),
            array("reg" => 727, "mat" => 9),
            array("reg" => 727, "mat" => 10),
            array("reg" => 727, "mat" => 11),
            array("reg" => 727, "mat" => 12),
            array("reg" => 728, "mat" => 1),
            array("reg" => 728, "mat" => 2),
            array("reg" => 728, "mat" => 4),
            array("reg" => 728, "mat" => 7),
            array("reg" => 728, "mat" => 13),
            array("reg" => 729, "mat" => 1),
            array("reg" => 729, "mat" => 2),
            array("reg" => 729, "mat" => 4),
            array("reg" => 729, "mat" => 7),
            array("reg" => 729, "mat" => 13),
            array("reg" => 730, "mat" => 3),
            array("reg" => 730, "mat" => 6),
            array("reg" => 730, "mat" => 9),
            array("reg" => 730, "mat" => 10),
            array("reg" => 730, "mat" => 11),
            array("reg" => 730, "mat" => 12),
            array("reg" => 731, "mat" => 3),
            array("reg" => 731, "mat" => 6),
            array("reg" => 731, "mat" => 9),
            array("reg" => 731, "mat" => 10),
            array("reg" => 731, "mat" => 11),
            array("reg" => 731, "mat" => 12),
            array("reg" => 732, "mat" => 3),
            array("reg" => 732, "mat" => 6),
            array("reg" => 732, "mat" => 9),
            array("reg" => 732, "mat" => 10),
            array("reg" => 732, "mat" => 11),
            array("reg" => 732, "mat" => 12),
            array("reg" => 733, "mat" => 1),
            array("reg" => 733, "mat" => 2),
            array("reg" => 733, "mat" => 4),
            array("reg" => 733, "mat" => 7),
            array("reg" => 733, "mat" => 13),
            array("reg" => 734, "mat" => 3),
            array("reg" => 734, "mat" => 6),
            array("reg" => 734, "mat" => 9),
            array("reg" => 734, "mat" => 10),
            array("reg" => 734, "mat" => 11),
            array("reg" => 734, "mat" => 12),
            array("reg" => 735, "mat" => 3),
            array("reg" => 735, "mat" => 6),
            array("reg" => 735, "mat" => 9),
            array("reg" => 735, "mat" => 10),
            array("reg" => 735, "mat" => 11),
            array("reg" => 735, "mat" => 12),
            array("reg" => 736, "mat" => 3),
            array("reg" => 736, "mat" => 6),
            array("reg" => 736, "mat" => 9),
            array("reg" => 736, "mat" => 10),
            array("reg" => 736, "mat" => 11),
            array("reg" => 736, "mat" => 12),
            array("reg" => 737, "mat" => 1),
            array("reg" => 737, "mat" => 2),
            array("reg" => 737, "mat" => 4),
            array("reg" => 737, "mat" => 7),
            array("reg" => 737, "mat" => 13),
            array("reg" => 738, "mat" => 3),
            array("reg" => 738, "mat" => 6),
            array("reg" => 738, "mat" => 9),
            array("reg" => 738, "mat" => 10),
            array("reg" => 738, "mat" => 11),
            array("reg" => 738, "mat" => 12),
            array("reg" => 739, "mat" => 1),
            array("reg" => 739, "mat" => 2),
            array("reg" => 739, "mat" => 4),
            array("reg" => 739, "mat" => 7),
            array("reg" => 739, "mat" => 13),
            array("reg" => 740, "mat" => 1),
            array("reg" => 740, "mat" => 2),
            array("reg" => 740, "mat" => 4),
            array("reg" => 740, "mat" => 7),
            array("reg" => 740, "mat" => 13),
            array("reg" => 741, "mat" => 3),
            array("reg" => 741, "mat" => 6),
            array("reg" => 741, "mat" => 9),
            array("reg" => 741, "mat" => 10),
            array("reg" => 741, "mat" => 11),
            array("reg" => 741, "mat" => 12),
            array("reg" => 742, "mat" => 3),
            array("reg" => 742, "mat" => 6),
            array("reg" => 742, "mat" => 9),
            array("reg" => 742, "mat" => 10),
            array("reg" => 742, "mat" => 11),
            array("reg" => 742, "mat" => 12),
            array("reg" => 743, "mat" => 3),
            array("reg" => 743, "mat" => 6),
            array("reg" => 743, "mat" => 9),
            array("reg" => 743, "mat" => 10),
            array("reg" => 743, "mat" => 11),
            array("reg" => 743, "mat" => 12),
            array("reg" => 744, "mat" => 3),
            array("reg" => 744, "mat" => 6),
            array("reg" => 744, "mat" => 9),
            array("reg" => 744, "mat" => 10),
            array("reg" => 744, "mat" => 11),
            array("reg" => 744, "mat" => 12),
            array("reg" => 745, "mat" => 3),
            array("reg" => 745, "mat" => 6),
            array("reg" => 745, "mat" => 9),
            array("reg" => 745, "mat" => 10),
            array("reg" => 745, "mat" => 11),
            array("reg" => 745, "mat" => 12),
            array("reg" => 746, "mat" => 3),
            array("reg" => 746, "mat" => 6),
            array("reg" => 746, "mat" => 9),
            array("reg" => 746, "mat" => 10),
            array("reg" => 746, "mat" => 11),
            array("reg" => 746, "mat" => 12),
            array("reg" => 747, "mat" => 3),
            array("reg" => 747, "mat" => 6),
            array("reg" => 747, "mat" => 9),
            array("reg" => 747, "mat" => 10),
            array("reg" => 747, "mat" => 11),
            array("reg" => 747, "mat" => 12),
            array("reg" => 748, "mat" => 3),
            array("reg" => 748, "mat" => 6),
            array("reg" => 748, "mat" => 9),
            array("reg" => 748, "mat" => 10),
            array("reg" => 748, "mat" => 11),
            array("reg" => 748, "mat" => 12),
            array("reg" => 749, "mat" => 3),
            array("reg" => 749, "mat" => 7),
            array("reg" => 749, "mat" => 10),
            array("reg" => 750, "mat" => 3),
            array("reg" => 750, "mat" => 7),
            array("reg" => 750, "mat" => 10),
            array("reg" => 751, "mat" => 3),
            array("reg" => 751, "mat" => 7),
            array("reg" => 751, "mat" => 10),
            array("reg" => 752, "mat" => 3),
            array("reg" => 752, "mat" => 9),
            array("reg" => 752, "mat" => 10),
            array("reg" => 752, "mat" => 11),
            array("reg" => 752, "mat" => 12),
            array("reg" => 753, "mat" => 2),
            array("reg" => 753, "mat" => 7),
            array("reg" => 754, "mat" => 4),
            array("reg" => 755, "mat" => 3),
            array("reg" => 755, "mat" => 6),
            array("reg" => 755, "mat" => 9),
            array("reg" => 755, "mat" => 10),
            array("reg" => 755, "mat" => 11),
            array("reg" => 755, "mat" => 12),
            array("reg" => 756, "mat" => 1),
            array("reg" => 756, "mat" => 2),
            array("reg" => 756, "mat" => 4),
            array("reg" => 756, "mat" => 7),
            array("reg" => 756, "mat" => 13),
            array("reg" => 757, "mat" => 1),
            array("reg" => 757, "mat" => 2),
            array("reg" => 757, "mat" => 4),
            array("reg" => 757, "mat" => 7),
            array("reg" => 757, "mat" => 13),
            array("reg" => 758, "mat" => 1),
            array("reg" => 758, "mat" => 2),
            array("reg" => 758, "mat" => 4),
            array("reg" => 758, "mat" => 7),
            array("reg" => 758, "mat" => 13),
            array("reg" => 759, "mat" => 3),
            array("reg" => 759, "mat" => 6),
            array("reg" => 759, "mat" => 9),
            array("reg" => 759, "mat" => 10),
            array("reg" => 759, "mat" => 11),
            array("reg" => 759, "mat" => 12),
            array("reg" => 760, "mat" => 1),
            array("reg" => 760, "mat" => 2),
            array("reg" => 760, "mat" => 4),
            array("reg" => 760, "mat" => 7),
            array("reg" => 760, "mat" => 13),
            array("reg" => 761, "mat" => 1),
            array("reg" => 761, "mat" => 2),
            array("reg" => 761, "mat" => 4),
            array("reg" => 761, "mat" => 7),
            array("reg" => 761, "mat" => 13),
            array("reg" => 762, "mat" => 1),
            array("reg" => 762, "mat" => 2),
            array("reg" => 762, "mat" => 4),
            array("reg" => 762, "mat" => 7),
            array("reg" => 762, "mat" => 13),
            array("reg" => 763, "mat" => 1),
            array("reg" => 763, "mat" => 2),
            array("reg" => 763, "mat" => 4),
            array("reg" => 763, "mat" => 7),
            array("reg" => 763, "mat" => 13),
            array("reg" => 764, "mat" => 4),
            array("reg" => 765, "mat" => 3),
            array("reg" => 765, "mat" => 6),
            array("reg" => 765, "mat" => 7),
            array("reg" => 765, "mat" => 9),
            array("reg" => 765, "mat" => 10),
            array("reg" => 765, "mat" => 11),
            array("reg" => 765, "mat" => 12),
            array("reg" => 766, "mat" => 3),
            array("reg" => 766, "mat" => 6),
            array("reg" => 766, "mat" => 7),
            array("reg" => 766, "mat" => 9),
            array("reg" => 766, "mat" => 10),
            array("reg" => 766, "mat" => 11),
            array("reg" => 766, "mat" => 12),
            array("reg" => 767, "mat" => 1),
            array("reg" => 767, "mat" => 2),
            array("reg" => 767, "mat" => 4),
            array("reg" => 767, "mat" => 7),
            array("reg" => 767, "mat" => 13),
            array("reg" => 768, "mat" => 3),
            array("reg" => 768, "mat" => 6),
            array("reg" => 768, "mat" => 9),
            array("reg" => 768, "mat" => 10),
            array("reg" => 768, "mat" => 11),
            array("reg" => 768, "mat" => 12),
            array("reg" => 769, "mat" => 3),
            array("reg" => 769, "mat" => 6),
            array("reg" => 769, "mat" => 9),
            array("reg" => 769, "mat" => 10),
            array("reg" => 769, "mat" => 11),
            array("reg" => 769, "mat" => 12),
            array("reg" => 770, "mat" => 1),
            array("reg" => 770, "mat" => 2),
            array("reg" => 770, "mat" => 4),
            array("reg" => 770, "mat" => 7),
            array("reg" => 770, "mat" => 13),
            array("reg" => 771, "mat" => 3),
            array("reg" => 771, "mat" => 6),
            array("reg" => 771, "mat" => 9),
            array("reg" => 771, "mat" => 10),
            array("reg" => 771, "mat" => 11),
            array("reg" => 771, "mat" => 12),
            array("reg" => 772, "mat" => 1),
            array("reg" => 772, "mat" => 2),
            array("reg" => 772, "mat" => 4),
            array("reg" => 772, "mat" => 7),
            array("reg" => 772, "mat" => 13),
            array("reg" => 773, "mat" => 3),
            array("reg" => 773, "mat" => 6),
            array("reg" => 773, "mat" => 9),
            array("reg" => 773, "mat" => 10),
            array("reg" => 773, "mat" => 11),
            array("reg" => 773, "mat" => 12),
            array("reg" => 774, "mat" => 7),
            array("reg" => 775, "mat" => 1),
            array("reg" => 775, "mat" => 2),
            array("reg" => 775, "mat" => 4),
            array("reg" => 775, "mat" => 7),
            array("reg" => 775, "mat" => 13),
            array("reg" => 776, "mat" => 3),
            array("reg" => 776, "mat" => 6),
            array("reg" => 776, "mat" => 9),
            array("reg" => 776, "mat" => 10),
            array("reg" => 776, "mat" => 12),
            array("reg" => 777, "mat" => 3),
            array("reg" => 777, "mat" => 6),
            array("reg" => 777, "mat" => 9),
            array("reg" => 777, "mat" => 10),
            array("reg" => 777, "mat" => 11),
            array("reg" => 777, "mat" => 12),
            array("reg" => 778, "mat" => 3),
            array("reg" => 778, "mat" => 6),
            array("reg" => 778, "mat" => 9),
            array("reg" => 778, "mat" => 10),
            array("reg" => 778, "mat" => 11),
            array("reg" => 778, "mat" => 12),
            array("reg" => 779, "mat" => 3),
            array("reg" => 779, "mat" => 6),
            array("reg" => 779, "mat" => 9),
            array("reg" => 779, "mat" => 10),
            array("reg" => 779, "mat" => 11),
            array("reg" => 779, "mat" => 12),
            array("reg" => 780, "mat" => 3),
            array("reg" => 780, "mat" => 6),
            array("reg" => 780, "mat" => 9),
            array("reg" => 780, "mat" => 10),
            array("reg" => 780, "mat" => 11),
            array("reg" => 780, "mat" => 12),
            array("reg" => 781, "mat" => 3),
            array("reg" => 781, "mat" => 6),
            array("reg" => 781, "mat" => 9),
            array("reg" => 781, "mat" => 10),
            array("reg" => 781, "mat" => 11),
            array("reg" => 781, "mat" => 12),
            array("reg" => 782, "mat" => 1),
            array("reg" => 782, "mat" => 2),
            array("reg" => 782, "mat" => 4),
            array("reg" => 782, "mat" => 7),
            array("reg" => 782, "mat" => 13),
            array("reg" => 783, "mat" => 1),
            array("reg" => 783, "mat" => 2),
            array("reg" => 783, "mat" => 4),
            array("reg" => 783, "mat" => 7),
            array("reg" => 783, "mat" => 13),
            array("reg" => 784, "mat" => 1),
            array("reg" => 784, "mat" => 2),
            array("reg" => 784, "mat" => 4),
            array("reg" => 784, "mat" => 7),
            array("reg" => 784, "mat" => 13),
            array("reg" => 785, "mat" => 1),
            array("reg" => 785, "mat" => 2),
            array("reg" => 785, "mat" => 4),
            array("reg" => 785, "mat" => 7),
            array("reg" => 785, "mat" => 13),
            array("reg" => 786, "mat" => 1),
            array("reg" => 786, "mat" => 2),
            array("reg" => 786, "mat" => 4),
            array("reg" => 786, "mat" => 7),
            array("reg" => 786, "mat" => 13),
            array("reg" => 787, "mat" => 1),
            array("reg" => 787, "mat" => 2),
            array("reg" => 787, "mat" => 4),
            array("reg" => 787, "mat" => 7),
            array("reg" => 787, "mat" => 13),
            array("reg" => 788, "mat" => 1),
            array("reg" => 788, "mat" => 2),
            array("reg" => 788, "mat" => 4),
            array("reg" => 788, "mat" => 7),
            array("reg" => 788, "mat" => 13),
            array("reg" => 789, "mat" => 1),
            array("reg" => 789, "mat" => 2),
            array("reg" => 789, "mat" => 4),
            array("reg" => 789, "mat" => 7),
            array("reg" => 789, "mat" => 13),
            array("reg" => 790, "mat" => 3),
            array("reg" => 790, "mat" => 6),
            array("reg" => 790, "mat" => 9),
            array("reg" => 790, "mat" => 10),
            array("reg" => 790, "mat" => 11),
            array("reg" => 790, "mat" => 12),
            array("reg" => 791, "mat" => 1),
            array("reg" => 791, "mat" => 2),
            array("reg" => 791, "mat" => 4),
            array("reg" => 791, "mat" => 7),
            array("reg" => 791, "mat" => 13),
            array("reg" => 792, "mat" => 3),
            array("reg" => 792, "mat" => 6),
            array("reg" => 792, "mat" => 9),
            array("reg" => 792, "mat" => 10),
            array("reg" => 792, "mat" => 11),
            array("reg" => 792, "mat" => 12),
            array("reg" => 793, "mat" => 3),
            array("reg" => 793, "mat" => 6),
            array("reg" => 793, "mat" => 9),
            array("reg" => 793, "mat" => 10),
            array("reg" => 793, "mat" => 11),
            array("reg" => 793, "mat" => 12),
            array("reg" => 794, "mat" => 1),
            array("reg" => 794, "mat" => 2),
            array("reg" => 794, "mat" => 4),
            array("reg" => 794, "mat" => 7),
            array("reg" => 794, "mat" => 13),
            array("reg" => 795, "mat" => 2),
            array("reg" => 795, "mat" => 6),
            array("reg" => 795, "mat" => 7),
            array("reg" => 795, "mat" => 13),
            array("reg" => 796, "mat" => 3),
            array("reg" => 796, "mat" => 6),
            array("reg" => 796, "mat" => 9),
            array("reg" => 796, "mat" => 10),
            array("reg" => 796, "mat" => 11),
            array("reg" => 796, "mat" => 12),
            array("reg" => 797, "mat" => 1),
            array("reg" => 797, "mat" => 2),
            array("reg" => 797, "mat" => 4),
            array("reg" => 797, "mat" => 7),
            array("reg" => 797, "mat" => 13),
            array("reg" => 798, "mat" => 3),
            array("reg" => 798, "mat" => 6),
            array("reg" => 798, "mat" => 9),
            array("reg" => 798, "mat" => 10),
            array("reg" => 798, "mat" => 11),
            array("reg" => 798, "mat" => 12),
            array("reg" => 799, "mat" => 3),
            array("reg" => 799, "mat" => 6),
            array("reg" => 799, "mat" => 9),
            array("reg" => 799, "mat" => 10),
            array("reg" => 799, "mat" => 11),
            array("reg" => 799, "mat" => 12),
            array("reg" => 800, "mat" => 3),
            array("reg" => 800, "mat" => 6),
            array("reg" => 800, "mat" => 9),
            array("reg" => 800, "mat" => 10),
            array("reg" => 800, "mat" => 11),
            array("reg" => 800, "mat" => 12),
            array("reg" => 801, "mat" => 3),
            array("reg" => 801, "mat" => 6),
            array("reg" => 801, "mat" => 9),
            array("reg" => 801, "mat" => 10),
            array("reg" => 801, "mat" => 11),
            array("reg" => 801, "mat" => 12),
            array("reg" => 802, "mat" => 3),
            array("reg" => 802, "mat" => 6),
            array("reg" => 802, "mat" => 9),
            array("reg" => 802, "mat" => 10),
            array("reg" => 802, "mat" => 11),
            array("reg" => 802, "mat" => 12),
            array("reg" => 803, "mat" => 3),
            array("reg" => 803, "mat" => 6),
            array("reg" => 803, "mat" => 9),
            array("reg" => 803, "mat" => 10),
            array("reg" => 803, "mat" => 11),
            array("reg" => 803, "mat" => 12),
            array("reg" => 804, "mat" => 3),
            array("reg" => 804, "mat" => 6),
            array("reg" => 804, "mat" => 9),
            array("reg" => 804, "mat" => 10),
            array("reg" => 804, "mat" => 11),
            array("reg" => 804, "mat" => 12),
            array("reg" => 805, "mat" => 1),
            array("reg" => 805, "mat" => 2),
            array("reg" => 805, "mat" => 4),
            array("reg" => 805, "mat" => 7),
            array("reg" => 805, "mat" => 13),
            array("reg" => 806, "mat" => 3),
            array("reg" => 806, "mat" => 6),
            array("reg" => 806, "mat" => 9),
            array("reg" => 806, "mat" => 10),
            array("reg" => 806, "mat" => 11),
            array("reg" => 806, "mat" => 12),
            array("reg" => 807, "mat" => 1),
            array("reg" => 807, "mat" => 2),
            array("reg" => 807, "mat" => 4),
            array("reg" => 807, "mat" => 7),
            array("reg" => 807, "mat" => 13),
            array("reg" => 808, "mat" => 3),
            array("reg" => 808, "mat" => 6),
            array("reg" => 808, "mat" => 9),
            array("reg" => 808, "mat" => 10),
            array("reg" => 808, "mat" => 11),
            array("reg" => 808, "mat" => 12),
            array("reg" => 809, "mat" => 3),
            array("reg" => 809, "mat" => 6),
            array("reg" => 809, "mat" => 9),
            array("reg" => 809, "mat" => 10),
            array("reg" => 809, "mat" => 11),
            array("reg" => 809, "mat" => 12),
            array("reg" => 810, "mat" => 1),
            array("reg" => 810, "mat" => 2),
            array("reg" => 810, "mat" => 13),
            array("reg" => 811, "mat" => 4),
            array("reg" => 812, "mat" => 4),
            array("reg" => 813, "mat" => 1),
            array("reg" => 813, "mat" => 2),
            array("reg" => 813, "mat" => 4),
            array("reg" => 813, "mat" => 7),
            array("reg" => 813, "mat" => 13),
            array("reg" => 814, "mat" => 1),
            array("reg" => 814, "mat" => 2),
            array("reg" => 814, "mat" => 4),
            array("reg" => 814, "mat" => 7),
            array("reg" => 814, "mat" => 13),
            array("reg" => 815, "mat" => 3),
            array("reg" => 815, "mat" => 6),
            array("reg" => 815, "mat" => 9),
            array("reg" => 815, "mat" => 10),
            array("reg" => 815, "mat" => 11),
            array("reg" => 815, "mat" => 12),
            array("reg" => 816, "mat" => 3),
            array("reg" => 816, "mat" => 6),
            array("reg" => 816, "mat" => 9),
            array("reg" => 816, "mat" => 10),
            array("reg" => 816, "mat" => 11),
            array("reg" => 816, "mat" => 12),
            array("reg" => 817, "mat" => 1),
            array("reg" => 817, "mat" => 2),
            array("reg" => 817, "mat" => 4),
            array("reg" => 817, "mat" => 7),
            array("reg" => 817, "mat" => 13),
            array("reg" => 818, "mat" => 3),
            array("reg" => 818, "mat" => 6),
            array("reg" => 818, "mat" => 9),
            array("reg" => 818, "mat" => 10),
            array("reg" => 818, "mat" => 11),
            array("reg" => 818, "mat" => 12),
            array("reg" => 819, "mat" => 3),
            array("reg" => 819, "mat" => 6),
            array("reg" => 819, "mat" => 9),
            array("reg" => 819, "mat" => 10),
            array("reg" => 819, "mat" => 11),
            array("reg" => 819, "mat" => 12),
            array("reg" => 820, "mat" => 1),
            array("reg" => 820, "mat" => 2),
            array("reg" => 820, "mat" => 4),
            array("reg" => 820, "mat" => 7),
            array("reg" => 820, "mat" => 13),
            array("reg" => 821, "mat" => 1),
            array("reg" => 821, "mat" => 2),
            array("reg" => 821, "mat" => 4),
            array("reg" => 821, "mat" => 7),
            array("reg" => 821, "mat" => 13),
            array("reg" => 822, "mat" => 3),
            array("reg" => 822, "mat" => 6),
            array("reg" => 822, "mat" => 9),
            array("reg" => 822, "mat" => 10),
            array("reg" => 822, "mat" => 11),
            array("reg" => 822, "mat" => 12),
            array("reg" => 823, "mat" => 3),
            array("reg" => 823, "mat" => 9),
            array("reg" => 823, "mat" => 10),
            array("reg" => 823, "mat" => 11),
            array("reg" => 823, "mat" => 12),
            array("reg" => 824, "mat" => 4),
            array("reg" => 825, "mat" => 3),
            array("reg" => 825, "mat" => 6),
            array("reg" => 825, "mat" => 9),
            array("reg" => 825, "mat" => 10),
            array("reg" => 825, "mat" => 11),
            array("reg" => 825, "mat" => 12),
            array("reg" => 826, "mat" => 3),
            array("reg" => 826, "mat" => 6),
            array("reg" => 826, "mat" => 9),
            array("reg" => 826, "mat" => 10),
            array("reg" => 826, "mat" => 11),
            array("reg" => 826, "mat" => 12),
            array("reg" => 827, "mat" => 3),
            array("reg" => 827, "mat" => 6),
            array("reg" => 827, "mat" => 9),
            array("reg" => 827, "mat" => 10),
            array("reg" => 827, "mat" => 11),
            array("reg" => 827, "mat" => 12),
            array("reg" => 828, "mat" => 1),
            array("reg" => 828, "mat" => 2),
            array("reg" => 828, "mat" => 4),
            array("reg" => 828, "mat" => 7),
            array("reg" => 828, "mat" => 13),
            array("reg" => 829, "mat" => 1),
            array("reg" => 829, "mat" => 2),
            array("reg" => 829, "mat" => 4),
            array("reg" => 829, "mat" => 7),
            array("reg" => 829, "mat" => 13),
            array("reg" => 830, "mat" => 3),
            array("reg" => 830, "mat" => 6),
            array("reg" => 830, "mat" => 9),
            array("reg" => 830, "mat" => 10),
            array("reg" => 830, "mat" => 11),
            array("reg" => 830, "mat" => 12),
            array("reg" => 831, "mat" => 3),
            array("reg" => 831, "mat" => 6),
            array("reg" => 831, "mat" => 9),
            array("reg" => 831, "mat" => 10),
            array("reg" => 831, "mat" => 11),
            array("reg" => 831, "mat" => 12),
            array("reg" => 832, "mat" => 1),
            array("reg" => 832, "mat" => 2),
            array("reg" => 832, "mat" => 4),
            array("reg" => 832, "mat" => 7),
            array("reg" => 832, "mat" => 13),
            array("reg" => 833, "mat" => 1),
            array("reg" => 833, "mat" => 2),
            array("reg" => 833, "mat" => 4),
            array("reg" => 833, "mat" => 7),
            array("reg" => 833, "mat" => 13),
            array("reg" => 834, "mat" => 1),
            array("reg" => 834, "mat" => 2),
            array("reg" => 834, "mat" => 4),
            array("reg" => 834, "mat" => 7),
            array("reg" => 834, "mat" => 13),
            array("reg" => 835, "mat" => 3),
            array("reg" => 835, "mat" => 6),
            array("reg" => 835, "mat" => 9),
            array("reg" => 835, "mat" => 10),
            array("reg" => 835, "mat" => 11),
            array("reg" => 835, "mat" => 12),
            array("reg" => 836, "mat" => 3),
            array("reg" => 836, "mat" => 6),
            array("reg" => 836, "mat" => 9),
            array("reg" => 836, "mat" => 10),
            array("reg" => 836, "mat" => 11),
            array("reg" => 836, "mat" => 12),
            array("reg" => 837, "mat" => 3),
            array("reg" => 837, "mat" => 6),
            array("reg" => 837, "mat" => 9),
            array("reg" => 837, "mat" => 10),
            array("reg" => 837, "mat" => 11),
            array("reg" => 837, "mat" => 12),
            array("reg" => 838, "mat" => 3),
            array("reg" => 838, "mat" => 6),
            array("reg" => 838, "mat" => 9),
            array("reg" => 838, "mat" => 10),
            array("reg" => 838, "mat" => 11),
            array("reg" => 838, "mat" => 12),
            array("reg" => 839, "mat" => 3),
            array("reg" => 839, "mat" => 6),
            array("reg" => 839, "mat" => 9),
            array("reg" => 839, "mat" => 10),
            array("reg" => 839, "mat" => 11),
            array("reg" => 839, "mat" => 12),
            array("reg" => 840, "mat" => 1),
            array("reg" => 840, "mat" => 2),
            array("reg" => 840, "mat" => 4),
            array("reg" => 840, "mat" => 7),
            array("reg" => 840, "mat" => 13),
            array("reg" => 841, "mat" => 3),
            array("reg" => 841, "mat" => 6),
            array("reg" => 841, "mat" => 9),
            array("reg" => 841, "mat" => 10),
            array("reg" => 841, "mat" => 11),
            array("reg" => 841, "mat" => 12),
            array("reg" => 842, "mat" => 1),
            array("reg" => 842, "mat" => 2),
            array("reg" => 842, "mat" => 4),
            array("reg" => 842, "mat" => 7),
            array("reg" => 842, "mat" => 13),
            array("reg" => 843, "mat" => 1),
            array("reg" => 843, "mat" => 2),
            array("reg" => 843, "mat" => 4),
            array("reg" => 843, "mat" => 7),
            array("reg" => 843, "mat" => 13),
            array("reg" => 844, "mat" => 1),
            array("reg" => 844, "mat" => 2),
            array("reg" => 844, "mat" => 4),
            array("reg" => 844, "mat" => 7),
            array("reg" => 844, "mat" => 13),
            array("reg" => 845, "mat" => 1),
            array("reg" => 845, "mat" => 2),
            array("reg" => 845, "mat" => 4),
            array("reg" => 845, "mat" => 7),
            array("reg" => 845, "mat" => 13),
            array("reg" => 846, "mat" => 3),
            array("reg" => 846, "mat" => 6),
            array("reg" => 846, "mat" => 9),
            array("reg" => 846, "mat" => 10),
            array("reg" => 846, "mat" => 11),
            array("reg" => 846, "mat" => 12),
            array("reg" => 847, "mat" => 3),
            array("reg" => 847, "mat" => 6),
            array("reg" => 847, "mat" => 9),
            array("reg" => 847, "mat" => 10),
            array("reg" => 847, "mat" => 11),
            array("reg" => 847, "mat" => 12),
            array("reg" => 848, "mat" => 3),
            array("reg" => 848, "mat" => 6),
            array("reg" => 848, "mat" => 9),
            array("reg" => 848, "mat" => 10),
            array("reg" => 848, "mat" => 11),
            array("reg" => 848, "mat" => 12),
            array("reg" => 849, "mat" => 3),
            array("reg" => 849, "mat" => 6),
            array("reg" => 849, "mat" => 9),
            array("reg" => 849, "mat" => 10),
            array("reg" => 849, "mat" => 11),
            array("reg" => 849, "mat" => 12),
            array("reg" => 850, "mat" => 3),
            array("reg" => 850, "mat" => 6),
            array("reg" => 850, "mat" => 9),
            array("reg" => 850, "mat" => 10),
            array("reg" => 850, "mat" => 11),
            array("reg" => 850, "mat" => 12),
            array("reg" => 851, "mat" => 4),
            array("reg" => 852, "mat" => 3),
            array("reg" => 852, "mat" => 6),
            array("reg" => 852, "mat" => 9),
            array("reg" => 852, "mat" => 10),
            array("reg" => 852, "mat" => 11),
            array("reg" => 852, "mat" => 12),
            array("reg" => 853, "mat" => 1),
            array("reg" => 853, "mat" => 2),
            array("reg" => 853, "mat" => 4),
            array("reg" => 853, "mat" => 7),
            array("reg" => 853, "mat" => 13),
            array("reg" => 854, "mat" => 3),
            array("reg" => 854, "mat" => 7),
            array("reg" => 854, "mat" => 9),
            array("reg" => 854, "mat" => 10),
            array("reg" => 854, "mat" => 11),
            array("reg" => 854, "mat" => 12),
            array("reg" => 855, "mat" => 4),
            array("reg" => 856, "mat" => 3),
            array("reg" => 856, "mat" => 6),
            array("reg" => 856, "mat" => 7),
            array("reg" => 856, "mat" => 9),
            array("reg" => 914, "mat" => 9),
            array("reg" => 856, "mat" => 10),
            array("reg" => 914, "mat" => 10),
            array("reg" => 856, "mat" => 11),
            array("reg" => 914, "mat" => 11),
            array("reg" => 856, "mat" => 12),
            array("reg" => 914, "mat" => 12),
            array("reg" => 857, "mat" => 1),
            array("reg" => 857, "mat" => 2),
            array("reg" => 857, "mat" => 4),
            array("reg" => 857, "mat" => 7),
            array("reg" => 857, "mat" => 13),
            array("reg" => 858, "mat" => 1),
            array("reg" => 858, "mat" => 2),
            array("reg" => 858, "mat" => 4),
            array("reg" => 858, "mat" => 7),
            array("reg" => 858, "mat" => 13),
            array("reg" => 859, "mat" => 3),
            array("reg" => 859, "mat" => 6),
            array("reg" => 859, "mat" => 9),
            array("reg" => 859, "mat" => 10),
            array("reg" => 859, "mat" => 11),
            array("reg" => 859, "mat" => 12),
            array("reg" => 860, "mat" => 1),
            array("reg" => 860, "mat" => 2),
            array("reg" => 860, "mat" => 4),
            array("reg" => 860, "mat" => 7),
            array("reg" => 860, "mat" => 13),
            array("reg" => 861, "mat" => 4),
            array("reg" => 862, "mat" => 3),
            array("reg" => 862, "mat" => 9),
            array("reg" => 862, "mat" => 10),
            array("reg" => 862, "mat" => 12),
            array("reg" => 863, "mat" => 1),
            array("reg" => 863, "mat" => 2),
            array("reg" => 863, "mat" => 4),
            array("reg" => 863, "mat" => 7),
            array("reg" => 863, "mat" => 13),
            array("reg" => 864, "mat" => 3),
            array("reg" => 864, "mat" => 6),
            array("reg" => 864, "mat" => 9),
            array("reg" => 864, "mat" => 10),
            array("reg" => 864, "mat" => 11),
            array("reg" => 864, "mat" => 12),
            array("reg" => 865, "mat" => 3),
            array("reg" => 865, "mat" => 6),
            array("reg" => 865, "mat" => 9),
            array("reg" => 865, "mat" => 10),
            array("reg" => 865, "mat" => 11),
            array("reg" => 865, "mat" => 12),
            array("reg" => 866, "mat" => 3),
            array("reg" => 866, "mat" => 6),
            array("reg" => 866, "mat" => 9),
            array("reg" => 866, "mat" => 10),
            array("reg" => 866, "mat" => 11),
            array("reg" => 866, "mat" => 12),
            array("reg" => 867, "mat" => 1),
            array("reg" => 867, "mat" => 2),
            array("reg" => 867, "mat" => 4),
            array("reg" => 867, "mat" => 7),
            array("reg" => 867, "mat" => 13),
            array("reg" => 868, "mat" => 1),
            array("reg" => 868, "mat" => 2),
            array("reg" => 868, "mat" => 4),
            array("reg" => 868, "mat" => 7),
            array("reg" => 868, "mat" => 13),
            array("reg" => 869, "mat" => 1),
            array("reg" => 869, "mat" => 2),
            array("reg" => 869, "mat" => 4),
            array("reg" => 869, "mat" => 7),
            array("reg" => 869, "mat" => 13),
            array("reg" => 870, "mat" => 1),
            array("reg" => 870, "mat" => 2),
            array("reg" => 870, "mat" => 4),
            array("reg" => 870, "mat" => 7),
            array("reg" => 870, "mat" => 13),
            array("reg" => 871, "mat" => 2),
            array("reg" => 871, "mat" => 7),
            array("reg" => 871, "mat" => 10),
            array("reg" => 871, "mat" => 12),
            array("reg" => 871, "mat" => 13),
            array("reg" => 872, "mat" => 1),
            array("reg" => 872, "mat" => 2),
            array("reg" => 872, "mat" => 7),
            array("reg" => 872, "mat" => 13),
            array("reg" => 873, "mat" => 1),
            array("reg" => 873, "mat" => 2),
            array("reg" => 873, "mat" => 4),
            array("reg" => 873, "mat" => 7),
            array("reg" => 873, "mat" => 13),
            array("reg" => 874, "mat" => 1),
            array("reg" => 874, "mat" => 2),
            array("reg" => 874, "mat" => 4),
            array("reg" => 874, "mat" => 7),
            array("reg" => 874, "mat" => 13),
            array("reg" => 875, "mat" => 3),
            array("reg" => 875, "mat" => 6),
            array("reg" => 875, "mat" => 9),
            array("reg" => 875, "mat" => 10),
            array("reg" => 875, "mat" => 11),
            array("reg" => 875, "mat" => 12),
            array("reg" => 876, "mat" => 3),
            array("reg" => 876, "mat" => 6),
            array("reg" => 876, "mat" => 9),
            array("reg" => 876, "mat" => 10),
            array("reg" => 876, "mat" => 11),
            array("reg" => 876, "mat" => 12),
            array("reg" => 877, "mat" => 1),
            array("reg" => 877, "mat" => 2),
            array("reg" => 877, "mat" => 4),
            array("reg" => 877, "mat" => 7),
            array("reg" => 877, "mat" => 13),
            array("reg" => 878, "mat" => 1),
            array("reg" => 878, "mat" => 2),
            array("reg" => 878, "mat" => 4),
            array("reg" => 878, "mat" => 7),
            array("reg" => 878, "mat" => 13),
            array("reg" => 879, "mat" => 3),
            array("reg" => 879, "mat" => 6),
            array("reg" => 879, "mat" => 9),
            array("reg" => 879, "mat" => 10),
            array("reg" => 879, "mat" => 11),
            array("reg" => 879, "mat" => 12),
            array("reg" => 880, "mat" => 3),
            array("reg" => 880, "mat" => 6),
            array("reg" => 880, "mat" => 9),
            array("reg" => 880, "mat" => 10),
            array("reg" => 880, "mat" => 11),
            array("reg" => 880, "mat" => 12),
            array("reg" => 881, "mat" => 3),
            array("reg" => 881, "mat" => 6),
            array("reg" => 881, "mat" => 9),
            array("reg" => 881, "mat" => 10),
            array("reg" => 881, "mat" => 11),
            array("reg" => 881, "mat" => 12),
            array("reg" => 882, "mat" => 3),
            array("reg" => 882, "mat" => 6),
            array("reg" => 882, "mat" => 9),
            array("reg" => 882, "mat" => 10),
            array("reg" => 882, "mat" => 11),
            array("reg" => 882, "mat" => 12),
            array("reg" => 883, "mat" => 1),
            array("reg" => 883, "mat" => 2),
            array("reg" => 883, "mat" => 4),
            array("reg" => 883, "mat" => 7),
            array("reg" => 883, "mat" => 13),
            array("reg" => 884, "mat" => 1),
            array("reg" => 884, "mat" => 2),
            array("reg" => 884, "mat" => 4),
            array("reg" => 884, "mat" => 7),
            array("reg" => 884, "mat" => 13),
            array("reg" => 885, "mat" => 3),
            array("reg" => 885, "mat" => 6),
            array("reg" => 885, "mat" => 9),
            array("reg" => 885, "mat" => 10),
            array("reg" => 885, "mat" => 11),
            array("reg" => 885, "mat" => 12),
            array("reg" => 886, "mat" => 1),
            array("reg" => 886, "mat" => 2),
            array("reg" => 886, "mat" => 4),
            array("reg" => 886, "mat" => 7),
            array("reg" => 886, "mat" => 13),
            array("reg" => 887, "mat" => 3),
            array("reg" => 887, "mat" => 6),
            array("reg" => 887, "mat" => 9),
            array("reg" => 887, "mat" => 10),
            array("reg" => 887, "mat" => 11),
            array("reg" => 887, "mat" => 12),
            array("reg" => 888, "mat" => 1),
            array("reg" => 888, "mat" => 2),
            array("reg" => 888, "mat" => 4),
            array("reg" => 888, "mat" => 7),
            array("reg" => 888, "mat" => 13),
            array("reg" => 889, "mat" => 1),
            array("reg" => 889, "mat" => 2),
            array("reg" => 889, "mat" => 4),
            array("reg" => 889, "mat" => 7),
            array("reg" => 889, "mat" => 13),
            array("reg" => 890, "mat" => 3),
            array("reg" => 890, "mat" => 6),
            array("reg" => 890, "mat" => 9),
            array("reg" => 890, "mat" => 10),
            array("reg" => 890, "mat" => 11),
            array("reg" => 890, "mat" => 12),
            array("reg" => 891, "mat" => 3),
            array("reg" => 891, "mat" => 6),
            array("reg" => 891, "mat" => 9),
            array("reg" => 891, "mat" => 10),
            array("reg" => 891, "mat" => 11),
            array("reg" => 891, "mat" => 12),
            array("reg" => 892, "mat" => 1),
            array("reg" => 892, "mat" => 2),
            array("reg" => 892, "mat" => 13),
            array("reg" => 893, "mat" => 8),
            array("reg" => 894, "mat" => 1),
            array("reg" => 895, "mat" => 3),
            array("reg" => 896, "mat" => 1),
            array("reg" => 896, "mat" => 4),
            array("reg" => 897, "mat" => 1),
            array("reg" => 898, "mat" => 1),
            array("reg" => 899, "mat" => 1),
            array("reg" => 900, "mat" => 1),
            array("reg" => 901, "mat" => 1),
            array("reg" => 902, "mat" => 1),
            array("reg" => 903, "mat" => 1),
            array("reg" => 903, "mat" => 2),
            array("reg" => 904, "mat" => 1),
            array("reg" => 905, "mat" => 1),
            array("reg" => 906, "mat" => 1),
            array("reg" => 907, "mat" => 1),
            array("reg" => 908, "mat" => 1),
            array("reg" => 909, "mat" => 4),
            array("reg" => 910, "mat" => 4),
            array("reg" => 910, "mat" => 13),
            array("reg" => 911, "mat" => 3),
            array("reg" => 911, "mat" => 6),
            array("reg" => 911, "mat" => 9),
            array("reg" => 911, "mat" => 10),
            array("reg" => 911, "mat" => 11),
            array("reg" => 911, "mat" => 12),
            array("reg" => 912, "mat" => 1),
            array("reg" => 912, "mat" => 2),
            array("reg" => 912, "mat" => 4),
            array("reg" => 912, "mat" => 7),
            array("reg" => 912, "mat" => 13),
            array("reg" => 913, "mat" => 3),
            array("reg" => 913, "mat" => 6),
            array("reg" => 913, "mat" => 9),
            array("reg" => 913, "mat" => 10),
            array("reg" => 913, "mat" => 11),
            array("reg" => 913, "mat" => 12),
            array("reg" => 914, "mat" => 1),
            array("reg" => 915, "mat" => 6),
            array("reg" => 915, "mat" => 10),
            array("reg" => 916, "mat" => 1),
            array("reg" => 917, "mat" => 1),
            array("reg" => 917, "mat" => 2),
            array("reg" => 918, "mat" => 1),
            array("reg" => 919, "mat" => 13),
            array("reg" => 920, "mat" => 1),
            array("reg" => 920, "mat" => 7),
            array("reg" => 921, "mat" => 3),
            array("reg" => 922, "mat" => 3),
            array("reg" => 923, "mat" => 1),
            array("reg" => 923, "mat" => 4),
            array("reg" => 924, "mat" => 1),
            array("reg" => 925, "mat" => 4),
            array("reg" => 926, "mat" => 3),
            array("reg" => 927, "mat" => 1),
            array("reg" => 928, "mat" => 1),
            array("reg" => 929, "mat" => 10),
            array("reg" => 930, "mat" => 1),
            array("reg" => 931, "mat" => 6),
            array("reg" => 932, "mat" => 1),
            array("reg" => 933, "mat" => 8),
            array("reg" => 934, "mat" => 7),
            array("reg" => 934, "mat" => 8),
            array("reg" => 935, "mat" => 8),
            array("reg" => 936, "mat" => 3),
            array("reg" => 937, "mat" => 10),
            array("reg" => 938, "mat" => 3),
            array("reg" => 938, "mat" => 8),
            array("reg" => 939, "mat" => 3),
            array("reg" => 939, "mat" => 9),
            array("reg" => 940, "mat" => 7),
            array("reg" => 940, "mat" => 13),
            array("reg" => 941, "mat" => 1)
        );

        foreach($materials as $material) {
            $conservator = Conservator::where('registration_number', $material['reg'])->first();
            $material = Material::find($material['mat']);

            if ($conservator && $material)
                $conservator->materials()->attach($material);
        }

    }
}