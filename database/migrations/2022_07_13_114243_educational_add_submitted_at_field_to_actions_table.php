<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class EducationalAddSubmittedAtFieldToActionsTable extends Migration
{
    public function up()
    {
        Schema::connection('mysql_educational')->table('actions', function (Blueprint $table) {
            $table->timestamp('submitted_at')->after('link')->nullable()->default(null);
        });
    }
    
    public function down()
    {
        Schema::connection('mysql_educational')->table('actions', function (Blueprint $table) {
            $table->dropColumn('submitted_at');
        });
    }
}
