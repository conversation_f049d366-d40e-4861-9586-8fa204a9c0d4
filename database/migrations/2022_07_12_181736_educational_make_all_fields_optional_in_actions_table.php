<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class EducationalMakeAllFieldsOptionalInActionsTable extends Migration
{
    public function up()
    {
        Schema::connection('mysql_educational')->table('actions', function (Blueprint $table) {
            $table->unsignedBigInteger('period_id')->nullable()->change();
            $table->unsignedBigInteger('unit_id')->nullable()->change();
            $table->unsignedInteger('region_id')->nullable()->change();
            $table->boolean('ongoing')->nullable()->change();
            $table->string('title')->nullable()->change();
            $table->string('description')->nullable()->change();
        });
    }


    public function down()
    {
        Schema::connection('mysql_educational')->disableForeignKeyConstraints();
        Schema::connection('mysql_educational')->table('actions', function (Blueprint $table) {
            $table->unsignedBigInteger('period_id')->nullable(false)->change();
            $table->unsignedBigInteger('unit_id')->nullable(false)->change();
            $table->unsignedInteger('region_id')->nullable(false)->change();
            $table->boolean('ongoing')->nullable(false)->change();
            $table->string('title')->nullable(false)->change();
            $table->string('description')->nullable(false)->change();
        });
        Schema::connection('mysql_educational')->enableForeignKeyConstraints();
    }
}
