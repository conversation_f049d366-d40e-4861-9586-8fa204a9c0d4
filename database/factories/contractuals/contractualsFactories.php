<?php

use App\Models\Contractuals\Applicant;
use App\Models\Contractuals\Application;
use App\Models\Contractuals\Contest;
use App\Models\Contractuals\ContestType;
use App\Models\Contractuals\Degree;
use App\Models\Contractuals\Evaluation;
use App\Models\Contractuals\Experience;
use App\Models\Contractuals\Language;
use App\Models\Contractuals\LanguageLevel;
use App\Models\Contractuals\Minor;
use App\Models\Contractuals\Position;
use App\Models\Contractuals\Postgraduate;
use App\Models\Contractuals\Requirement;
use App\Models\Contractuals\RequirementType;
use App\Models\Contractuals\Specialization;
use App\Models\Contractuals\SpecializationType;
use App\Models\Unit;
use Carbon\Carbon;

/*
|--------------------------------------------------------------------------
| Contest related models
|--------------------------------------------------------------------------
|
| Includes Contest, Position, Specialization, Requirement
|
*/
$factory->define(ContestType::class, function (Faker\Generator $faker) {
    return [
        'id' => $faker->unique()->randomDigitNotNull,
        'name' => 'SOX',
        'description' => 'Διαγωνισμός για φύλακες',
    ];
});

$factory->define(Contest::class, function (Faker\Generator $faker) {
    return [
        'name' => 'Example contest',
        'type_id' => function () {
            return factory(ContestType::class)->create()->id;
        },
        'protocol_number' => 'YPPOA/GDDYHD/DHD/TPS/000001/00001/001/001',
        'protocol_date' => Carbon::now()->subWeek(1),
        'ada' => 'ADA-KWDIKOS',
        'start_date' => Carbon::now()->addWeek(1),
        'end_date' => Carbon::now()->addMonth(1),
        'locked_at' => null,
        'rated_at' => null,
        'ranked_at' => null,
    ];
});

$factory->define(Position::class, function (Faker\Generator $faker) {
    return [
        'amount' => 3,
        'location' => 'Athens',
        'code' => $faker->postcode,
        'specialization_id' => function () {
            return factory(Specialization::class)->create()->id;
        },
        'contest_id' => function () {
            return factory(Contest::class)->create()->id;
        },
        'unit_id' => function () {
            return factory(Unit::class)->create()->id;
        },
    ];
});

$factory->define(Specialization::class, function (Faker\Generator $faker) {
    return [
        'name' => $faker->unique()->jobTitle,
        // FIXME integrity constraint violation: 1062 Duplicate entry
        'shortname'              => 'PE ARX',
        'specialization_type_id' => function () {
            return factory(SpecializationType::class)->create()->id;
        },
    ];
});

$factory->define(SpecializationType::class, function (Faker\Generator $faker) {
    return [
        'name' => $faker->unique()->word,
        // FIXME integrity constraint violation: 1062 Duplicate entry 'ΠΕ' for key 'specialization_types_name_unique'
    ];
});

$factory->define(Requirement::class, function (Faker\Generator $faker) {
    return [
        'name' => 'Βεβαίωση σπουδών ή αναλυτική βαθμολογία',
        'requirement_type_id' => function () {
            return factory(RequirementType::class)->create()->id;
        },
    ];
});

$factory->define(RequirementType::class, function (Faker\Generator $faker) {
    return [
        'name' => 'Τίτλος Σπουδών',
        'qualifiable_type' => 'degree',
    ];
});

/*
|--------------------------------------------------------------------------
| Applicant related models
|--------------------------------------------------------------------------
|
| Includes Applicant, various Assets
|
*/
$factory->define(Applicant::class, function (Faker\Generator $faker) {
    return [
        'name' => 'John',
        'surname' => 'Doe',
        'fathername' => 'Jim',
        'mothername' => 'Jane',
        'birthdate' => Carbon::now()->subYear(30),
        'eu_citizen' => true,
        'greek_nationality' => true,
        'policeid_number' => 'ΧΧ123456',
        'policeid_date' => Carbon::now()->subYear(12),
        'afm' => $faker->unique()->randomNumber(9),
        'doy' => 'AA Athens',
        'amka' => $faker->unique()->randomNumber(9),
        'street' => 'Example str',
        'street_number' => '22',
        'postcode' => '10682',
        'city' => 'Athens',
        'phonenumber1' => '**********',
        'phonenumber2' => '**********',
        'email' => '<EMAIL>',
    ];
});

$factory->define(Degree::class, function (Faker\Generator $faker) {
    return [
        'name' => 'BSc in Archaeology',
        'issuer' => 'University of Athens',
        'mark' => '8.2',
        'year' => Carbon::now()->subYear(5)->year,
        'verified' => true,
        'applicant_id' => function () {
            return factory(Applicant::class)->create()->id;
        },
    ];
});

$factory->define(Postgraduate::class, function (Faker\Generator $faker) {
    return [
        'name' => 'MSc in Byzantine Studies',
        'issuer' => 'University of Crete',
        'year' => Carbon::now()->subYear(3)->year,
        'verified' => true,
        'applicant_id' => function () {
            return factory(Applicant::class)->create()->id;
        },
    ];
});

$factory->define(Experience::class, function (Faker\Generator $faker) {
    return [
        'name' => 'Archaeologist',
        'issuer' => 'Company A',
        'started_at' => Carbon::now()->subYear(3),
        'ended_at' => Carbon::now()->subYear(2),
        'verified' => true,
        'applicant_id' => function () {
            return factory(Applicant::class)->create()->id;
        },
    ];
});

$factory->define(Minor::class, function (Faker\Generator $faker) {
    return [
        'amount' => 2,
        'verified' => true,
        'applicant_id' => function () {
            return factory(Applicant::class)->create()->id;
        },
    ];
});

$factory->define(Language::class, function (Faker\Generator $faker) {
    return [
        'name' => 'Αγγλικά',
        'code' => 'en',
    ];
});

$factory->define(LanguageLevel::class, function (Faker\Generator $faker) {
    return [
        'name' => 'Γ1',
    ];
});

/*
|--------------------------------------------------------------------------
| Application related models
|--------------------------------------------------------------------------
|
*/
$factory->define(Application::class, function (Faker\Generator $faker) { //TODO
    return [
        'applicant_id' => function () {
            return factory(Applicant::class)->create()->id;
        },
        'contest_id' => function () {
            return factory(Contest::class)->create()->id;
        },
        'protocol_number' => 'YPPOA/GDDYHD/DHD/TPS/000001/00001/001/002',
        'protocol_date' => Carbon::now()->addWeek(2),
        'healthy' => true,
        'unrestricted' => false,
        'invalidated' => false,
        'impediment_eight_months' => false,
        'invalidation_description' => '',
        'name' => 'John',
        'surname' => 'Doe',
        'fathername' => 'Jim',
        'mothername' => 'Jane',
        'birthdate' => Carbon::now()->subYear(30),
        'eu_citizen' => true,
        'greek_nationality' => true,
        'policeid_number' => 'ΧΧ123456',
        'policeid_date' => Carbon::now()->subYear(12),
        'afm' => '**********',
        'doy' => 'AA Athens',
        'amka' => '99999999999',
        'street' => 'Example str',
        'street_number' => '22',
        'postcode' => '10682',
        'city' => 'Athens',
        'phonenumber1' => '**********',
        'phonenumber2' => '**********',
        'email' => '<EMAIL>',
        'submitted_at' => Carbon::now(),
        'locked_at' => null,
        'rated_at' => null,
        'rejected' => null,
    ];
});

$factory->define(Evaluation::class, function (Faker\Generator $faker) {
    return [
        'qualification_id' => '',
        'position_id' => '',
        'requirement_id' => '',
        'relevant' => '',
        'auxiliary_level' => '',
        'points' => '',
    ];
});
