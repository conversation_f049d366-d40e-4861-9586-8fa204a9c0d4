services:
  web:
    image: culturegr/apptree/web:dev
    build:
      context: ./
      dockerfile: ./docker/web/Dockerfile
      target: base-build
      args:
        NGINX_VERSION: ${DOCKER_NGINX_VERSION}
        NODE_VERSION: ${DOCKER_NODE_VERSION}
    ports:
      - ${DOCKER_APP_PORT}:80
    volumes:
      - ./public/:/var/www/html/public/
      - ./docker/web/nginx_template_local.conf:/etc/nginx/conf.d/default.conf
      - ./storage/app/public/:/var/www/html/storage/app/public
    networks:
      - frontend

  app:
    image: culturegr/apptree/app:dev
    build:
      context: ./
      dockerfile: ./docker/app/Dockerfile
      target: development-build
      args:
        PHP_VERSION: ${DOCKER_PHP_VERSION}
        COMPOSER_VERSION: ${DOCKER_COMPOSER_VERSION}
        NODE_VERSION: ${DOCKER_NODE_VERSION}
        MYSQL_VERSION: ${DOCKER_MYSQL_VERSION}
    environment:
      APP_ENV: local
    depends_on:
      - web
    volumes:
      - ./:/var/www/html
      - ./docker/app/php.ini-development:/usr/local/etc/php/php.ini
    networks:
      - frontend
      - backend

  node:
    image: "node:${DOCKER_NODE_VERSION}"
    user: node
    volumes:
      - ./:/home/<USER>/app
    networks:
      - backend

  app_xdebug:
    image: culturegr/apptree/app:dev-xdebug
    build:
      context: ./
      dockerfile: ./docker/app/Dockerfile
      target: development-build-xdebug
      args:
        PHP_VERSION: ${DOCKER_PHP_VERSION}
        COMPOSER_VERSION: ${DOCKER_COMPOSER_VERSION}
        NODE_VERSION: ${DOCKER_NODE_VERSION}
        MYSQL_VERSION: ${DOCKER_MYSQL_VERSION}
    environment:
      APP_ENV: local
    volumes:
      - ./:/var/www/html
      - ./docker/app/php.ini-development:/usr/local/etc/php/php.ini
      - ./docker/app/xdebug.ini:/usr/local/etc/php/conf.d/xdebug.ini
    networks:
      - frontend
      - backend

  db_testing:
    image: "mysql:${DOCKER_MYSQL_VERSION}"
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD}
      MYSQL_DATABASE: ${DB_DATABASE_MAIN}
      MYSQL_USER: ${DB_USERNAME}
      MYSQL_PASSWORD: ${DB_PASSWORD}
    ports:
      - "3310:3306"
    networks:
      - backend
    volumes:
      - ./docker/db/conf.d:/etc/mysql/conf.d
      - apptree-db-testing-data:/var/lib/mysql

volumes:
  apptree-db-testing-data:
    name: apptree-db-testing-data

networks:
  frontend:
    name: apptree-frontend-network
    driver: bridge
  backend:
    name: apptree-backend-network
    driver: bridge
    # **NOTE**
    # In _Linux_ systems, when you set DB_HOST=127.0.0.1 or DB_HOST=localhost in a Dockerized environment,
    # it refers to the localhost of the container itself, not your host machine. To connect to the host machine's
    # MySQL server, you need to use the host machine's private IP address, or use docker networks.
    # In this case, we use the docker network to connect the app container to the db container.
    # In _Windows_ and Mac systems, you have the option to use host.docker.internal to connect to the host machine's
    # MySQL server without the need to use docker networks.
