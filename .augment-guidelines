# General Guidelines:
- Focus strictly on the requested task without adding unrequested features or explanations
- Confirm understanding before implementation and ask clarifying questions when needed
# Coding Principles
- Apply SOLID only when it boosts maintainability/testability. Enforce encapsulation to protect state and set clear module boundaries.
- Keep It Simple (KISS) to avoid over-complicating. Structure code in separable units for easy testing and break complex logic into focused pure functions.

# Tech Stack & Tools:
- PHP 8.4+ and Laravel 12.x (always use php artisan for generating controllers, routes, models, etc.)
- Use Carbon\Carbon for date handling.
- Vue 2.7 with script tags, TypeScript, and Composition API.
- Tailwind CSS for styling.
<!-- - When modifying Vue or TypeScript files, check for ESLint errors and automatically fix all autofixable issues (e.g., run npx eslint --fix [filepath]) -->

# Code Documentation
- Write self-explanatory code: omit redundant doc comments when type hints exist
- Add comments only to explain intent or complex logic, not obvious functionality

# Code style and naming Conventions
- Use descriptive names for variables, functions, and classes that reveal purpose
- The maximum number of attributes per line for Vue templates and Laravel views is 1 attribute per line.

# Laravel CRUD Architecture:
- DTOs: Immutable objects with fromArray(), fromModel(), and toArray().
- Form Requests: One per operation (e.g., CreateXRequest), including a toDto() method.
- Action Classes: One per operation (e.g., CreateXAction) with a handle() method that accepts a DTO, containing business logic (inject sub-actions via the constructor).
- Presenters: Context-specific (e.g., IndexPresenter, ShowPresenter, FormPresenter) handling UI and permissions.
- Controllers: Inject Form Requests, Action Classes, and Presenters to handle only HTTP concerns.