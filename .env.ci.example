#########################
# GENERAL
#########################

APP_ENV=staging
APP_DEBUG=true
APP_NAME=apptree
APP_KEY=
APP_URL=https://apptree.culture.gr

#########################
# DOCKER
#########################

DOCKER_NGINX_VERSION=1.27
DOCKER_PHP_VERSION=8.4
DOCKER_COMPOSER_VERSION=2.8
DOCKER_NODE_VERSION=22.4
DOCKER_REDIS_VERSION=7.2
DOCKER_MYSQL_VERSION=8.0

#########################
# PATHS
#########################

WKHTMLTOPDF=/var/www/html/vendor/bin/wkhtmltopdf-amd64
WKHTMLTOIMAGE=/var/www/html/vendor/bin/wkhtmltoimage-amd64
# Font path is used by dompdf and must point to the asset inside the docker container
FONT_PATH=/var/www/html/public/fonts/

#########################
# DATABASES
#########################

DB_HOST=************
DB_HOST_SOX="${DB_HOST}"
DB_PORT=3306
DB_ROOT_PASSWORD=
DB_USERNAME=
DB_PASSWORD=

DB_CONNECTION_MAIN=mysql_main
DB_CONNECTION_PHONEBOOK=mysql_phonebook
DB_CONNECTION_PERSONNEL=mysql_personnel
DB_CONNECTION_OPENDATA=mysql_opendata
DB_CONNECTION_CONTRACTUALS=mysql_contractuals
DB_CONNECTION_CONSERVATIONS=mysql_conservations
DB_CONNECTION_REGISTRY=mysql_registry
DB_CONNECTION_EDUCATIONAL=mysql_educational
DB_CONNECTION_PUBLIC_CONTRACTUALS=mysql_public_contractuals
DB_CONNECTION_SUMMER_CAMPS=mysql_summer_camps
DB_CONNECTION_ASSETS=mysql_assets

DB_DATABASE_MAIN=laravel
DB_DATABASE_PHONEBOOK=phonebook
DB_DATABASE_PERSONNEL=personnel
DB_DATABASE_OPENDATA=opendata
DB_DATABASE_CONTRACTUALS=contractuals
DB_DATABASE_CONSERVATIONS=conservations
DB_DATABASE_REGISTRY=registry
DB_DATABASE_EDUCATIONAL=educational
DB_DATABASE_PUBLIC_CONTRACTUALS=public_contractuals
DB_DATABASE_SUMMER_CAMPS=summer_camps
DB_DATABASE_ASSETS=assets

#########################
# DBSYNC
#########################
DBSYNC_REMOTE_HOST=***********
DBSYNC_REMOTE_PORT=3306
DBSYNC_REMOTE_USER=staging
DBSYNC_REMOTE_PASSWORD='Pvt68zhkW2M!'
DBSYNC_LOCAL_HOST=${DB_HOST}
DBSYNC_LOCAL_PORT=${DB_PORT}
DBSYNC_LOCAL_USER=${DB_USERNAME}
DBSYNC_LOCAL_PASSWORD=${DB_PASSWORD}

#########################
# LDAP SERVER
#########################

LDAP_LOGGING=false
LDAP_CONNECTION=default
LDAP_HOST=dc01.culture.gr
LDAP_USERNAME=
LDAP_PASSWORD=
LDAP_PORT=389
LDAP_BASE_DN="dc=culture,dc=gr"
LDAP_TIMEOUT=10
LDAP_SSL=false
LDAP_TLS=false

########################
# CACHE DRIVER
########################

CACHE_DRIVER=file
SESSION_DRIVER=file
QUEUE_DRIVER=redis

########################
# REDIS DRIVER
########################

REDIS_HOST=redis
REDIS_PASSWORD=admin
REDIS_PORT=6379
REDIS_DB=0

########################
# MAIL SERVER
########################

MAIL_MAILER=smtp
MAIL_HOST=***********
MAIL_PORT=25
MAIL_USERNAME=dhd.dev
MAIL_PASSWORD=
MAIL_ENCRYPTION=false
MAIL_FROM_ADDRESS='<EMAIL>'
MAIL_FROM_NAME="apptree"

########################
# LOGGER
########################

ACTIVITY_LOGGER_ENABLED=true

########################
# LARAVEL PASSPORT
########################

PASSPORT_PERSONAL_ACCESS_CLIENT_ID=
PASSPORT_PERSONAL_ACCESS_CLIENT_SECRET=

########################
# MISC
########################

CHRISTMAS_MODE=false

#########################
# BUGSNAG
#########################

BUGSNAG_API_KEY=
BUGSNAG_LOGGER_LEVEL=error
BUGSNAG_NOTIFY_RELEASE_STAGES=staging

#########################
# Public Contractuals Integration
#########################
PUBLIC_CONTRACTUALS_URL=http://testsrv.culture.gr:8081
PUBLIC_CONTRACTUALS_API_KEY=

#########################
# PUSHER
#########################

BROADCAST_DRIVER=pusher
PUSHER_APP_ID=local
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

LARAVEL_WEBSOCKETS_HOST=127.0.0.1
LARAVEL_WEBSOCKETS_PORT=6001
LARAVEL_WEBSOCKETS_SSL_LOCAL_CERT=/etc/ssl/websockets/certs/culture.gr.crt
LARAVEL_WEBSOCKETS_SSL_LOCAL_PK=/etc/ssl/websockets/culture.gr.key
LARAVEL_WEBSOCKETS_SCHEME=https

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"
MIX_LARAVEL_WEBSOCKETS_PORT="${LARAVEL_WEBSOCKETS_PORT}"
MIX_LARAVEL_WEBSOCKETS_FORCETLS=true
