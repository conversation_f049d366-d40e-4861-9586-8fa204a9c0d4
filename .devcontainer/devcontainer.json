// For format details, see https://aka.ms/devcontainer.json. For config options, see the
// README at: https://github.com/devcontainers/templates/tree/main/src/docker-existing-docker-compose
{
	"name": "apptree",

	// Update the 'dockerComposeFile' list if you have more compose files or use different names.
	// The .devcontainer/docker-compose.yml file contains any overrides you need/want to make.
	"dockerComposeFile": [
		"../docker-compose.yml",
		"../docker-compose.external-networks.yml",
		"docker-compose.yml"
	],

	// The 'service' property is the name of the service for the container that VS Code should
	// use. Update this value and .devcontainer/docker-compose.yml to the real service name.
	"service": "app",

	// The optional 'workspaceFolder' property is the path VS Code should open by default when
	// connected. This is typically a file mount in .devcontainer/docker-compose.yml
	"workspaceFolder": "/workspaces/${localWorkspaceFolderBasename}",
	"features": {
		"ghcr.io/devcontainers/features/git:1": {},
		"ghcr.io/va-h/devcontainers-features/uv:1": {},
		"ghcr.io/devcontainers/features/node:1": {}
	},
	"customizations": {
		"vscode": {
			"extensions": [
				"GitHub.copilot",
				"GitHub.copilot-chat",
				"laravel.vscode-laravel",
				"bmewburn.vscode-intelephense-client",
				"bradlc.vscode-tailwindcss",
				"alfredbirk.tailwind-documentation",
				"humao.rest-client",
				"dbaeumer.vscode-eslint",
				"mhutchie.git-graph",
				"maattdd.gitless",
				"seatonjiang.gitmoji-vscode",
				"junstyle.php-cs-fixer",
				"austenc.tailwind-docs",
				"cweijan.vetur-plus",
				"open-southeners.laravel-pint",
				"DEVSENSE.composer-php-vscode"
			]
		}
	},

	// Features to add to the dev container. More info: https://containers.dev/features.
	// "features": {},

	// Use 'forwardPorts' to make a list of ports inside the container available locally.
	// "forwardPorts": [8081],

	// Uncomment the next line if you want start specific services in your Docker Compose config.
	"runServices": ["web"],

	// Uncomment the next line if you want to keep your containers running after VS Code shuts down.
	// "shutdownAction": "none",

	// Uncomment the next line to run commands after the container is created.
	// "postCreateCommand": "cat /etc/os-release",

	// Configure tool-specific properties.
	// "customizations": {},

	// Uncomment to connect as an existing user other than the container default. More info: https://aka.ms/dev-containers-non-root.
	// "remoteUser": "devcontainer"

	"mounts": [
    "source=${env:SSH_AUTH_SOCK},target=/ssh-agent,type=bind",
		"source=${env:HOME}/.ssh,target=/home/<USER>/.ssh,type=bind,consistency=cached"

  ],
  "containerEnv": {
    "SSH_AUTH_SOCK": "/ssh-agent",
		"HOME": "/home/<USER>"
  }
}
