<?php

namespace App\Http\Middleware\Contractuals;

use Closure;
use Illuminate\Support\Facades\Cache;

class CheckNoCalculationRunning
{
    /**
     * Run the request filter.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        if (Cache::has('calculating_contest')) {
            abort(503);
        }

        return $next($request);
    }

}
