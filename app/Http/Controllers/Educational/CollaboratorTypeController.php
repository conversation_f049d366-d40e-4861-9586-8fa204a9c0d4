<?php

namespace App\Http\Controllers\Educational;

use App\Http\Controllers\ReadyFormController;
use App\Models\Educational\CollaboratorType;

class CollaboratorTypeController extends ReadyFormController
{
    protected array $indexFields = [
        'name',
    ];

    protected array $formFields = [
        ['name' => 'name', 'label' => 'Όνομα', 'type' => 'text'],
    ];

    protected string $formTitle = 'Κατηγορίες Συνεργαζόμενων Φορέων';

    protected array $validationRules = [
        'name' => 'required|min:3',
    ];

    protected string $bladeLayout = 'layouts.educational';

    protected array $permissions = [
        'index'  => 'educational.admin',
        'create' => 'educational.admin',
        'read'   => 'educational.admin',
        'update' => 'educational.admin',
        'delete' => 'educational.admin',
    ];

    public function __construct(CollaboratorType $collaboratorType)
    {
        parent::__construct($collaboratorType);
    }
}
