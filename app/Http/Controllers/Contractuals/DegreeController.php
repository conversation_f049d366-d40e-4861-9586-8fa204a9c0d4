<?php

namespace App\Http\Controllers\Contractuals;

use App\Http\Controllers\Controller;
use App\Models\Contractuals\Application;
use App\Models\Contractuals\Contest;
use App\Models\Contractuals\Degree;

class DegreeController extends Controller
{
    public function store(Contest $contest, Application $application)
    {
        $validated = request()->validate([
            'name' => 'required|string',
            'is_primary' => 'required|boolean',
            'mark' => 'required|numeric',
            'year' => 'required|numeric',
        ]);

        $degree = $application->degrees()->create($validated);

        return response()->json([
            'message' => 'Degree created successfully',
            'data' => $degree,
        ]);
    }

    public function update(Contest $contest, Application $application, Degree $degree)
    {
        $validated = request()->validate([
            'name' => 'required|string',
            'is_primary' => 'required|boolean',
            'mark' => 'required|numeric',
            'year' => 'required|numeric',
        ]);

        $degree->update($validated);

        return response()->json([
            'message' => 'Degree updated successfully',
            'data' => $degree,
        ]);
    }

    public function destroy(Contest $contest, Application $application, Degree $degree)
    {
        $degree->delete();
        $application->degrees()->detach($degree->id);

        return response()->json([
            'message' => 'Degree deleted successfully',
        ]);
    }
}
