<?php

namespace App\Http\Controllers\Contractuals;

use App\Http\Controllers\ReadyFormController;
use App\Models\Contractuals\Requirement;

class RequirementController extends ReadyFormController
{
    protected array $permissions
        = [
            'index'  => 'contractuals.admin',
            'create' => 'contractuals.admin',
            'read'   => 'contractuals.admin',
            'update' => 'contractuals.admin',
            'delete' => 'contractuals.admin',
        ];

    protected array $formFields = [
        ['name' => 'requirement_type_id', 'label' => 'Είδος Προσόντος', 'type' => 'select', 'relationship' => 'requirementType'],
        ['name' => 'name', 'label' => 'Προσόν', 'type' => 'text'],
        ['name' => 'rejection_description', 'label' => 'Λόγος Απόρριψης', 'type' => 'text'],
    ];

    protected array $indexFields = ['requirement_type_id', 'name'];

    protected string $formTitle = 'Ειδικά Προσόντα';

    protected bool $withTrashed = true;

    protected array $validationRules = [
        'requirement_type_id' => 'required',
        'name' => 'required|min:3',
    ];

    protected string $bladeLayout = 'layouts.contractuals';

    protected array $relationships = ['requirementType'];

    public function __construct(Requirement $requirement)
    {
        $this->middleware('permission:contractuals.admin');

        parent::__construct($requirement);
    }
}
