<?php

namespace App\Http\Controllers\Personnel;

use App\Http\Controllers\Controller;
use App\Models\Personnel\Employee;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redirect;

/**
 * @resource Personnel\Employee
 */
class EmployeeController extends Controller
{
    /**
     * Employee searchform
     * Shows a form for finding an employee.
     */
    public function index()
    {
        return view('personnel/employee/index');
    }

    /**
     * Show Employee
     * Display an employee's details based of searh form.
     */
    public function searchresult(Request $request)
    {
        if (! $request['employee']) {
            flash()->error('Σφάλμα!', 'Δεν επιλέξατε υπάλληλο.');

            return Redirect::back();
        }

        $employee = Employee::find($request['employee']);
        // Eager load user roles
        $employee->load('specialization');
        $employee->load('specialization.occupation');
        $employee->load('occupation');
        $employee->load('ranks');
        $employee->load('telephones');

        return view('personnel/employee/searchresult')->with('employee', $employee);
    }

    /**
     * Ajax search Employee
     * Search for Employee based on keypresses on select2 form input.
     */
    public function search(Request $request)
    {
        $term = $request['q'];

        if ($term) {
            $term = '%'.$term.'%';

            $emloyees = Employee::with('specialization')->where('compass_id', 'like', $term)->orWhere('fullname', 'like', $term)->get();

            $list = [];

            foreach ($emloyees as $employee) {
                $employeeText = $employee->fullname.' '.$employee->compass_id.' '.$employee->specialization->name;
                $list[] = ['text' => $employeeText, 'id' => $employee->id];
            }

            return $list;
        } else {
            return $term;
        }
    }
}
