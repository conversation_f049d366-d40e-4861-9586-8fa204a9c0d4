<?php

namespace App\Services\Contractuals\QualificationRaters\Sox4765;

use App\Services\Contractuals\QualificationRaters\SoxQualificationsRater;

/**
 * Class DegreesSoxQualificationsRater.
 *
 * ΤΡΟΠΟΣ ΒΑΘΜΟΛΟΓΙΣΗΣ:
 * ΠΕ, ΤΕ: οι μονάδες του βασικού τίτλου με 2 δεκαδικά πολλαπλασιάζονται με το 40
 * ΔΕ: οι μονάδες του βασικού τίτλου με 2 δεκαδικά πολλαπλασιάζονται με το 20
 */
class DoctorateQualificationsRater implements SoxQualificationsRater
{
    use HasRatings;

    public function rate($qualifications, $applicantCategory, $auxiliaryLevel): void
    {
        $eligibleQualification = $qualifications->first();

        if ($eligibleQualification === null) {

            return;
        }

        $points = 0;
        $additionalPoints = 0;

        switch ($applicantCategory) {
            case 1: // PE
            case 2: // TE
                $points = 150;

                $eligibleQualification->update(['points' => $points]);

                break;
            case 3: // DE
                $points = 0;

                $eligibleQualification->update(['points' => $points]);

                break;
            case 4: // YE
                $points = 0;

                $eligibleQualification->update(['points' => $points]);

                break;
        }


        $this->ratings['doctorates'] = $points > 0 ? $qualifications->count() : 0;
        $this->ratings['doctorates_points'] = $points;

        if ($applicantCategory == 1 || $applicantCategory == 2) {
            $additionalQualification = $qualifications->first(function ($qualification) use ($eligibleQualification) {
                return $eligibleQualification->id !== $qualification->id;
            });

            if ($additionalQualification) {
                $additionalPoints = $eligibleQualification->points / 2;
                $additionalQualification->update(['points' => $additionalPoints]);

                $this->ratings['other_degrees'] = $additionalPoints;
                $this->ratings['other_degrees_points'] = $additionalPoints;
            }
        }

        $this->points = $points + $additionalPoints;
    }
}
