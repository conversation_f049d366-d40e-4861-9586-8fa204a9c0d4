<?php

namespace App\Services\Contractuals;

use App\Models\Contractuals\Application;
use App\Models\Contractuals\Disability;
use App\Models\Contractuals\Experience;
use App\Models\Contractuals\FamilyDisability;
use App\Models\Contractuals\Minor;
use App\Models\Contractuals\MultiChildFamily;
use App\Models\Contractuals\SingleParentFamily;
use App\Models\Contractuals\ThreeChildFamily;
use App\Models\Contractuals\Unemployment;
use DB;
use Illuminate\Support\Arr;

class ApplicationFormService
{
    public function __construct(
        protected ApplicantFormService $applicantFormService
    ) {}

    public function update(Application $application, array $applicationFormData): Application
    {
//        if ($application->isLocked()) {
//            throw new \Exception('Application cannot be updated because it is locked');
//        }

        DB::connection('mysql_contractuals')->beginTransaction();

        try {
            $application->update(Arr::except($applicationFormData, [
                'experience',
                'unemployment',
                'multiChildFamily',
                'threeChildFamily',
                'singleParentFamily',
                'minors',
                'disability',
                'familyDisability',
            ]));

//            $application->experiences()->find($applicationFormData['experience']['id'])->update(Arr::except($applicationFormData['experience'], ['id']));

            Experience::find($applicationFormData['experience']['id'])->update(Arr::except($applicationFormData['experience'], ['id']));
            Unemployment::find($applicationFormData['unemployment']['id'])->update(Arr::except($applicationFormData['unemployment'], ['id']));
            MultiChildFamily::find($applicationFormData['multiChildFamily']['id'])->update(Arr::except($applicationFormData['multiChildFamily'], ['id']));
            ThreeChildFamily::find($applicationFormData['threeChildFamily']['id'])->update(Arr::except($applicationFormData['threeChildFamily'], ['id']));
            SingleParentFamily::find($applicationFormData['singleParentFamily']['id'])->update(Arr::except($applicationFormData['singleParentFamily'], ['id']));
            Minor::find($applicationFormData['minors']['id'])->update(Arr::except($applicationFormData['minors'], ['id']));
            Disability::find($applicationFormData['disability']['id'])->update(Arr::except($applicationFormData['disability'], ['id']));
            FamilyDisability::find($applicationFormData['familyDisability']['id'])->update(Arr::except($applicationFormData['familyDisability'], ['id']));

            // Renew updated_at as we may have added/removed qualifications.
            $application->touch();

            DB::connection('mysql_contractuals')->commit();
        } catch (\Throwable $e) {
            DB::connection('mysql_contractuals')->rollBack();
            throw $e;
        }

        return $application;
    }
}
