<?php

namespace App\Presenters\Educational;

use CultureGr\Presenter\Presenter;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class ActionShowPresenter extends Presenter
{
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'user' => $this->user?->username ?? '',
            'period' => $this->period?->name ?? '',
            'unit' => $this->unit->name,
            'unit_abbrv' => $this->unit->abbrv,
            'title' => $this->title,
            'type' => $this->type->name,
            'involvements' => $this->involvements->pluck('name'),
            'ongoing' => $this->ongoing ? 'Ναι' : 'Όχι',
            'is_digital' => match ($this->is_digital) {
                false => 'Μη ψηφιακή',
                true => 'Ψηφιακή',
                default => ''
            },
            'context' => $this->context ? $this->context->name : '',
            'started_at' => $this->started_at ? $this->started_at->format('d/m/Y') : '',
            'ended_at' => $this->ended_at ? $this->ended_at->format('d/m/Y') : '',
            'duration' => $this->duration ? $this->duration->name : '',
            'frequency' => $this->frequency ?? '',
            'description' => $this->description,
            'contributors' => $this->contributors,
            'location_types' => $this->locations->groupBy('location_type_id')
                ->map(function ($locationGroup) {
                    return [
                        'name' => $locationGroup->map(function ($location) {
                            return $location['locationType']['name'];
                        })->unique()->first(),
                        'locations' => $locationGroup->pluck('name'),
                    ];
                })->values(),
            'target_types' => $this->targets->groupBy('target_type_id')
                ->map(function ($targetGroup) {
                    return [
                        'name' => $targetGroup->map(function ($target) {
                            return $target['targetType']['name'];
                        })->unique()->first(),
                        'targets' => $targetGroup->pluck('name'),
                        'amount' => $this->targetTypes->filter(function ($targetTypes) use ($targetGroup) {
                            return $targetTypes['participants']['target_type_id'] === $targetGroup->pluck('target_type_id')->first();
                        })->first()['participants']['amount'],
                    ];
                })->values(),
            'totalNumberOfParticipants' => $this->targetTypes->map(function ($targetType) {
                return $targetType->participants->amount;
            })->sum(),
            'collaborator_types' => $this->collaborators->groupBy('collaborator_type_id')
                ->map(function ($collaboratorGroup) {
                    return [
                        'name' => $collaboratorGroup->map(function ($collaborator) {
                            return $collaborator['collaboratorType']['name'];
                        })->unique()->first(),
                        'collaborators' => $collaboratorGroup->pluck('name'),
                    ];
                })->values(),
            'funds' => $this->funds->pluck('name'),
            'tools' => $this->tools->pluck('name'),
            'assessments' => $this->assessments->pluck('name'),
            'disseminations' => $this->disseminations->pluck('name'),
            'link' => $this->link,
            'evaluation' => $this->evaluation,
            'attachments' => $this->model->getMedia('actions')
                ->map(function (Media $file) {
                    return [
                        'id' => $file->id,
                        'name' => $file->name.'.'.$file->getCustomProperty('extension'),
                        'url' => $file->getUrl(),
                    ];
                }),
            'submitted_at' => $this->submitted_at ? $this->submitted_at->format('d/m/Y H:i') : '',
            'can' => [
                'update' => auth()->user()->can('educational.update'),
                'admin' => auth()->user()->can('educational.admin'),
            ],
        ];
    }
}
