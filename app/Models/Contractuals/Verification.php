<?php

namespace App\Models\Contractuals;

use App\Models\Traits\LogsAttributeActivity;
use DateTimeInterface;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Verification extends Model
{
    use SoftDeletes, LogsAttributeActivity;

    protected $connection = 'mysql_contractuals';

    protected $fillable = [
        'description',
        'protocol_date',
        'protocol_number',
        'applicant_id',
        'verifiable_id',
        'verifiable_type',
        'user_id',
    ];

    protected $casts = [
        'protocol_date' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected $hidden = ['created_at', 'updated_at', 'deleted_at'];

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format('Y-m-d');
    }

    public function applicant()
    {
        return $this->belongsTo(Applicant::class);
    }

    public function verifiable()
    {
        return $this->morphTo();
    }
}
