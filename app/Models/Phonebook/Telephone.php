<?php

namespace App\Models\Phonebook;

use App\Models\Personnel\Employee;
use App\Models\Traits\LogsAttributeActivity;
use App\Models\Unit;
use Illuminate\Database\Eloquent\Model;

class Telephone extends Model
{
    use LogsAttributeActivity;

    protected $connection = 'mysql_phonebook';

    protected $fillable = [
        'tel',
        'description',
        'info',
        'directorate_id',
        'type_id',
        'work',
        'unit_id',
    ];

    public function scopeOfService($query)
    {
        return $query->whereNull('employee_id');
    }

    public function scopeOfEmployee($query)
    {
        return $query->whereNotNull('employee_id');
    }

    public function scopeWorkTelephones($query)
    {
        return $query->ofEmployee()->where('work', 1);
    }

    public function scopePersonalTelephones($query)
    {
        return $query->ofEmployee()->where('work', '!=', 1);
    }

    public function scopeOfUnit($query, int $unitId)
    {
        return $query->where('unit_id', '=', $unitId);
    }

    public function type()
    {
        return $this->belongsTo(Type::class);
    }

    public function employee()
    {
        return $this->belongsTo(Employee::class);
    }

    public function unit()
    {
        return $this->belongsTo(Unit::class);
    }
}
