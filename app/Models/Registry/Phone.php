<?php

namespace App\Models\Registry;

use App\Models\Registry\Traits\ValidityRange;
use App\Models\Traits\HasTenants;
use App\Models\Traits\LogsAttributeActivity;
use App\Models\Unit;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;

class Phone extends Model
{
    use LogsAttributeActivity, HasTenants, ValidityRange;

    protected $connection = 'mysql_registry';

    protected $fillable = [
        'number',
        'building_id',
        'phone_provider_id',
        'supply_number',
        'has_internet',
        'by_syzefxis',
        'paid_centrally',
        'keles_linked',
        'unit_id',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
        'valid_from' => 'datetime',
        'valid_to' => 'datetime',
    ];

    protected static $applicationName = 'registry';

    public static function queryWithFilters($filters)
    {
        $query = static::query();

        $query->when($filters->has('id'), function ($query) use ($filters) {
            return $query->where('id', $filters['id']);
        })
            ->when($filters->has('units'), function ($query) use ($filters) {
                return $query->whereIn('unit_id', Arr::pluck($filters['units'], 'id'));
            })
            ->when(((! $filters->has('show_all') || $filters['show_all'] == false) && ($filters->has('valid_on'))),
                function ($query) use ($filters) {
                    return $query->validOn($filters['valid_on']);
                })
            ->when($filters->has('number'), function ($query) use ($filters) {
                return $query->where('number', 'like', '%'.$filters['number'].'%');
            })
            ->when($filters->has('supply_number'), function ($query) use ($filters) {
                return $query->where('supply_number', 'like', '%'.$filters['supply_number'].'%');
            })
            ->when($filters->has('has_internet'), function ($query) use ($filters) {
                return $query->where('has_internet', $filters['has_internet']);
            })
            ->when($filters->has('by_syzefxis'), function ($query) use ($filters) {
                return $query->where('by_syzefxis', $filters['by_syzefxis']);
            })
            ->when($filters->has('paid_centrally'), function ($query) use ($filters) {
                return $query->where('paid_centrally', $filters['paid_centrally']);
            })
            ->when($filters->has('keles_linked'), function ($query) use ($filters) {
                return $query->where('keles_linked', $filters['keles_linked']);
            });

        return $query;
    }

    public function phoneProvider()
    {
        return $this->belongsTo(PhoneProvider::class);
    }

    public function building()
    {
        return $this->belongsTo(Building::class)->withoutTenants();
    }

    public function unit()
    {
        return $this->belongsTo(Unit::class);
    }
}
