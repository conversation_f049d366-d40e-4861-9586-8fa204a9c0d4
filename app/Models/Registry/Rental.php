<?php

namespace App\Models\Registry;

use App\Models\Registry\Traits\ValidityRange;
use App\Models\Traits\HasTenants;
use App\Models\Traits\LogsAttributeActivity;
use App\Models\Unit;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Arr;

class Rental extends Model
{
    use LogsAttributeActivity, HasTenants, ValidityRange, SoftDeletes;

    protected $connection = 'mysql_registry';

    protected $fillable = [
        'building_id',
        'contract',
        'rental_process_id',
        'monthly_rent',
        'unit_id',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
        'valid_from' => 'datetime',
        'valid_to' => 'datetime',
    ];

    protected static $applicationName = 'registry';

    public static function queryWithFilters($filters)
    {
        $query = static::query();

        $query->when($filters->has('id'), function ($query) use ($filters) {
            return $query->where('id', $filters['id']);
        })
            ->when($filters->has('units'), function ($query) use ($filters) {
                return $query->whereIn('unit_id', Arr::pluck($filters['units'], 'id'));
            })
            ->when(((! $filters->has('show_all') || $filters['show_all'] == false) && ($filters->has('valid_on'))),
                function ($query) use ($filters) {
                    return $query->validOn($filters['valid_on']);
                })
            ->when($filters->has('contract'), function ($query) use ($filters) {
                return $query->where('contract', 'like', '%'.$filters['contract'].'%');
            })
            ->when($filters->has('rental_process_id'), function ($query) use ($filters) {
                return $query->where('rental_process_id', $filters['rental_process_id']);
            });

        return $query;
    }

    public function building()
    {
        return $this->belongsTo(Building::class);
    }

    public function rentalProcess()
    {
        return $this->belongsTo(RentalProcess::class);
    }

    public function unit()
    {
        return $this->belongsTo(Unit::class);
    }
}
