<?php

namespace App\Models\Assets;

use App\Models\Traits\HasTenants;
use App\Models\User;
use CultureGr\Filterer\Filterable;
use CultureGr\Presenter\Presentable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Number;

class Asset extends Model
{
    use HasFactory, Filterable, Presentable, HasTenants;

    protected static $applicationName = 'assets';

    protected static function newFactory(): Factory
    {
        return \Database\Factories\Assets\AssetFactory::new();
    }

    protected $connection = 'mysql_assets';

    protected $fillable = [
        'contract_id',
        'asset_category_id',
        'serial_number',
        'quantity',
        'date_of_receipt',
        'location',
        'acquisition_cost_in_cents',
        'user_id',
        'unit_id',
    ];

    protected $filterable = [
        'serial_number',
        'quantity',
        'date_of_receipt',
        'location',
        'acquisition_cost_in_cents',
    ];

    protected $sortable = [
        'serial_number',
        'quantity',
        'date_of_receipt',
        'location',
        'acquisition_cost_in_cents',
    ];

    protected $casts = [
        'date_of_receipt' => 'date',
        'quantity' => 'integer',
        'acquisition_cost_in_cents' => 'integer',
        'submitted_at' => 'datetime',
    ];

    protected function acquisitionCostFormatted(): Attribute
    {
        return Attribute::make(
            get: fn () => Number::currency($this->acquisition_cost_in_cents / 100, in: 'EUR'),
        );
    }

    public function contract(): BelongsTo
    {
        return $this->belongsTo(Contract::class);
    }

    public function assetCategory(): BelongsTo
    {
        return $this->belongsTo(AssetCategory::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function unit(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Unit::class);
    }

    /**
     * Submit the asset.
     */
    public function submit(): void
    {
        $this->user_id = Auth::id();
        $this->submitted_at = now();
        $this->save();
    }

    /**
     * Withdraw the asset submission.
     */
    public function withdraw(): void
    {
        $this->user_id = Auth::id();
        $this->submitted_at = null;
        $this->save();
    }

    /**
     * Scope a query to only include submitted assets.
     */
    public function scopeSubmitted(Builder $query): Builder
    {
        return $query->whereNotNull('submitted_at');
    }

    /**
     * Scope a query to only include unsubmitted assets.
     */
    public function scopeUnsubmitted(Builder $query): Builder
    {
        return $query->whereNull('submitted_at');
    }

    /**
     * Scope a query to only include assets of a specific contract.
     */
    public function scopeOfContract(Builder $query, int|Contract $contract): Builder
    {
        $contractId = $contract instanceof Contract ? $contract->id : $contract;
        return $query->where('contract_id', $contractId);
    }
}
