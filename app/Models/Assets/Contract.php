<?php

namespace App\Models\Assets;

use App\Models\Traits\HasTenants;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use CultureGr\Presenter\Presentable;
use CultureGr\Filterer\Filterable;

class Contract extends Model
{
    use HasFactory, Presentable, Filterable, HasTenants;

    protected static $applicationName = 'assets';

    /**
     * Create a new factory instance for the model.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    protected static function newFactory()
    {
        return \Database\Factories\Assets\ContractFactory::new();
    }

    protected $connection = 'mysql_assets';

    protected $fillable = [
        'contract_number',
    ];

    protected $filterable = [
        'contract_number',
    ];

    protected $sortable = [
        'contract_number',
    ];

    /**
     * Get the assets for the contract.
     */
    public function assets(): HasMany
    {
        return $this->hasMany(Asset::class);
    }

    public function unit(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Unit::class);
    }
}
