<?php

namespace App\Policies;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;

trait AuthorizesTenants
{
    public function create(User $user): bool
    {
        return $user->can($this->getPermissionFullname('create'));
    }

    public function createGd(User $user): bool
    {
        return $user->can($this->getPermissionFullname('readGd'));
    }

    public function createAll(User $user): bool
    {
        return $user->can($this->getPermissionFullname('createAll'));
    }

    public function read(User $user, Model $model): bool
    {
        return ($user->can($this->getPermissionFullname('read')) && $user->sameUnit($model)) ||
            ($user->can($this->getPermissionFullname('readGd')) && $user->sameGdirectorate($model)) ||
            ($user->can($this->getPermissionFullname('readAll')));
    }

    public function update(User $user, Model $model): bool
    {
        return ($user->can($this->getPermissionFullname('update')) && $user->sameUnit($model)) ||
            ($user->can($this->getPermissionFullname('updateGd')) && $user->sameGdirectorate($model)) ||
            ($user->can($this->getPermissionFullname('updateAll')));
    }

    public function delete(User $user, Model $model): bool
    {
        return ($user->can($this->getPermissionFullname('delete')) && $user->sameUnit($model)) ||
            ($user->can($this->getPermissionFullname('deleteGd')) && $user->sameGdirectorate($model)) ||
            ($user->can($this->getPermissionFullname('deleteAll')));
    }

    protected function getPermissionFullname($action): string
    {
        return $this->getApplicationName().'.'.$action;
    }

    protected function getApplicationName(): string
    {

        // FIXME: Does not work well with api requests or request from Gate mixin
        return static::$applicationName ?? request()->segment(1);
    }
}
