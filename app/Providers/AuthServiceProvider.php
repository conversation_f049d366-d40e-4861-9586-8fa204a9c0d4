<?php

namespace App\Providers;

use App\Models\Assets\Asset;
use App\Models\Assets\Contract;
use App\Models\Opendata\Dataset;
use App\Models\Permission;
use App\Models\Phonebook\EmployeeContact;
use App\Models\Phonebook\ServiceContact;
use App\Models\Registry\Building;
use App\Models\Registry\Mobile;
use App\Models\Registry\Phone;
use App\Models\Registry\Staff;
use App\Models\Registry\Vehicle;
use App\Models\User;
use App\Policies\Assets\AssetPolicy;
use App\Policies\Assets\ContractPolicy;
use App\Policies\Opendata\DatasetPolicy;
use App\Policies\Phonebook\EmployeeContactPolicy;
use App\Policies\Phonebook\ServiceContactPolicy;
use App\Policies\Registry\BuildingPolicy;
use App\Policies\Registry\MobilePolicy;
use App\Policies\Registry\PhonePolicy;
use App\Policies\Registry\StaffPolicy;
use App\Policies\Registry\VehiclePolicy;
use Auth;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;
use Laravel\Fortify\Fortify;
use Laravel\Passport\Passport;

class AuthServiceProvider extends ServiceProvider
{
    protected $policies = [
        // Opendata app:
        Dataset::class => DatasetPolicy::class,

        // Phonebook app:
        ServiceContact::class => ServiceContactPolicy::class,
        EmployeeContact::class => EmployeeContactPolicy::class,

        // Registry app:
        Building::class => BuildingPolicy::class,
        Mobile::class => MobilePolicy::class,
        Vehicle::class => VehiclePolicy::class,
        Staff::class => StaffPolicy::class,
        Phone::class => PhonePolicy::class,

        // Asset app:
        Asset::class => AssetPolicy::class,
        Contract::class => ContractPolicy::class,
    ];

    public function boot()
    {
        $this->registerPolicies();

        Fortify::authenticateUsing(function ($request) {
            if (env('AUTH_GUARD') === 'web-fallback') {
                // Database credentials
                // Use the database credentials to authenticate only for development
                // environment where the LDAP connection is abscent.
                $credentials = [
                    'username' => $request->username,
                    'password' => $request->password,
                ];
            } else {
                // LDAP credentials
                $credentials = [
                    'samaccountname' => $request->username,
                    'password' => $request->password,
                ];
            }

            $validated = Auth::guard(env('AUTH_GUARD', 'web'))->validate($credentials);

            return $validated ? Auth::getLastAttempted() : null;
        });

        Passport::cookie('apptree_token');

        Gate::before(function ($user) {
            if ($user->isAdmin()) {
                return true;
            }

            if (Auth::guest()) {
                if (request()->ajax() || request()->wantsJson()) {
                    return response('Unauthorized', 401);
                }

                return redirect()->guest('signin');
            }
        });

        collect($this->getPermissions())->each(function ($permission) {
            Gate::define($permission->name, function (User $user) use ($permission) {
                return $user->hasRole($permission->roles);
            });
        });
    }

    protected function getPermissions()
    {
        if (app()->runningInConsole()) {
            return [];
        }

        return Permission::with('roles')->get();
    }
}
