<?php

namespace App\Exports\Contractuals;

use App\Models\Contractuals\Calculation;
use App\Models\Contractuals\Position;
use App\Models\Contractuals\Ranking;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\RegistersEventListeners;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class RankingsExport2190 implements FromQuery, WithMapping, WithHeadings, WithEvents, WithColumnFormatting, ShouldAutoSize
{
    use Exportable, RegistersEventListeners;

    const AUXILIARY_LEVELS = ['1', 'Α', 'B', 'Γ'];

    protected $contestId;
    protected $positionId;
    protected $rejected;
    protected $calculationId;

    public function forPosition(int $position_id)
    {
        $this->positionId = $position_id;

        return $this;
    }

    public function forContest(int $contest_id)
    {
        $this->contestId = $contest_id;

        return $this;
    }

    public function forCalculation(int $calculation_id)
    {
        $this->calculationId = $calculation_id;

        return $this;
    }

    public function accepted()
    {
        $this->rejected = 0;

        return $this;
    }

    public function rejected()
    {
        $this->rejected = 1;

        return $this;
    }

    public function query()
    {
        if (! empty($this->positionId)) {
            return Ranking::query()->where('position_id', $this->positionId)
                ->where('rejected', $this->rejected)
                ->where('calculation_id', $this->calculationId)
                ->orderBy('rank');
        }

        return Ranking::query()->where('contest_id', $this->contestId)
            ->where('rejected', $this->rejected)
            ->where('calculation_id', $this->calculationId)
            ->orderBy('rank');
    }

    public function map($ranking): array
    {
        return [
            $ranking->protocol_number,
            $ranking->surname,
            $ranking->name,
            $ranking->fathername,
            $ranking->policeid_number,
            $ranking->impediment_eight_months ? 'Ναι' : 'Όχι',
            'Όχι', // Εντοποιότητα
            $this::AUXILIARY_LEVELS[$ranking->auxiliary_level],

            $ranking->unemployments,
            $ranking->multi_child_families_children,
            $ranking->multi_child_families_siblings,
            1 == $this->chooseLargest($ranking->three_child_families_children, $ranking->three_child_families_siblings) ? 3 : null,
            $ranking->minors,
            $ranking->single_parent_families_children,
            $ranking->single_parent_families_siblings,
            $ranking->degrees,
            $ranking->experiences,
            $ranking->disabilities,
            $ranking->family_disabilities,

            $ranking->unemployments_points,
            $ranking->multi_child_families_children_points,
            $ranking->multi_child_families_siblings_points,
            $this->chooseLargest($ranking->three_child_families_children_points, $ranking->three_child_families_siblings_points),
            $ranking->minors_points,
            $ranking->single_parent_families_children_points,
            $ranking->single_parent_families_siblings_points,
            $ranking->degrees_points,
            $ranking->experiences_points,
            $ranking->disabilities_points,
            $ranking->family_disabilities_points,

            $ranking->impediment_eight_months ? 'Ναι' : 'Όχι',
            $this::AUXILIARY_LEVELS[$ranking->auxiliary_level],
            'Όχι', // Εντοποιότητα
            $ranking->score,
            $ranking->rank,
            true === $ranking->employable ? 'Προσλαμβάνεται' : '',
        ];
    }

    public function headings(): array
    {
        $position = Position::find($this->positionId);
        $calculation = Calculation::find($this->calculationId);

        return [
            ['ΔΙΑΓΩΝΙΣΜΟΣ: '.$position->contest->name],
            ['ΥΠΗΡΕΣΙΑ: '.$position->unit->name],
            ['ΚΑΤΗΓΟΡΙΑ ΥΠΟΨΗΦΙΩΝ: '.$position->specialization->specializationType->name],
            ['ΚΩΔΙΚΟΣ ΘΕΣΗΣ: '.$position->code],
            ['ΕΙΔΙΚΟΤΗΤΑ: '.$position->specialization->name],
            ['ΗΜΕΡΟΜΗΝΙΑ ΕΚΔΟΣΗΣ: '.$calculation->run_at->format('d/m/Y')],
            [],
            [
                'Α.Μ.',
                'ΕΠΩΝΥΜΟ',
                'ΟΝΟΜΑ',
                'ΟΝΟΜΑ ΠΑΤΡΟΣ',
                'ΑΡΙΘΜ. ΤΑΥΤΟΤ.',
                'ΚΩΛΥΜΑ 8ΜΗΝΗΣ ΑΠΑΣΧΟΛΗΣΗΣ',
                'ΕΝΤΟΠΙΟΤΗΤΑ',
                'ΚΥΡΙΑ ΠΡΟΣΟΝΤΑ(1) / ΣΕΙΡΑ ΕΠΙΚΟΥΡΙΑΣ',
                'ΧΡΟΝΟΣ ΑΝΕΡΓΙΑΣ (σε μήνες) (1)',
                'ΠΟΛΥΤΕΚΝΟΣ (αριθμ. τέκνων) (2)',
                'ΤΕΚΝΟ ΠΟΛΥΤΕΚΝΗΣ ΟΙΚΟΓΕΝΕΙΑΣ (αριθμ. τέκνων) (3)',
                'ΤΡΙΤΕΚΝΟΣ ή ΤΕΚΝΟ ΤΡΙΤΕΚΝΗΣ ΟΙΚΟΓΕΝΕΙΑΣ (αριθμ. τέκνων) (4 ή 5)',
                'ΑΝΗΛΙΚΑ ΤΕΚΝΑ (αριθμ. ανήλικων τέκνων) (6)',
                'ΓΟΝΕΑΣ ΜΟΝΟΓΟΝΕΙΚΗΣ ΟΙΚΟΓΕΝΕΙΑΣ (αριθμ. τέκνων) (7)',
                'ΤΕΚΝΟ ΜΟΝΟΓΟΝΕΙΚΗΣ ΟΙΚΟΓΕΝΕΙΑΣ  (αρ. τέκνων) (8)',
                'ΒΑΘΜΟΣ ΒΑΣΙΚΟΥ ΤΙΤΛΟΥ (9)',
                'ΕΜΠΕΙΡΙΑ (σε μήνες) (10)',
                'ΑΝΑΠΗΡΙΑ ΥΠΟΨΗΦΙΟΥ (Ποσοστό  Αναπηρίας) (11)',
                'ΑΝΑΠΗΡΙΑ ΓΟΝΕΑ, ΤΕΚΝΟΥ (Ποσοστό  Αναπηρίας) (12)',
                'ΜΟΝΑΔΕΣ (1)',
                'ΜΟΝΑΔΕΣ (2)',
                'ΜΟΝΑΔΕΣ (3)',
                'ΜΟΝΑΔΕΣ (4 ή 5)',
                'ΜΟΝΑΔΕΣ (6)',
                'ΜΟΝΑΔΕΣ (7)',
                'ΜΟΝΑΔΕΣ (8)',
                'ΜΟΝΑΔΕΣ (9)',
                'ΜΟΝΑΔΕΣ (10)',
                'ΜΟΝΑΔΕΣ (11)',
                'ΜΟΝΑΔΕΣ (12)',
                'ΚΩΛΥΜΑ 8ΜΗΝΗΣ ΑΠΑΣΧΟΛΗΣΗΣ',
                'ΚΥΡΙΟΣ η ΕΠΙΚΟΥΡΙΚΟΣ ΠΙΝΑΚΑΣ',
                'ΕΝΤΟΠΙΟΤΗΤΑ',
                'ΣΥΝΟΛΟ ΜΟΝΑΔΩΝ',
                'Σειρά Κατάταξης',
                'Παρατηρήσεις',
            ],
        ];
    }

    /**
     * @return array
     */
    public function columnFormats(): array
    {
        return [
            'P'  => NumberFormat::FORMAT_NUMBER_00,
            'AA' => NumberFormat::FORMAT_NUMBER_00,
            'AH' => NumberFormat::FORMAT_NUMBER_00,
        ];
    }

    public static function afterSheet(AfterSheet $event)
    {
        $styleArrayHeading = [
            'font' => [
                'bold' => true,
            ],
        ];

        $styleArrayHeader = [
            'font' => [
                'bold' => true,
            ],
            'alignment' => [
                'textRotation' => 90,
            ],
        ];

        $event->sheet->getStyle('A1:F6')->applyFromArray($styleArrayHeading);
        $event->sheet->getStyle('A8:AJ8')->applyFromArray($styleArrayHeader);

        // Headers
        $event->sheet->mergeCells('A1:Z1');
        $event->sheet->mergeCells('A2:Z2');
        $event->sheet->mergeCells('A3:Z3');
        $event->sheet->mergeCells('A4:Z4');
        $event->sheet->mergeCells('A5:Z5');
        $event->sheet->mergeCells('A6:Z6');

        // Signature
        $event->sheet->appendRows([
            [''],
            ['', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '',
                'Η Γενική Διευθύντρια', ],
            ['', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '',
                'Αρχαιοτήτων και Πολιτιστικής Κληρονομιάς', ],
            [''],
            ['', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '',
                'Πολυξένη Αδάμ - Βελένη', ],
        ], $event->getSheet());

        $signatureName = $event->sheet->getHighestDataRow();
        $signatureHeader1 = $signatureName - 3;
        $signatureHeader2 = $signatureName - 2;
        $event->sheet->mergeCells("AB$signatureHeader1:AI$signatureHeader1");
        $event->sheet->mergeCells("AB$signatureHeader2:AI$signatureHeader2");
        $event->sheet->mergeCells("AB$signatureName:AI$signatureName");

        $styleSignature = [
            'font' => [
                'bold' => true,
            ],
            'alignment' => [
                'horizontal' => 'center',
            ],
        ];
        $event->sheet->getStyle("AB$signatureHeader1")->applyFromArray($styleSignature);
        $event->sheet->getStyle("AB$signatureHeader2")->applyFromArray($styleSignature);
        $event->sheet->getStyle("AB$signatureName")->applyFromArray($styleSignature);
    }

    protected function chooseLargest($a, $b)
    {
        if ($a >= $b) {
            return $a;
        }

        return $b;
    }
}
