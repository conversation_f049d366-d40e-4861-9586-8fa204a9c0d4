<?php

namespace App\Rules\Educational;

use App\Models\Educational\Action;
use Illuminate\Contracts\Validation\ImplicitRule;
use Illuminate\Support\Str;

class RelatedAttachedToModel implements ImplicitRule
{
    public function __construct(protected Action $action)
    {
    }

    public function passes($attribute, $value)
    {
        // If attribute is a required field, check if related models have been attached.
        if ($this->attributeIsRequiredForThisActionType($attribute)) {
            $relationship = Str::camel(Str::plural($attribute));

            return $this->action->{$relationship}->isNotEmpty();
        }

        // Otherwise, it is not required and pass the validation.
        return true;
    }

    public function message(): string
    {
        return 'Πρέπει να επιλέξετε τουλάχιστον μία κατηγορία για την υποβολή της αίτησης.';
    }

    protected function attributeIsRequiredForThisActionType($attribute): bool
    {
        $types = Action::REQUIRED_FIELDS[$attribute];

        return in_array($this->action->type_id, $types);
    }
}
