{"name": "apptree", "private": true, "scripts": {"dev": "npm run development", "development": "mix", "watch": "mix watch", "watch-poll": "mix watch -- --watch-options-poll=1000", "hot": "mix watch --hot", "prod": "npm run production", "production": "mix --production", "test": "NODE_ENV=test jest"}, "devDependencies": {"@babel/core": "^7.14.5", "@iconify/vue2": "^2.1.0", "@tailwindcss/postcss": "^4.1.4", "@types/lodash": "^4.14.182", "@typescript-eslint/eslint-plugin": "^5.30.5", "@typescript-eslint/parser": "^5.30.5", "@vue-a11y/focus-loop": "^0.2.0", "@vue/test-utils": "^1.1.2", "@vue/tsconfig": "^0.1.3", "axios": "^0.21.4", "babel-core": "^7.0.0-bridge.0", "babel-eslint": "^10.1.0", "babel-jest": "^29.0.3", "chart.js": "^3.7.1", "cross-env": "^7.0.2", "css-loader": "^6.7.1", "datatables.net": "^1.11.3", "datatables.net-autofill": "^2.3.5", "datatables.net-bs": "^1.10.22", "datatables.net-buttons": "^2.2.3", "element-ui": "^2.15.7", "eslint": "^8.23.1", "eslint-config-airbnb-base": "^15.0.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-import-resolver-typescript": "^3.2.4", "eslint-plugin-import": "^2.26.0", "eslint-plugin-simple-import-sort": "^8.0.0", "eslint-plugin-vue": "^9.2.0", "eslint-plugin-vue-scoped-css": "^2.2.0", "eslint-webpack-plugin": "^3.2.0", "file-loader": "^6.2.0", "jest": "^29.0.3", "jquery": "^3.5.1", "laravel-echo": "^1.11.3", "laravel-mix": "^6.0.27", "less": "^3.12.2 || ^4.1.1", "less-loader": "^11.0.0", "lodash": "^4.17.21", "moment": "^2.29.1", "optionator": "^0.9.1", "portal-vue": "^2.1.7", "postcss": "^8.4.47", "pusher-js": "^7.0.3", "qs": "^6.9.4", "sass": "^1.34.1", "sass-loader": "^12.1.0", "tailwindcss": "^4.1.4", "ts-loader": "^9.3.1", "typescript": "^4.7.4", "vue": "^2.7.2", "vue-chartjs": "^4.0.7", "vue-cookies": "^1.8.1", "vue-eslint-parser": "^9.0.3", "vue-good-table": "^2.21.1", "vue-jest": "^3.0.7", "vue-loader": "^15.9.7", "vue-template-compiler": "^2.6.14"}}