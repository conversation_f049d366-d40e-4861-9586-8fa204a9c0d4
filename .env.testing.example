#########################
# GENERAL
#########################
APP_ENV=testing
APP_KEY=base64:95/LUXx8gIM6JOAt6a/jckBnjZheck3n/AzSaebc/Nk=
APP_URL=http://localhost

#########################
# DATABASES
#########################

DB_HOST=db_testing
DB_PORT=3306
DB_ROOT_PASSWORD=supersecret
DB_USERNAME=apptree
DB_PASSWORD=secret

DB_CONNECTION_MAIN=mysql_main
DB_CONNECTION_PHONEBOOK=mysql_phonebook
DB_CONNECTION_PERSONNEL=mysql_personnel
DB_CONNECTION_OPENDATA=mysql_opendata
DB_CONNECTION_CONTRACTUALS=mysql_contractuals
DB_CONNECTION_CONSERVATIONS=mysql_conservations
DB_CONNECTION_REGISTRY=mysql_registry
DB_CONNECTION_EDUCATIONAL=mysql_educational
DB_CONNECTION_SUMMER_CAMPS=mysql_summer_camps

DB_DATABASE_MAIN=laravel
DB_DATABASE_PHONEBOOK=phonebook
DB_DATABASE_PERSONNEL=personnel
DB_DATABASE_OPENDATA=opendata
DB_DATABASE_CONTRACTUALS=contractuals
DB_DATABASE_CONSERVATIONS=conservations
DB_DATABASE_REGISTRY=registry
DB_DATABASE_EDUCATIONAL=educational
DB_DATABASE_SUMMER_CAMPS=summer_camps

#########################
# DOCKER
#########################

DOCKER_DB_PORT=3309
