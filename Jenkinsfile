#!/usr/bin/env groovy

pipeline {
    agent any
    environment {
        PUSHER_APP_KEY=credentials('pusher-app-key')
    }
    stages {
        stage('Build') {
            steps {
                script {
                    if (env.BRANCH_NAME == 'dev') {
                        /*
                        Create the required .env file
                        */
                        sh '''
                            if [ -f .env ]; then rm .env; fi && cp .env.ci.example .env
                        '''

                        /*
                        Build the ci images
                        */
                        sh '''
                            COMMIT_SHA=$(git rev-parse --short HEAD) COMPOSE_DOCKER_CLI_BUILD=1 DOCKER_BUILDKIT=1 \
                                docker compose -f docker-compose.ci.yml  build || 127
                        '''
                    }
                    if (env.BRANCH_NAME == 'main') {
                        /*
                        Create the required .env file
                        */
                        sh '''
                            if [ -f .env ]; then rm .env; fi && cp .env.prod.example .env
                        '''

                        /*
                        Build the production images
                        */
                        sh '''
                            COMMIT_SHA=$(git rev-parse --short HEAD) COMPOSE_DOCKER_CLI_BUILD=1 DOCKER_BUILDKIT=1 \
                                docker compose -f docker-stack.yml  build || 127
                        '''
                    }
                }
            }
        }
        stage('Deploy') {
            steps {
                script {
                    if (env.BRANCH_NAME == 'dev') {
                        /*
                        Push docker images to private registry
                        */
                        sh '''
                            docker compose -f docker-compose.ci.yml push app web
                        '''

                         /*
                         Pass the Compose file to production server
                         */
                        sh '''
                            scp -i ~/.ssh/id_testsrv \
                                docker-compose.ci.yml \
                                <EMAIL>:~/docker/apptree
                        '''

                        /*
                        Deploy the stack
                        */
                        sh '''
                            ssh -i ~/.ssh/id_testsrv \
                                <EMAIL> \
                                "docker stack deploy -c ~/docker/apptree/docker-compose.ci.yml apptree"
                        '''
                    }
                    if (env.BRANCH_NAME == 'main') {
                        sh '''
                            docker compose -f docker-stack.yml push app web
                        '''

                         /*
                         Pass the stack file to production server
                         */
                        sh '''
                            scp -i ~/.ssh/id_apptree \
                                docker-stack.yml \
                                <EMAIL>:~/docker
                        '''

                        /*
                        Deploy the stack
                        */
                        sh '''
                            ssh -i ~/.ssh/id_apptree \
                                <EMAIL> \
                                "docker stack deploy -c ~/docker/docker-stack.yml apptree"
                        '''
                    }
                }
            }
        }
    }
    post {
        success {
            script {
                def targetChannel = (env.BRANCH_NAME == 'main') ? '#deploys' :
                                    (env.BRANCH_NAME == 'dev') ? '#deploys-staging' :
                                    '#devel'
                def commitHashShort = env.GIT_COMMIT.substring(0, 8)
                def repoUrl = env.GIT_URL
                def repoName = repoUrl.split(':')[1].replaceAll('\\.git$', '')
                def title = "Deployment of ${env.BRANCH_NAME} branch successful!"
                def indent = "    "
                def message = """
                    :tada: Job *${env.JOB_NAME}* was successful!\n
                    ${indent}Status: ${currentBuild.currentResult}
                    ${indent}Repo: `$repoName` on `${env.BRANCH_NAME}`
                    ${indent}Commit: `$commitHashShort`
                """.stripIndent()

                slackSend(
                    color: 'good',
                    title: title,
                    message: message,
                    teamDomain: 'culturegr',
                    channel: targetChannel,
                    tokenCredentialId: 'slack-notifier'
                )
            }
        }

        failure {
            script {
                def targetChannel = (env.BRANCH_NAME == 'main') ? '#deploys' :
                                    (env.BRANCH_NAME == 'dev') ? '#deploys-staging' :
                                    '#devel'
                def commitHashShort = env.GIT_COMMIT.substring(0, 8)
                def repoUrl = env.GIT_URL
                def repoName = repoUrl.split(':')[1].replaceAll('\\.git$', '')
                def title = "Deployment of ${env.BRANCH_NAME} branch successful!"
                def indent = "    "
                def message = """
                    :x: Job *${env.JOB_NAME}* has failed\n
                    ${indent}Status: ${currentBuild.currentResult}
                    ${indent}Repo: `$repoName` on `${env.BRANCH_NAME}`
                    ${indent}Commit: `$commitHashShort`
                """.stripIndent()

                slackSend(
                    color: 'danger',
                    title: title,
                    message: message,
                    teamDomain: 'culturegr',
                    channel: targetChannel,
                    tokenCredentialId: 'slack-notifier'
                )
            }
        }
    }
}
